# PC_Control2 关键类和方法快速参考

## 📋 概述
本文档提供了PC_Control2项目中核心类、接口和方法的快速参考，包括主要属性、关键方法、使用示例和最佳实践。

---

## 🎯 核心ViewModel类

### 1. MainWindowViewModel

#### 主要属性
```csharp
public class MainWindowViewModel : ViewModelBase
{
    // 项目管理
    public ProjectModel CurrentProject { get; set; }
    public bool HasUnsavedChanges { get; set; }
    
    // 子ViewModel
    public ProjectTreeViewModel ProjectTreeViewModel { get; }
    public EnhancedSFCViewModel SFCViewModel { get; }
    public BlueprintCanvasViewModel BlueprintViewModel { get; }
    public ToolboxViewModel ToolboxViewModel { get; }
    public PropertyPanelViewModel PropertyPanelViewModel { get; }
    
    // UI状态
    public SFCElementViewModel SelectedElement { get; set; }
    public bool IsLoading { get; set; }
    public string StatusMessage { get; set; }
    
    // 命令
    public ICommand NewProjectCommand { get; }
    public ICommand OpenProjectCommand { get; }
    public ICommand SaveProjectCommand { get; }
    public ICommand ExitCommand { get; }
}
```

#### 关键方法
```csharp
// 项目操作
public async Task<bool> CreateNewProjectAsync(string projectName, string projectPath)
{
    try
    {
        IsLoading = true;
        var project = await _projectService.CreateProjectAsync(projectName, projectPath);
        CurrentProject = project;
        HasUnsavedChanges = false;
        return true;
    }
    catch (Exception ex)
    {
        ShowErrorMessage($"创建项目失败: {ex.Message}");
        return false;
    }
    finally
    {
        IsLoading = false;
    }
}

// 元素选择处理
private void OnElementSelected(object sender, ElementSelectedEventArgs e)
{
    SelectedElement = e.Element;
    PropertyPanelViewModel.SetSelectedElement(e.Element);
    
    // 通知其他ViewModel
    _eventAggregator.Publish(new ElementSelectionChangedEvent(e.Element));
}

// 状态更新
public void UpdateStatus(string message, StatusLevel level = StatusLevel.Info)
{
    StatusMessage = message;
    StatusLevel = level;
    
    // 自动清除状态消息
    Task.Delay(5000).ContinueWith(_ => 
    {
        if (StatusMessage == message)
            StatusMessage = string.Empty;
    });
}
```

#### 使用示例
```csharp
// 在App.xaml.cs中注册
services.AddTransient<MainWindowViewModel>();

// 在MainWindow.xaml.cs中使用
public MainWindow(MainWindowViewModel viewModel)
{
    InitializeComponent();
    DataContext = viewModel;
}

// 在XAML中绑定
<MenuItem Header="新建项目" Command="{Binding NewProjectCommand}"/>
<TextBlock Text="{Binding StatusMessage}" />
```

### 2. EnhancedSFCViewModel

#### 主要属性
```csharp
public class EnhancedSFCViewModel : ViewModelBase
{
    // SFC元素集合
    public ObservableCollection<SFCStepViewModel> Steps { get; }
    public ObservableCollection<SFCTransitionViewModel> Transitions { get; }
    public ObservableCollection<SFCBranchViewModel> Branches { get; }
    public ObservableCollection<SFCConnectionViewModel> Connections { get; }
    
    // 选择和编辑
    public SFCElementViewModel SelectedElement { get; set; }
    public ObservableCollection<SFCElementViewModel> SelectedElements { get; }
    
    // 画布状态
    public double ZoomFactor { get; set; } = 1.0;
    public Point PanOffset { get; set; }
    public bool IsGridVisible { get; set; } = true;
    
    // 验证状态
    public ValidationResult ValidationResult { get; set; }
    public bool HasValidationErrors => ValidationResult?.HasErrors ?? false;
    
    // 命令
    public ICommand InsertStepCommand { get; }
    public ICommand InsertTransitionCommand { get; }
    public ICommand InsertBranchCommand { get; }
    public ICommand DeleteElementCommand { get; }
    public ICommand ValidateSFCCommand { get; }
    public ICommand GenerateCodeCommand { get; }
}
```

#### 关键方法
```csharp
// 元素创建
public SFCStepViewModel CreateStep(Point position, string name = null)
{
    var step = new SFCStepViewModel
    {
        Name = name ?? $"步骤{Steps.Count + 1}",
        Position = position,
        Id = Guid.NewGuid().ToString()
    };
    
    Steps.Add(step);
    RegisterElement(step);
    
    // 触发验证
    ValidateSFC();
    
    return step;
}

// 连接创建
public bool CreateConnection(string sourceElementId, int sourceIndex, 
    string targetElementId, int targetIndex)
{
    var validation = _connectionManager.ValidateConnection(
        sourceElementId, sourceIndex, targetElementId, targetIndex);
    
    if (!validation.IsValid)
    {
        ShowValidationErrors(validation);
        return false;
    }
    
    var connection = _connectionManager.CreateConnection(
        sourceElementId, sourceIndex, targetElementId, targetIndex);
    
    if (connection != null)
    {
        Connections.Add(connection);
        UpdateConnectPointStates();
        return true;
    }
    
    return false;
}

// SFC验证
public async Task ValidateSFCAsync()
{
    try
    {
        var sfcModel = CreateSFCModel();
        ValidationResult = await _sfcValidator.ValidateAsync(sfcModel);
        
        // 更新UI验证状态
        UpdateValidationVisualization();
        
        OnPropertyChanged(nameof(HasValidationErrors));
    }
    catch (Exception ex)
    {
        ValidationResult = new ValidationResult();
        ValidationResult.AddError($"验证过程出错: {ex.Message}");
    }
}

// 代码生成
public async Task<string> GenerateCodeAsync()
{
    if (HasValidationErrors)
    {
        throw new InvalidOperationException("存在验证错误，无法生成代码");
    }
    
    var sfcModel = CreateSFCModel();
    return await _codeGenerator.GenerateSTCodeAsync(sfcModel);
}
```

### 3. BlueprintCanvasViewModel

#### 主要属性
```csharp
public class BlueprintCanvasViewModel : ViewModelBase
{
    // 节点和连接
    public ObservableCollection<BlueprintNodeViewModel> Nodes { get; }
    public ObservableCollection<BlueprintConnectionViewModel> Connections { get; }
    
    // 选择状态
    public BlueprintNodeViewModel SelectedNode { get; set; }
    public ObservableCollection<BlueprintNodeViewModel> SelectedNodes { get; }
    
    // 画布状态
    public double ZoomLevel { get; set; } = 1.0;
    public Point ViewportOffset { get; set; }
    
    // 执行状态
    public bool IsExecuting { get; set; }
    public Guid? CurrentExecutionId { get; set; }
    
    // 命令
    public ICommand AddNodeCommand { get; }
    public ICommand DeleteNodeCommand { get; }
    public ICommand ExecuteBlueprintCommand { get; }
    public ICommand StopExecutionCommand { get; }
}
```

#### 关键方法
```csharp
// 节点创建
public BlueprintNodeViewModel CreateNode(NodeDefinition nodeDefinition, Point position)
{
    var node = new BlueprintNodeViewModel(nodeDefinition)
    {
        Position = position,
        Id = Guid.NewGuid().ToString()
    };
    
    Nodes.Add(node);
    
    // 自动布局引脚
    LayoutNodePins(node);
    
    return node;
}

// 连接创建
public bool CreateConnection(BlueprintPinViewModel sourcePin, BlueprintPinViewModel targetPin)
{
    var validation = _connectionValidator.ValidateConnection(sourcePin.Model, targetPin.Model);
    
    if (!validation.IsValid)
        return false;
    
    var connection = new BlueprintConnectionViewModel
    {
        SourcePin = sourcePin,
        TargetPin = targetPin,
        Id = Guid.NewGuid().ToString()
    };
    
    Connections.Add(connection);
    sourcePin.Connections.Add(connection);
    targetPin.Connections.Add(connection);
    
    return true;
}

// 蓝图执行
public async Task<Guid> ExecuteBlueprintAsync()
{
    if (IsExecuting)
        return Guid.Empty;
    
    try
    {
        IsExecuting = true;
        var blueprint = CreateBlueprintModel();
        CurrentExecutionId = await _executionEngine.StartExecutionAsync(blueprint);
        return CurrentExecutionId.Value;
    }
    catch (Exception ex)
    {
        ShowErrorMessage($"执行失败: {ex.Message}");
        return Guid.Empty;
    }
}
```

---

## 🔧 服务接口定义

### 1. IProjectService

#### 接口定义
```csharp
public interface IProjectService
{
    // 项目操作
    Task<ProjectModel> CreateProjectAsync(string name, string path);
    Task<ProjectModel> LoadProjectAsync(string projectPath);
    Task SaveProjectAsync(ProjectModel project);
    Task<bool> CloseProjectAsync(ProjectModel project);
    
    // 项目查询
    Task<IEnumerable<string>> GetRecentProjectsAsync();
    Task<ProjectModel> GetCurrentProjectAsync();
    
    // 事件
    event EventHandler<ProjectEventArgs> ProjectCreated;
    event EventHandler<ProjectEventArgs> ProjectLoaded;
    event EventHandler<ProjectEventArgs> ProjectSaved;
    event EventHandler<ProjectEventArgs> ProjectClosed;
}
```

#### 实现类
```csharp
public class ProjectService : IProjectService
{
    private readonly IFileService _fileService;
    private readonly ISerializationService _serializationService;
    private ProjectModel _currentProject;

    public ProjectService(IFileService fileService, ISerializationService serializationService)
    {
        _fileService = fileService;
        _serializationService = serializationService;
    }

    public async Task<ProjectModel> CreateProjectAsync(string name, string path)
    {
        var project = new ProjectModel
        {
            Name = name,
            ProjectPath = path,
            CreatedAt = DateTime.Now,
            Version = "1.0.0"
        };

        // 创建项目目录结构
        await CreateProjectStructureAsync(project);
        
        // 保存项目文件
        await SaveProjectAsync(project);
        
        _currentProject = project;
        ProjectCreated?.Invoke(this, new ProjectEventArgs(project));
        
        return project;
    }

    public async Task<ProjectModel> LoadProjectAsync(string projectPath)
    {
        if (!File.Exists(projectPath))
            throw new FileNotFoundException($"项目文件不存在: {projectPath}");

        var projectData = await _fileService.ReadTextAsync(projectPath);
        var project = _serializationService.Deserialize<ProjectModel>(projectData);
        
        project.ProjectPath = projectPath;
        _currentProject = project;
        
        ProjectLoaded?.Invoke(this, new ProjectEventArgs(project));
        
        return project;
    }

    private async Task CreateProjectStructureAsync(ProjectModel project)
    {
        var projectDir = Path.GetDirectoryName(project.ProjectPath);
        
        // 创建子目录
        var directories = new[] { "SFC", "Blueprints", "HMI", "Config", "Build" };
        foreach (var dir in directories)
        {
            var fullPath = Path.Combine(projectDir, dir);
            Directory.CreateDirectory(fullPath);
        }
    }

    public event EventHandler<ProjectEventArgs> ProjectCreated;
    public event EventHandler<ProjectEventArgs> ProjectLoaded;
    public event EventHandler<ProjectEventArgs> ProjectSaved;
    public event EventHandler<ProjectEventArgs> ProjectClosed;
}
```

### 2. IBlueprintService

#### 接口定义
```csharp
public interface IBlueprintService
{
    // 蓝图执行
    Task<Guid> ExecuteBlueprintAsync(BlueprintModel blueprint);
    Task PauseExecutionAsync(Guid executionId);
    Task ResumeExecutionAsync(Guid executionId);
    Task StopExecutionAsync(Guid executionId);
    
    // 执行状态
    Task<ExecutionState> GetExecutionStateAsync(Guid executionId);
    Task<IEnumerable<ExecutionState>> GetActiveExecutionsAsync();
    
    // 蓝图验证
    Task<ValidationResult> ValidateBlueprintAsync(BlueprintModel blueprint);
    
    // 事件
    event EventHandler<ExecutionEventArgs> ExecutionStarted;
    event EventHandler<ExecutionEventArgs> ExecutionCompleted;
    event EventHandler<ExecutionErrorEventArgs> ExecutionError;
}
```

### 3. ISFCValidator

#### 接口定义
```csharp
public interface ISFCValidator
{
    // 验证方法
    Task<ValidationResult> ValidateAsync(SFCModel sfcModel);
    Task<ValidationResult> ValidateElementAsync(SFCElementViewModel element);
    Task<ValidationResult> ValidateConnectionAsync(SFCConnectionViewModel connection);
    
    // 规则管理
    void RegisterRule(ISFCValidationRule rule);
    void RemoveRule(ISFCValidationRule rule);
    IEnumerable<ISFCValidationRule> GetRules();
    
    // 实时验证
    void EnableRealTimeValidation(SFCModel sfcModel);
    void DisableRealTimeValidation();
    
    // 事件
    event EventHandler<ValidationEventArgs> ValidationCompleted;
    event EventHandler<ValidationEventArgs> ValidationError;
}
```

---

## 🛠️ 重要工具类

### 1. SFCConnectionManager

#### 主要方法
```csharp
public class SFCConnectionManager
{
    // 连接创建
    public SFCConnectionViewModel CreateConnection(string sourceElementId, int sourceIndex, 
        string targetElementId, int targetIndex)
    {
        var connection = new SFCConnectionViewModel
        {
            Id = Guid.NewGuid().ToString(),
            SourceElementId = sourceElementId,
            SourceConnectPointIndex = sourceIndex,
            TargetElementId = targetElementId,
            TargetConnectPointIndex = targetIndex
        };

        _connections.Add(connection);
        UpdateConnectPointStates(connection);
        
        return connection;
    }

    // 连接验证
    public ValidationResult ValidateConnection(string sourceElementId, int sourceIndex, 
        string targetElementId, int targetIndex)
    {
        var result = new ValidationResult();
        
        // 获取连接点适配器
        var sourceAdapter = GetConnectPointAdapter(sourceElementId, sourceIndex, true);
        var targetAdapter = GetConnectPointAdapter(targetElementId, targetIndex, false);
        
        if (sourceAdapter == null || targetAdapter == null)
        {
            result.AddError("无效的连接点");
            return result;
        }
        
        // 检查连接兼容性
        if (!sourceAdapter.CanConnectTo(targetAdapter))
        {
            result.AddError("连接点类型不兼容");
        }
        
        return result;
    }

    // 连接查询
    public IEnumerable<SFCConnectionViewModel> GetConnectionsForElement(string elementId)
    {
        return _connections.Where(c => 
            c.SourceElementId == elementId || c.TargetElementId == elementId);
    }

    public IEnumerable<SFCConnectionViewModel> GetInputConnections(string elementId)
    {
        return _connections.Where(c => c.TargetElementId == elementId);
    }

    public IEnumerable<SFCConnectionViewModel> GetOutputConnections(string elementId)
    {
        return _connections.Where(c => c.SourceElementId == elementId);
    }
}
```

### 2. NodeDiscoveryService

#### 主要方法
```csharp
public class NodeDiscoveryService
{
    // 节点扫描
    public void ScanAssembly(Assembly assembly)
    {
        var nodeTypes = assembly.GetTypes()
            .Where(t => t.GetCustomAttribute<BlueprintNodeAttribute>() != null);

        foreach (var nodeType in nodeTypes)
        {
            var nodeDefinition = CreateNodeDefinition(nodeType);
            RegisterNodeDefinition(nodeDefinition);
        }
    }

    // 节点查询
    public IEnumerable<NodeDefinition> GetNodesByCategory(string category)
    {
        return _nodesByCategory.TryGetValue(category, out var nodes) 
            ? nodes.OrderBy(n => n.DisplayName) 
            : Enumerable.Empty<NodeDefinition>();
    }

    public NodeDefinition GetNodeDefinition(Type nodeType)
    {
        return _nodesByType.TryGetValue(nodeType, out var definition) ? definition : null;
    }

    public IEnumerable<string> GetCategories()
    {
        return _nodesByCategory.Keys.OrderBy(k => k);
    }

    // 节点创建
    public BlueprintNode CreateNode(NodeDefinition nodeDefinition)
    {
        var node = new BlueprintNode
        {
            Id = Guid.NewGuid().ToString(),
            NodeDefinition = nodeDefinition,
            DisplayName = nodeDefinition.DisplayName
        };

        // 创建引脚
        CreatePinsForNode(node, nodeDefinition);
        
        return node;
    }
}
```

### 3. ValidationHelper

#### 扩展方法
```csharp
public static class ValidationHelper
{
    // 验证结果合并
    public static ValidationResult Merge(this ValidationResult result, ValidationResult other)
    {
        if (other == null) return result;
        
        foreach (var error in other.Errors)
            result.Errors.Add(error);
            
        foreach (var warning in other.Warnings)
            result.Warnings.Add(warning);
            
        return result;
    }

    // 验证消息创建
    public static ValidationMessage CreateError(string message, string elementId = null)
    {
        return new ValidationMessage(ValidationLevel.Error, message, elementId);
    }

    public static ValidationMessage CreateWarning(string message, string elementId = null)
    {
        return new ValidationMessage(ValidationLevel.Warning, message, elementId);
    }

    // 验证结果检查
    public static bool HasErrorsForElement(this ValidationResult result, string elementId)
    {
        return result.Errors.Any(e => e.ElementId == elementId);
    }

    public static IEnumerable<ValidationMessage> GetMessagesForElement(
        this ValidationResult result, string elementId)
    {
        return result.Errors.Concat(result.Warnings)
            .Where(m => m.ElementId == elementId);
    }
}
```

### 4. GeometryHelper

#### 几何计算方法
```csharp
public static class GeometryHelper
{
    // 点距离计算
    public static double Distance(Point p1, Point p2)
    {
        var dx = p2.X - p1.X;
        var dy = p2.Y - p1.Y;
        return Math.Sqrt(dx * dx + dy * dy);
    }

    // 矩形碰撞检测
    public static bool IsPointInRectangle(Point point, Rect rectangle)
    {
        return point.X >= rectangle.Left && point.X <= rectangle.Right &&
               point.Y >= rectangle.Top && point.Y <= rectangle.Bottom;
    }

    // 贝塞尔曲线计算
    public static Point CalculateBezierPoint(Point start, Point control1, Point control2, Point end, double t)
    {
        var u = 1 - t;
        var tt = t * t;
        var uu = u * u;
        var uuu = uu * u;
        var ttt = tt * t;

        var x = uuu * start.X + 3 * uu * t * control1.X + 3 * u * tt * control2.X + ttt * end.X;
        var y = uuu * start.Y + 3 * uu * t * control1.Y + 3 * u * tt * control2.Y + ttt * end.Y;

        return new Point(x, y);
    }

    // 网格对齐
    public static Point SnapToGrid(Point point, double gridSize)
    {
        return new Point(
            Math.Round(point.X / gridSize) * gridSize,
            Math.Round(point.Y / gridSize) * gridSize);
    }

    // 角度计算
    public static double CalculateAngle(Point from, Point to)
    {
        var dx = to.X - from.X;
        var dy = to.Y - from.Y;
        return Math.Atan2(dy, dx) * 180 / Math.PI;
    }
}
```

---

## ⚙️ 配置和常量

### 1. SFCConnectPointConfig

#### 配置常量
```csharp
public static class SFCConnectPointConfig
{
    // 连接点尺寸
    public const double ConnectPointSize = 12.0;
    public const double ConnectPointStrokeThickness = 1.0;
    
    // 连接点边距配置
    public static readonly Thickness StepInputMargin = new Thickness(-6, -6, 0, 0);
    public static readonly Thickness StepOutputMargin = new Thickness(-6, 0, 0, -6);
    
    public static readonly Thickness TransitionInputMargin = new Thickness(-6, -6, 0, 0);
    public static readonly Thickness TransitionOutputMargin = new Thickness(-6, 0, 0, -6);
    
    // 分支连接点边距
    public static readonly Thickness SelectionBranchLeftTopMargin = new Thickness(-6, 10, 0, 0);
    public static readonly Thickness SelectionBranchLeftBottomMargin = new Thickness(-6, 0, 0, 10);
    public static readonly Thickness SelectionBranchRightTopMargin = new Thickness(0, 10, -6, 0);
    public static readonly Thickness SelectionBranchRightBottomMargin = new Thickness(0, 0, -6, 10);
    
    // 颜色配置
    public static readonly SolidColorBrush ConnectedBrush = new SolidColorBrush(Colors.LimeGreen);
    public static readonly SolidColorBrush DisconnectedBrush = new SolidColorBrush(Colors.White);
    public static readonly SolidColorBrush HoverBrush = new SolidColorBrush(Colors.Yellow);
    
    // 连接线配置
    public const double ConnectionLineThickness = 2.0;
    public const double ConnectionArrowSize = 8.0;
    public static readonly SolidColorBrush ConnectionLineBrush = new SolidColorBrush(Colors.White);
}
```

### 2. ApplicationConstants

#### 应用程序常量
```csharp
public static class ApplicationConstants
{
    // 版本信息
    public const string ApplicationName = "PC_Control2";
    public const string ApplicationVersion = "1.0.0";
    public const string CompanyName = "Industrial Automation Solutions";
    
    // 文件扩展名
    public const string ProjectFileExtension = ".pc2proj";
    public const string SFCFileExtension = ".sfc";
    public const string BlueprintFileExtension = ".bp";
    
    // 默认路径
    public static readonly string DefaultProjectsPath = 
        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "PC_Control2_Projects");
    
    public static readonly string ApplicationDataPath = 
        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PC_Control2");
    
    // 网格设置
    public const double DefaultGridSize = 20.0;
    public const double MinZoomLevel = 0.1;
    public const double MaxZoomLevel = 5.0;
    
    // 性能设置
    public const int MaxUndoStackSize = 50;
    public const int AutoSaveIntervalMinutes = 5;
    public const int MaxRecentProjects = 10;
}
```

### 3. SFCElementTypes

#### 元素类型枚举
```csharp
public enum SFCElementType
{
    [Description("步骤")]
    Step = 1,
    
    [Description("转换条件")]
    Transition = 2,
    
    [Description("选择分支")]
    SelectionBranch = 3,
    
    [Description("并行分支")]
    ParallelBranch = 4,
    
    [Description("跳转")]
    Jump = 5,
    
    [Description("终止符")]
    Terminator = 6
}

public enum SFCBranchType
{
    [Description("选择分支")]
    Selection = 1,
    
    [Description("并行分支")]
    Parallel = 2
}

public enum ConnectPointDirection
{
    [Description("输入")]
    Input = 1,
    
    [Description("输出")]
    Output = 2,
    
    [Description("双向")]
    InputOutput = 3
}

public enum SFCDataFlowType
{
    [Description("控制流")]
    ControlFlow = 1,
    
    [Description("数据流")]
    DataFlow = 2
}
```

### 4. ValidationLevels

#### 验证级别定义
```csharp
public enum ValidationLevel
{
    [Description("信息")]
    Info = 1,
    
    [Description("警告")]
    Warning = 2,
    
    [Description("错误")]
    Error = 3,
    
    [Description("严重错误")]
    Critical = 4
}

public static class ValidationLevelExtensions
{
    public static Color GetColor(this ValidationLevel level)
    {
        return level switch
        {
            ValidationLevel.Info => Colors.Blue,
            ValidationLevel.Warning => Colors.Orange,
            ValidationLevel.Error => Colors.Red,
            ValidationLevel.Critical => Colors.DarkRed,
            _ => Colors.Gray
        };
    }

    public static string GetIcon(this ValidationLevel level)
    {
        return level switch
        {
            ValidationLevel.Info => "ℹ️",
            ValidationLevel.Warning => "⚠️",
            ValidationLevel.Error => "❌",
            ValidationLevel.Critical => "🚫",
            _ => "❓"
        };
    }
}
```

---

## 📋 快速参考检查清单

### ViewModel使用
- [ ] 继承ViewModelBase
- [ ] 实现属性通知
- [ ] 使用RelayCommand
- [ ] 正确处理依赖注入
- [ ] 实现IDisposable（如需要）

### 服务实现
- [ ] 定义清晰的接口
- [ ] 实现异步方法
- [ ] 正确处理异常
- [ ] 提供事件通知
- [ ] 支持取消操作

### 工具类设计
- [ ] 静态方法优先
- [ ] 扩展方法适当使用
- [ ] 参数验证
- [ ] 异常处理
- [ ] 性能考虑

### 配置管理
- [ ] 常量集中定义
- [ ] 枚举提供描述
- [ ] 默认值合理
- [ ] 类型安全
- [ ] 易于维护

---

## 🎯 使用建议

### 1. 命名约定
- **ViewModel**: 以`ViewModel`结尾
- **Service**: 以`Service`结尾，接口以`I`开头
- **Model**: 以`Model`结尾
- **Command**: 以`Command`结尾
- **Event**: 以`EventArgs`结尾

### 2. 依赖注入
```csharp
// 在App.xaml.cs中注册服务
services.AddSingleton<IProjectService, ProjectService>();
services.AddTransient<MainWindowViewModel>();

// 在构造函数中注入依赖
public MainWindowViewModel(IProjectService projectService)
{
    _projectService = projectService ?? throw new ArgumentNullException(nameof(projectService));
}
```

### 3. 异步编程
```csharp
// 使用async/await模式
public async Task<bool> SaveProjectAsync()
{
    try
    {
        await _projectService.SaveProjectAsync(CurrentProject);
        return true;
    }
    catch (Exception ex)
    {
        ShowErrorMessage($"保存失败: {ex.Message}");
        return false;
    }
}
```

### 4. 错误处理
```csharp
// 统一的错误处理模式
try
{
    // 业务逻辑
}
catch (ArgumentException ex)
{
    // 参数错误
    ShowErrorMessage($"参数错误: {ex.Message}");
}
catch (InvalidOperationException ex)
{
    // 操作错误
    ShowErrorMessage($"操作失败: {ex.Message}");
}
catch (Exception ex)
{
    // 未知错误
    ShowErrorMessage($"未知错误: {ex.Message}");
    Logger.LogError(ex, "Unexpected error occurred");
}
```

这个快速参考文档提供了PC_Control2项目中最重要的类和方法的概览，帮助开发者快速定位和使用核心功能。