using System;
using System.Windows;
using PC_Control2.Demo.Controls;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// SFC连接创建器 - 替换坐标重叠机制为真实连接点关联
    /// 实现基于连接点的真实逻辑连接
    /// </summary>
    public class SFCConnectionCreator
    {
        #region 私有字段
        
        private readonly SFCConnectionManager _connectionManager;
        
        #endregion
        
        #region 构造函数
        
        public SFCConnectionCreator(SFCConnectionManager connectionManager)
        {
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        }
        
        #endregion
        
        #region 连接创建方法
        
        /// <summary>
        /// 基于连接点创建连接（替换坐标重叠方式）
        /// </summary>
        /// <param name="sourceConnectPoint">源连接点适配器</param>
        /// <param name="targetConnectPoint">目标连接点适配器</param>
        /// <returns>创建的连接模型，失败则返回null</returns>
        public SFCConnectionModel? CreateConnection(
            SFCConnectPointAdapter sourceConnectPoint, 
            SFCConnectPointAdapter targetConnectPoint)
        {
            // 1. 验证连接的有效性
            var validationResult = sourceConnectPoint.CanConnectTo(targetConnectPoint);
            if (!validationResult.IsValid)
            {
                System.Diagnostics.Debug.WriteLine($"[SFCConnectionCreator] 连接验证失败: {validationResult.Message}");
                return null;
            }
            
            // 2. 获取源和目标元素
            var sourceElement = _connectionManager.GetElement(sourceConnectPoint.ElementId);
            var targetElement = _connectionManager.GetElement(targetConnectPoint.ElementId);
            
            if (sourceElement == null || targetElement == null)
            {
                System.Diagnostics.Debug.WriteLine($"[SFCConnectionCreator] 元素不存在: Source={sourceElement?.Id}, Target={targetElement?.Id}");
                return null;
            }
            
            // 3. 创建连接模型
            var connection = new SFCConnectionModel
            {
                Id = Guid.NewGuid().ToString(),
                SourceId = sourceElement.Id,
                TargetId = targetElement.Id,
                SourceType = sourceElement.ElementType,
                TargetType = targetElement.ElementType,
                SourceConnectPointIndex = sourceConnectPoint.ConnectPointIndex,
                TargetConnectPointIndex = targetConnectPoint.ConnectPointIndex,
                ConnectionType = DetermineConnectionType(sourceElement, targetElement),
                LineStyle = LineStyle.Solid
            };
            
            // 4. 设置连接点关联
            connection.SetSourceConnectPoint(sourceConnectPoint);
            connection.SetTargetConnectPoint(targetConnectPoint);
            
            // 5. 设置元素关联
            connection.SetSourceElement(sourceElement);
            connection.SetTargetElement(targetElement);
            
            // 6. 计算连接路径点（基于连接点位置而非坐标重叠）
            var pathPoints = CalculateConnectionPath(sourceConnectPoint, targetConnectPoint);
            connection.PathPoints.Clear();
            foreach (var point in pathPoints)
            {
                connection.PathPoints.Add(point);
            }
            
            // 7. 添加到连接管理器
            _connectionManager.AddConnection(connection);

            // 🔧 关键修复：更新连接点适配器状态，确保拖拽创建的连接也能正确显示连接状态
            sourceConnectPoint.AddConnection(connection.Id);
            targetConnectPoint.AddConnection(connection.Id);

            System.Diagnostics.Debug.WriteLine($"[SFCConnectionCreator] 连接创建成功: {sourceElement.Id} -> {targetElement.Id}");
            System.Diagnostics.Debug.WriteLine($"[SFCConnectionCreator] ✅ 已更新连接点适配器状态: 源适配器ConnectionIds={sourceConnectPoint.ConnectionIds.Count}, 目标适配器ConnectionIds={targetConnectPoint.ConnectionIds.Count}");
            return connection;
        }
        
        /// <summary>
        /// 基于UI连接点创建连接
        /// </summary>
        /// <param name="sourceUIConnectPoint">源UI连接点</param>
        /// <param name="targetUIConnectPoint">目标UI连接点</param>
        /// <returns>创建的连接模型，失败则返回null</returns>
        public SFCConnectionModel? CreateConnection(
            SFCConnectPoint sourceUIConnectPoint, 
            SFCConnectPoint targetUIConnectPoint)
        {
            if (sourceUIConnectPoint.Adapter == null || targetUIConnectPoint.Adapter == null)
            {
                System.Diagnostics.Debug.WriteLine("[SFCConnectionCreator] UI连接点缺少适配器");
                return null;
            }
            
            return CreateConnection(sourceUIConnectPoint.Adapter, targetUIConnectPoint.Adapter);
        }
        
        /// <summary>
        /// 自动连接（用于元素插入时的自动连接）
        /// </summary>
        /// <param name="sourceElement">源元素</param>
        /// <param name="targetElement">目标元素</param>
        /// <param name="sourceConnectPointIndex">源连接点索引（默认为输出连接点0）</param>
        /// <param name="targetConnectPointIndex">目标连接点索引（默认为输入连接点0）</param>
        /// <returns>创建的连接模型，失败则返回null</returns>
        public SFCConnectionModel? CreateAutoConnection(
            SFCElementModelBase sourceElement,
            SFCElementModelBase targetElement,
            int sourceConnectPointIndex = 0,
            int targetConnectPointIndex = 0)
        {
            // 获取源输出连接点
            var sourceConnectPoint = sourceElement.GetConnectPointByIndex(ConnectPointDirection.Output, sourceConnectPointIndex);
            if (sourceConnectPoint == null)
            {
                System.Diagnostics.Debug.WriteLine($"[SFCConnectionCreator] 源元素缺少输出连接点: {sourceElement.Id}[{sourceConnectPointIndex}]");
                return null;
            }
            
            // 获取目标输入连接点
            var targetConnectPoint = targetElement.GetConnectPointByIndex(ConnectPointDirection.Input, targetConnectPointIndex);
            if (targetConnectPoint == null)
            {
                System.Diagnostics.Debug.WriteLine($"[SFCConnectionCreator] 目标元素缺少输入连接点: {targetElement.Id}[{targetConnectPointIndex}]");
                return null;
            }
            
            return CreateConnection(sourceConnectPoint, targetConnectPoint);
        }
        
        #endregion
        
        #region 私有辅助方法
        
        /// <summary>
        /// 确定连接类型
        /// </summary>
        private SFCConnectionType DetermineConnectionType(SFCElementModelBase source, SFCElementModelBase target)
        {
            // 根据源和目标元素类型确定连接类型
            if (source.ElementType == SFCElementType.Branch || target.ElementType == SFCElementType.Branch)
            {
                return SFCConnectionType.Branch;
            }
            
            return SFCConnectionType.Normal;
        }
        
        /// <summary>
        /// 计算连接路径点（基于连接点位置）
        /// </summary>
        private Point[] CalculateConnectionPath(
            SFCConnectPointAdapter sourceConnectPoint, 
            SFCConnectPointAdapter targetConnectPoint)
        {
            // 获取连接点的UI控件
            var sourceElement = _connectionManager.GetElement(sourceConnectPoint.ElementId);
            var targetElement = _connectionManager.GetElement(targetConnectPoint.ElementId);
            
            if (sourceElement == null || targetElement == null)
            {
                return new Point[0];
            }
            
            // 获取连接点UI控件
            var sourceUI = sourceElement.GetConnectPointUI(sourceConnectPoint.Id);
            var targetUI = targetElement.GetConnectPointUI(targetConnectPoint.Id);
            
            if (sourceUI != null && targetUI != null)
            {
                // 基于真实连接点位置计算路径
                var sourcePosition = sourceUI.GetCenterPosition(null);
                var targetPosition = targetUI.GetCenterPosition(null);
                
                return new Point[] { sourcePosition, targetPosition };
            }
            else
            {
                // 回退到元素中心位置
                var sourcePosition = new Point(
                    sourceElement.Position.X + sourceElement.Size.Width / 2,
                    sourceElement.Position.Y + sourceElement.Size.Height);
                    
                var targetPosition = new Point(
                    targetElement.Position.X + targetElement.Size.Width / 2,
                    targetElement.Position.Y);
                    
                return new Point[] { sourcePosition, targetPosition };
            }
        }
        
        #endregion
    }
}
