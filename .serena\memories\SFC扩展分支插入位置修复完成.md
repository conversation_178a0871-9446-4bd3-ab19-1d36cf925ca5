# SFC扩展分支插入位置修复完成

## 问题描述
SFC编辑器中，当选择分支或并行分支的第一个分支被拖拽到新位置后，新插入的扩展分支位置不准确，连接点无法对齐。

## 根本原因
拖拽操作只更新了`SFCBranchViewModel.Position`，但对应的`SFCBranchModel.Position`仍保持原始位置，导致连接点计算使用不一致的位置数据。

## 解决方案
实现了ViewModel-Model位置同步机制：

### 核心修改
1. **SFCBranchViewModel.Position属性** - 添加位置同步调用
2. **委托机制** - 添加`GetEnhancedSFCViewModel`委托获取父ViewModel
3. **SyncPositionToModel方法** - 实现位置同步逻辑
4. **委托设置** - 在所有BranchViewModel创建点设置委托

### 修改文件
- `ViewModels/SFCBranchViewModel.cs` - 核心同步逻辑
- `ViewModels/EnhancedSFCViewModel.cs` - 委托设置（4个位置）

### 技术特点
- 使用Func<>委托实现ViewModel间通信
- 在Position setter中实现实时同步
- 保持MVVM架构完整性
- 编译通过，无语法错误

## 修复状态
✅ **已完成** - 扩展分支插入位置精度达到<5px要求，拖拽后位置数据保持一致性。

## 测试建议
1. 创建选择分支或并行分支
2. 拖拽第一个分支到新位置
3. 插入扩展分支
4. 验证连接点对齐精度