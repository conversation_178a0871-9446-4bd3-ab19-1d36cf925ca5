# PC_Control2 蓝图系统架构和实现

## 📋 概述
本文档详细介绍了PC_Control2项目中蓝图系统的架构设计和核心实现，包括节点发现机制、连接验证系统、执行引擎设计和节点库扩展技术。

---

## 🏗️ 蓝图系统架构

### 核心组件关系
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  BlueprintCanvas│◄──►│BlueprintViewModel│◄──►│  BlueprintModel │
│   (画布控件)     │    │   (视图模型)     │    │   (数据模型)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
    节点渲染交互              业务逻辑协调              数据持久化
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│NodeDiscoveryService│  │BlueprintService │    │ConnectionManager│
│   (节点发现)     │    │   (执行引擎)     │    │   (连接管理)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 系统分层架构
```
┌─────────────────────────────────────┐
│           表现层 (UI Layer)          │  ← BlueprintCanvas, NodeViews
├─────────────────────────────────────┤
│         业务层 (Business Layer)      │  ← BlueprintService, ValidationService
├─────────────────────────────────────┤
│         服务层 (Service Layer)       │  ← NodeDiscovery, ExecutionEngine
├─────────────────────────────────────┤
│         模型层 (Model Layer)         │  ← BlueprintModel, NodeModel
├─────────────────────────────────────┤
│         扩展层 (Extension Layer)     │  ← NodeLibraries, CustomNodes
└─────────────────────────────────────┘
```

---

## 🔍 节点发现机制

### 1. 反射扫描系统

#### 节点特性定义
```csharp
[AttributeUsage(AttributeTargets.Class)]
public class BlueprintNodeAttribute : Attribute
{
    public string Category { get; set; }
    public string DisplayName { get; set; }
    public string Description { get; set; }
    public string IconPath { get; set; }
    public bool IsDeprecated { get; set; }

    public BlueprintNodeAttribute(string category, string displayName)
    {
        Category = category;
        DisplayName = displayName;
    }
}

[AttributeUsage(AttributeTargets.Method)]
public class BlueprintMethodAttribute : Attribute
{
    public string DisplayName { get; set; }
    public string Description { get; set; }
    public bool IsAsync { get; set; }
    public ExecutionMode ExecutionMode { get; set; } = ExecutionMode.Normal;

    public BlueprintMethodAttribute(string displayName = null)
    {
        DisplayName = displayName;
    }
}

[AttributeUsage(AttributeTargets.Parameter | AttributeTargets.ReturnValue)]
public class BlueprintPinAttribute : Attribute
{
    public string DisplayName { get; set; }
    public string Description { get; set; }
    public PinDirection Direction { get; set; }
    public bool IsOptional { get; set; }
    public object DefaultValue { get; set; }

    public BlueprintPinAttribute(PinDirection direction, string displayName = null)
    {
        Direction = direction;
        DisplayName = displayName;
    }
}

public enum ExecutionMode
{
    Normal,     // 普通执行
    Pure,       // 纯函数，无副作用
    Event,      // 事件节点
    Latent      // 延迟执行
}

public enum PinDirection
{
    Input,
    Output,
    InputOutput
}
```

#### 节点发现服务
```csharp
public class NodeDiscoveryService : INodeDiscoveryService
{
    private readonly Dictionary<string, List<NodeDefinition>> _nodesByCategory = new();
    private readonly Dictionary<Type, NodeDefinition> _nodesByType = new();
    private readonly List<Assembly> _scannedAssemblies = new();

    public void ScanAssembly(Assembly assembly)
    {
        if (_scannedAssemblies.Contains(assembly))
            return;

        _scannedAssemblies.Add(assembly);

        var nodeTypes = assembly.GetTypes()
            .Where(t => t.GetCustomAttribute<BlueprintNodeAttribute>() != null)
            .ToList();

        foreach (var nodeType in nodeTypes)
        {
            var nodeDefinition = CreateNodeDefinition(nodeType);
            RegisterNodeDefinition(nodeDefinition);
        }
    }

    public void ScanCurrentAppDomain()
    {
        var assemblies = AppDomain.CurrentDomain.GetAssemblies()
            .Where(a => !a.IsDynamic && !IsSystemAssembly(a))
            .ToList();

        foreach (var assembly in assemblies)
        {
            try
            {
                ScanAssembly(assembly);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"扫描程序集失败: {assembly.FullName}, 错误: {ex.Message}");
            }
        }
    }

    private NodeDefinition CreateNodeDefinition(Type nodeType)
    {
        var nodeAttr = nodeType.GetCustomAttribute<BlueprintNodeAttribute>();
        var methods = nodeType.GetMethods(BindingFlags.Public | BindingFlags.Static)
            .Where(m => m.GetCustomAttribute<BlueprintMethodAttribute>() != null)
            .ToList();

        var nodeDefinition = new NodeDefinition
        {
            NodeType = nodeType,
            Category = nodeAttr.Category,
            DisplayName = nodeAttr.DisplayName ?? nodeType.Name,
            Description = nodeAttr.Description,
            IconPath = nodeAttr.IconPath,
            IsDeprecated = nodeAttr.IsDeprecated,
            Methods = methods.Select(CreateMethodDefinition).ToList()
        };

        return nodeDefinition;
    }

    private MethodDefinition CreateMethodDefinition(MethodInfo method)
    {
        var methodAttr = method.GetCustomAttribute<BlueprintMethodAttribute>();
        
        return new MethodDefinition
        {
            Method = method,
            DisplayName = methodAttr.DisplayName ?? method.Name,
            Description = methodAttr.Description,
            IsAsync = methodAttr.IsAsync,
            ExecutionMode = methodAttr.ExecutionMode,
            InputPins = CreateInputPinDefinitions(method),
            OutputPins = CreateOutputPinDefinitions(method)
        };
    }

    private List<PinDefinition> CreateInputPinDefinitions(MethodInfo method)
    {
        var pins = new List<PinDefinition>();

        // 添加执行引脚（如果不是纯函数）
        var methodAttr = method.GetCustomAttribute<BlueprintMethodAttribute>();
        if (methodAttr.ExecutionMode != ExecutionMode.Pure)
        {
            pins.Add(new PinDefinition
            {
                Name = "Exec",
                DisplayName = "执行",
                DataType = typeof(ExecutionPin),
                Direction = PinDirection.Input,
                IsExecutionPin = true
            });
        }

        // 添加参数引脚
        foreach (var parameter in method.GetParameters())
        {
            var pinAttr = parameter.GetCustomAttribute<BlueprintPinAttribute>();
            pins.Add(new PinDefinition
            {
                Name = parameter.Name,
                DisplayName = pinAttr?.DisplayName ?? parameter.Name,
                Description = pinAttr?.Description,
                DataType = parameter.ParameterType,
                Direction = PinDirection.Input,
                IsOptional = pinAttr?.IsOptional ?? parameter.HasDefaultValue,
                DefaultValue = pinAttr?.DefaultValue ?? parameter.DefaultValue
            });
        }

        return pins;
    }

    private List<PinDefinition> CreateOutputPinDefinitions(MethodInfo method)
    {
        var pins = new List<PinDefinition>();

        // 添加执行输出引脚
        var methodAttr = method.GetCustomAttribute<BlueprintMethodAttribute>();
        if (methodAttr.ExecutionMode != ExecutionMode.Pure)
        {
            pins.Add(new PinDefinition
            {
                Name = "ExecOut",
                DisplayName = "完成",
                DataType = typeof(ExecutionPin),
                Direction = PinDirection.Output,
                IsExecutionPin = true
            });
        }

        // 添加返回值引脚
        if (method.ReturnType != typeof(void))
        {
            var returnAttr = method.ReturnParameter?.GetCustomAttribute<BlueprintPinAttribute>();
            pins.Add(new PinDefinition
            {
                Name = "ReturnValue",
                DisplayName = returnAttr?.DisplayName ?? "返回值",
                Description = returnAttr?.Description,
                DataType = method.ReturnType,
                Direction = PinDirection.Output
            });
        }

        return pins;
    }

    private void RegisterNodeDefinition(NodeDefinition nodeDefinition)
    {
        // 按类别分组
        if (!_nodesByCategory.ContainsKey(nodeDefinition.Category))
        {
            _nodesByCategory[nodeDefinition.Category] = new List<NodeDefinition>();
        }
        _nodesByCategory[nodeDefinition.Category].Add(nodeDefinition);

        // 按类型索引
        _nodesByType[nodeDefinition.NodeType] = nodeDefinition;
    }

    public IEnumerable<string> GetCategories()
    {
        return _nodesByCategory.Keys.OrderBy(k => k);
    }

    public IEnumerable<NodeDefinition> GetNodesByCategory(string category)
    {
        return _nodesByCategory.TryGetValue(category, out var nodes) 
            ? nodes.OrderBy(n => n.DisplayName) 
            : Enumerable.Empty<NodeDefinition>();
    }

    public NodeDefinition GetNodeDefinition(Type nodeType)
    {
        return _nodesByType.TryGetValue(nodeType, out var definition) ? definition : null;
    }

    private bool IsSystemAssembly(Assembly assembly)
    {
        var name = assembly.GetName().Name;
        return name.StartsWith("System.") || 
               name.StartsWith("Microsoft.") || 
               name.StartsWith("mscorlib") ||
               name.StartsWith("netstandard");
    }
}
```

### 2. 动态加载机制

#### 插件加载器
```csharp
public class PluginLoader : IPluginLoader
{
    private readonly NodeDiscoveryService _nodeDiscovery;
    private readonly List<string> _pluginDirectories = new();

    public PluginLoader(NodeDiscoveryService nodeDiscovery)
    {
        _nodeDiscovery = nodeDiscovery;
        _pluginDirectories.Add(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Plugins"));
    }

    public async Task LoadPluginsAsync()
    {
        var tasks = _pluginDirectories.Select(LoadPluginsFromDirectoryAsync);
        await Task.WhenAll(tasks);
    }

    private async Task LoadPluginsFromDirectoryAsync(string directory)
    {
        if (!Directory.Exists(directory))
            return;

        var pluginFiles = Directory.GetFiles(directory, "*.dll", SearchOption.AllDirectories);
        
        foreach (var pluginFile in pluginFiles)
        {
            try
            {
                await LoadPluginAsync(pluginFile);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载插件失败: {pluginFile}, 错误: {ex.Message}");
            }
        }
    }

    private async Task LoadPluginAsync(string pluginPath)
    {
        var assembly = Assembly.LoadFrom(pluginPath);
        
        // 验证插件
        if (!ValidatePlugin(assembly))
        {
            Debug.WriteLine($"插件验证失败: {pluginPath}");
            return;
        }

        // 扫描节点
        await Task.Run(() => _nodeDiscovery.ScanAssembly(assembly));
        
        Debug.WriteLine($"成功加载插件: {assembly.GetName().Name}");
    }

    private bool ValidatePlugin(Assembly assembly)
    {
        // 检查是否包含节点定义
        var hasNodes = assembly.GetTypes()
            .Any(t => t.GetCustomAttribute<BlueprintNodeAttribute>() != null);

        return hasNodes;
    }

    public void AddPluginDirectory(string directory)
    {
        if (!_pluginDirectories.Contains(directory))
        {
            _pluginDirectories.Add(directory);
        }
    }
}
```

---

## ✅ 连接验证系统

### 1. 类型匹配规则

#### 类型兼容性检查器
```csharp
public class TypeCompatibilityChecker : ITypeCompatibilityChecker
{
    private readonly Dictionary<(Type, Type), bool> _compatibilityCache = new();
    private readonly List<ITypeConverter> _typeConverters = new();

    public TypeCompatibilityChecker()
    {
        RegisterDefaultConverters();
    }

    public bool CanConnect(Type sourceType, Type targetType)
    {
        if (sourceType == null || targetType == null)
            return false;

        var key = (sourceType, targetType);
        if (_compatibilityCache.TryGetValue(key, out var cached))
            return cached;

        var result = CheckCompatibility(sourceType, targetType);
        _compatibilityCache[key] = result;
        return result;
    }

    private bool CheckCompatibility(Type sourceType, Type targetType)
    {
        // 1. 完全匹配
        if (sourceType == targetType)
            return true;

        // 2. 继承关系
        if (targetType.IsAssignableFrom(sourceType))
            return true;

        // 3. 泛型兼容性
        if (CheckGenericCompatibility(sourceType, targetType))
            return true;

        // 4. 隐式转换
        if (HasImplicitConversion(sourceType, targetType))
            return true;

        // 5. 自定义转换器
        if (HasCustomConverter(sourceType, targetType))
            return true;

        // 6. 执行引脚特殊处理
        if (IsExecutionPin(sourceType) && IsExecutionPin(targetType))
            return true;

        return false;
    }

    private bool CheckGenericCompatibility(Type sourceType, Type targetType)
    {
        if (!sourceType.IsGenericType || !targetType.IsGenericType)
            return false;

        var sourceGeneric = sourceType.GetGenericTypeDefinition();
        var targetGeneric = targetType.GetGenericTypeDefinition();

        // 检查泛型定义是否兼容
        if (sourceGeneric == targetGeneric)
        {
            var sourceArgs = sourceType.GetGenericArguments();
            var targetArgs = targetType.GetGenericArguments();

            if (sourceArgs.Length != targetArgs.Length)
                return false;

            // 检查所有泛型参数
            for (int i = 0; i < sourceArgs.Length; i++)
            {
                if (!CanConnect(sourceArgs[i], targetArgs[i]))
                    return false;
            }

            return true;
        }

        return false;
    }

    private bool HasImplicitConversion(Type sourceType, Type targetType)
    {
        try
        {
            // 检查隐式转换操作符
            var methods = sourceType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.Name == "op_Implicit" && m.ReturnType == targetType)
                .ToList();

            if (methods.Any())
                return true;

            // 检查目标类型的隐式转换
            methods = targetType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.Name == "op_Implicit" && 
                           m.GetParameters().Length == 1 && 
                           m.GetParameters()[0].ParameterType == sourceType)
                .ToList();

            return methods.Any();
        }
        catch
        {
            return false;
        }
    }

    private bool HasCustomConverter(Type sourceType, Type targetType)
    {
        return _typeConverters.Any(c => c.CanConvert(sourceType, targetType));
    }

    private bool IsExecutionPin(Type type)
    {
        return type == typeof(ExecutionPin) || type.IsSubclassOf(typeof(ExecutionPin));
    }

    public void RegisterConverter(ITypeConverter converter)
    {
        _typeConverters.Add(converter);
    }

    private void RegisterDefaultConverters()
    {
        // 数值类型转换器
        RegisterConverter(new NumericTypeConverter());
        
        // 字符串转换器
        RegisterConverter(new StringTypeConverter());
        
        // 集合转换器
        RegisterConverter(new CollectionTypeConverter());
    }
}

// 自定义类型转换器接口
public interface ITypeConverter
{
    bool CanConvert(Type sourceType, Type targetType);
    object Convert(object value, Type targetType);
}

// 数值类型转换器实现
public class NumericTypeConverter : ITypeConverter
{
    private static readonly Type[] NumericTypes = {
        typeof(byte), typeof(sbyte), typeof(short), typeof(ushort),
        typeof(int), typeof(uint), typeof(long), typeof(ulong),
        typeof(float), typeof(double), typeof(decimal)
    };

    public bool CanConvert(Type sourceType, Type targetType)
    {
        return NumericTypes.Contains(sourceType) && NumericTypes.Contains(targetType);
    }

    public object Convert(object value, Type targetType)
    {
        return System.Convert.ChangeType(value, targetType);
    }
}
```

### 2. 规则检查引擎

#### 连接规则定义
```csharp
public abstract class ConnectionRule
{
    public abstract string Name { get; }
    public abstract string Description { get; }
    public abstract ValidationLevel Level { get; }

    public abstract ValidationResult Validate(BlueprintPin sourcePin, BlueprintPin targetPin);
}

public class ExecutionFlowRule : ConnectionRule
{
    public override string Name => "执行流规则";
    public override string Description => "执行引脚只能连接到执行引脚";
    public override ValidationLevel Level => ValidationLevel.Error;

    public override ValidationResult Validate(BlueprintPin sourcePin, BlueprintPin targetPin)
    {
        var result = new ValidationResult();

        if (sourcePin.IsExecutionPin != targetPin.IsExecutionPin)
        {
            result.AddError("执行引脚只能连接到执行引脚");
        }

        return result;
    }
}

public class CircularReferenceRule : ConnectionRule
{
    public override string Name => "循环引用检查";
    public override string Description => "检测并防止循环依赖";
    public override ValidationLevel Level => ValidationLevel.Error;

    public override ValidationResult Validate(BlueprintPin sourcePin, BlueprintPin targetPin)
    {
        var result = new ValidationResult();

        if (WouldCreateCircularReference(sourcePin.Node, targetPin.Node))
        {
            result.AddError("连接会创建循环依赖");
        }

        return result;
    }

    private bool WouldCreateCircularReference(BlueprintNode sourceNode, BlueprintNode targetNode)
    {
        var visited = new HashSet<BlueprintNode>();
        return HasPath(targetNode, sourceNode, visited);
    }

    private bool HasPath(BlueprintNode from, BlueprintNode to, HashSet<BlueprintNode> visited)
    {
        if (visited.Contains(from))
            return false;

        visited.Add(from);

        if (from == to)
            return true;

        foreach (var outputPin in from.OutputPins)
        {
            foreach (var connection in outputPin.Connections)
            {
                if (HasPath(connection.TargetPin.Node, to, visited))
                    return true;
            }
        }

        return false;
    }
}

public class TypeCompatibilityRule : ConnectionRule
{
    private readonly TypeCompatibilityChecker _typeChecker;

    public override string Name => "类型兼容性";
    public override string Description => "检查引脚类型是否兼容";
    public override ValidationLevel Level => ValidationLevel.Error;

    public TypeCompatibilityRule(TypeCompatibilityChecker typeChecker)
    {
        _typeChecker = typeChecker;
    }

    public override ValidationResult Validate(BlueprintPin sourcePin, BlueprintPin targetPin)
    {
        var result = new ValidationResult();

        if (!_typeChecker.CanConnect(sourcePin.DataType, targetPin.DataType))
        {
            result.AddError($"类型不兼容: {sourcePin.DataType.Name} -> {targetPin.DataType.Name}");
        }

        return result;
    }
}
```

#### 连接验证器
```csharp
public class ConnectionValidator : IConnectionValidator
{
    private readonly List<ConnectionRule> _rules = new();

    public ConnectionValidator()
    {
        RegisterDefaultRules();
    }

    public ValidationResult ValidateConnection(BlueprintPin sourcePin, BlueprintPin targetPin)
    {
        var result = new ValidationResult();

        foreach (var rule in _rules)
        {
            var ruleResult = rule.Validate(sourcePin, targetPin);
            result.Merge(ruleResult);

            // 如果有错误且规则级别是错误，停止验证
            if (ruleResult.HasErrors && rule.Level == ValidationLevel.Error)
            {
                break;
            }
        }

        return result;
    }

    public void RegisterRule(ConnectionRule rule)
    {
        _rules.Add(rule);
    }

    public void RemoveRule(ConnectionRule rule)
    {
        _rules.Remove(rule);
    }

    private void RegisterDefaultRules()
    {
        RegisterRule(new ExecutionFlowRule());
        RegisterRule(new CircularReferenceRule());
        RegisterRule(new TypeCompatibilityRule(new TypeCompatibilityChecker()));
    }
}
```

---

## ⚙️ 执行引擎设计

### 1. 异步执行框架

#### 执行上下文
```csharp
public class BlueprintExecutionContext
{
    private readonly Dictionary<string, object> _variables = new();
    private readonly Stack<BlueprintNode> _executionStack = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    public Guid ExecutionId { get; } = Guid.NewGuid();
    public BlueprintModel Blueprint { get; }
    public CancellationToken CancellationToken => _cancellationTokenSource.Token;
    public bool IsRunning { get; private set; }
    public bool IsPaused { get; private set; }

    public BlueprintExecutionContext(BlueprintModel blueprint)
    {
        Blueprint = blueprint;
    }

    public void SetVariable(string name, object value)
    {
        _variables[name] = value;
    }

    public T GetVariable<T>(string name)
    {
        return _variables.TryGetValue(name, out var value) ? (T)value : default(T);
    }

    public void PushNode(BlueprintNode node)
    {
        _executionStack.Push(node);
    }

    public BlueprintNode PopNode()
    {
        return _executionStack.Count > 0 ? _executionStack.Pop() : null;
    }

    public void Start()
    {
        IsRunning = true;
        IsPaused = false;
    }

    public void Pause()
    {
        IsPaused = true;
    }

    public void Resume()
    {
        IsPaused = false;
    }

    public void Stop()
    {
        IsRunning = false;
        _cancellationTokenSource.Cancel();
    }

    public event EventHandler<NodeExecutionEventArgs> NodeExecuting;
    public event EventHandler<NodeExecutionEventArgs> NodeExecuted;
    public event EventHandler<ExecutionErrorEventArgs> ExecutionError;

    internal void OnNodeExecuting(BlueprintNode node)
    {
        NodeExecuting?.Invoke(this, new NodeExecutionEventArgs(node));
    }

    internal void OnNodeExecuted(BlueprintNode node, object result)
    {
        NodeExecuted?.Invoke(this, new NodeExecutionEventArgs(node, result));
    }

    internal void OnExecutionError(BlueprintNode node, Exception exception)
    {
        ExecutionError?.Invoke(this, new ExecutionErrorEventArgs(node, exception));
    }
}
```

#### 蓝图执行引擎
```csharp
public class BlueprintExecutionEngine : IBlueprintExecutionEngine
{
    private readonly Dictionary<Guid, BlueprintExecutionContext> _activeExecutions = new();
    private readonly IServiceProvider _serviceProvider;

    public BlueprintExecutionEngine(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task<Guid> StartExecutionAsync(BlueprintModel blueprint)
    {
        var context = new BlueprintExecutionContext(blueprint);
        _activeExecutions[context.ExecutionId] = context;

        context.Start();

        // 在后台线程执行蓝图
        _ = Task.Run(async () => await ExecuteBlueprintAsync(context));

        return context.ExecutionId;
    }

    private async Task ExecuteBlueprintAsync(BlueprintExecutionContext context)
    {
        try
        {
            // 查找入口节点（事件节点）
            var entryNodes = context.Blueprint.Nodes
                .Where(n => n.NodeDefinition.Methods.Any(m => m.ExecutionMode == ExecutionMode.Event))
                .ToList();

            if (!entryNodes.Any())
            {
                throw new InvalidOperationException("蓝图中没有找到入口节点");
            }

            // 执行所有入口节点
            var tasks = entryNodes.Select(node => ExecuteNodeAsync(context, node));
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            context.OnExecutionError(null, ex);
        }
        finally
        {
            context.Stop();
            _activeExecutions.Remove(context.ExecutionId);
        }
    }

    private async Task ExecuteNodeAsync(BlueprintExecutionContext context, BlueprintNode node)
    {
        if (context.CancellationToken.IsCancellationRequested)
            return;

        try
        {
            context.OnNodeExecuting(node);

            // 等待暂停状态
            while (context.IsPaused && !context.CancellationToken.IsCancellationRequested)
            {
                await Task.Delay(100, context.CancellationToken);
            }

            // 收集输入参数
            var inputValues = await CollectInputValuesAsync(context, node);

            // 执行节点方法
            var result = await InvokeNodeMethodAsync(context, node, inputValues);

            // 设置输出值
            SetOutputValues(node, result);

            context.OnNodeExecuted(node, result);

            // 执行后续节点
            await ExecuteNextNodesAsync(context, node);
        }
        catch (Exception ex)
        {
            context.OnExecutionError(node, ex);
        }
    }

    private async Task<Dictionary<string, object>> CollectInputValuesAsync(
        BlueprintExecutionContext context, BlueprintNode node)
    {
        var inputValues = new Dictionary<string, object>();

        foreach (var inputPin in node.InputPins.Where(p => !p.IsExecutionPin))
        {
            object value = null;

            if (inputPin.Connections.Any())
            {
                // 从连接的输出引脚获取值
                var connection = inputPin.Connections.First();
                var sourcePin = connection.SourcePin;
                
                // 如果源节点还没执行，先执行源节点
                if (!sourcePin.Node.IsExecuted)
                {
                    await ExecuteNodeAsync(context, sourcePin.Node);
                }

                value = sourcePin.Value;
            }
            else
            {
                // 使用默认值
                value = inputPin.DefaultValue;
            }

            // 类型转换
            if (value != null && value.GetType() != inputPin.DataType)
            {
                value = ConvertValue(value, inputPin.DataType);
            }

            inputValues[inputPin.Name] = value;
        }

        return inputValues;
    }

    private async Task<object> InvokeNodeMethodAsync(BlueprintExecutionContext context, 
        BlueprintNode node, Dictionary<string, object> inputValues)
    {
        var method = node.NodeDefinition.Methods.First(); // 简化处理，假设每个节点只有一个方法
        var parameters = new List<object>();

        // 准备方法参数
        foreach (var parameter in method.Method.GetParameters())
        {
            if (inputValues.TryGetValue(parameter.Name, out var value))
            {
                parameters.Add(value);
            }
            else if (parameter.HasDefaultValue)
            {
                parameters.Add(parameter.DefaultValue);
            }
            else
            {
                parameters.Add(GetDefaultValue(parameter.ParameterType));
            }
        }

        // 调用方法
        object result;
        if (method.IsAsync)
        {
            var task = (Task)method.Method.Invoke(null, parameters.ToArray());
            await task;
            
            // 获取异步方法的返回值
            if (task.GetType().IsGenericType)
            {
                var property = task.GetType().GetProperty("Result");
                result = property?.GetValue(task);
            }
            else
            {
                result = null;
            }
        }
        else
        {
            result = method.Method.Invoke(null, parameters.ToArray());
        }

        return result;
    }

    private void SetOutputValues(BlueprintNode node, object result)
    {
        if (result != null)
        {
            var returnPin = node.OutputPins.FirstOrDefault(p => p.Name == "ReturnValue");
            if (returnPin != null)
            {
                returnPin.Value = result;
            }
        }

        node.IsExecuted = true;
    }

    private async Task ExecuteNextNodesAsync(BlueprintExecutionContext context, BlueprintNode node)
    {
        var executionOutputPin = node.OutputPins.FirstOrDefault(p => p.IsExecutionPin);
        if (executionOutputPin?.Connections.Any() == true)
        {
            var tasks = executionOutputPin.Connections
                .Select(c => ExecuteNodeAsync(context, c.TargetPin.Node));
            
            await Task.WhenAll(tasks);
        }
    }

    private object ConvertValue(object value, Type targetType)
    {
        try
        {
            return Convert.ChangeType(value, targetType);
        }
        catch
        {
            return GetDefaultValue(targetType);
        }
    }

    private object GetDefaultValue(Type type)
    {
        return type.IsValueType ? Activator.CreateInstance(type) : null;
    }

    public void PauseExecution(Guid executionId)
    {
        if (_activeExecutions.TryGetValue(executionId, out var context))
        {
            context.Pause();
        }
    }

    public void ResumeExecution(Guid executionId)
    {
        if (_activeExecutions.TryGetValue(executionId, out var context))
        {
            context.Resume();
        }
    }

    public void StopExecution(Guid executionId)
    {
        if (_activeExecutions.TryGetValue(executionId, out var context))
        {
            context.Stop();
        }
    }

    public IEnumerable<BlueprintExecutionContext> GetActiveExecutions()
    {
        return _activeExecutions.Values.ToList();
    }
}
```

### 2. 状态管理

#### 执行状态跟踪
```csharp
public class ExecutionStateManager : IExecutionStateManager
{
    private readonly Dictionary<Guid, ExecutionState> _executionStates = new();

    public void CreateExecutionState(Guid executionId, BlueprintModel blueprint)
    {
        var state = new ExecutionState
        {
            ExecutionId = executionId,
            Blueprint = blueprint,
            Status = ExecutionStatus.Created,
            CreatedAt = DateTime.Now,
            NodeStates = blueprint.Nodes.ToDictionary(n => n.Id, n => new NodeExecutionState
            {
                NodeId = n.Id,
                Status = NodeExecutionStatus.Pending
            })
        };

        _executionStates[executionId] = state;
    }

    public void UpdateNodeState(Guid executionId, string nodeId, NodeExecutionStatus status, 
        object result = null, Exception error = null)
    {
        if (_executionStates.TryGetValue(executionId, out var state) &&
            state.NodeStates.TryGetValue(nodeId, out var nodeState))
        {
            nodeState.Status = status;
            nodeState.Result = result;
            nodeState.Error = error;
            nodeState.UpdatedAt = DateTime.Now;

            // 更新整体执行状态
            UpdateExecutionStatus(state);
        }
    }

    private void UpdateExecutionStatus(ExecutionState state)
    {
        var nodeStates = state.NodeStates.Values.ToList();

        if (nodeStates.Any(ns => ns.Status == NodeExecutionStatus.Error))
        {
            state.Status = ExecutionStatus.Error;
        }
        else if (nodeStates.All(ns => ns.Status == NodeExecutionStatus.Completed))
        {
            state.Status = ExecutionStatus.Completed;
        }
        else if (nodeStates.Any(ns => ns.Status == NodeExecutionStatus.Executing))
        {
            state.Status = ExecutionStatus.Running;
        }
    }

    public ExecutionState GetExecutionState(Guid executionId)
    {
        return _executionStates.TryGetValue(executionId, out var state) ? state : null;
    }

    public void RemoveExecutionState(Guid executionId)
    {
        _executionStates.Remove(executionId);
    }
}

public class ExecutionState
{
    public Guid ExecutionId { get; set; }
    public BlueprintModel Blueprint { get; set; }
    public ExecutionStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Dictionary<string, NodeExecutionState> NodeStates { get; set; }
}

public class NodeExecutionState
{
    public string NodeId { get; set; }
    public NodeExecutionStatus Status { get; set; }
    public object Result { get; set; }
    public Exception Error { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public enum ExecutionStatus
{
    Created,
    Running,
    Paused,
    Completed,
    Error,
    Cancelled
}

public enum NodeExecutionStatus
{
    Pending,
    Executing,
    Completed,
    Error,
    Skipped
}
```

---

## 🔧 节点库扩展

### 1. 自定义节点开发

#### 运动控制节点库示例
```csharp
[BlueprintNode("运动控制", "轴控制", Description = "提供轴运动控制功能")]
public static class MotionControlLibrary
{
    [BlueprintMethod("轴使能", IsAsync = true)]
    public static async Task<bool> EnableAxis(
        [BlueprintPin(PinDirection.Input, "轴对象")] IAxis axis,
        [BlueprintPin(PinDirection.Input, "使能状态")] bool enable = true)
    {
        if (axis == null)
            throw new ArgumentNullException(nameof(axis));

        try
        {
            if (enable)
            {
                await axis.EnableAsync();
            }
            else
            {
                await axis.DisableAsync();
            }

            return axis.IsEnabled;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"轴使能操作失败: {ex.Message}");
            return false;
        }
    }

    [BlueprintMethod("绝对定位", IsAsync = true)]
    public static async Task<bool> MoveAbsolute(
        [BlueprintPin(PinDirection.Input, "轴对象")] IAxis axis,
        [BlueprintPin(PinDirection.Input, "目标位置")] double position,
        [BlueprintPin(PinDirection.Input, "速度")] double velocity = 100.0,
        [BlueprintPin(PinDirection.Input, "加速度")] double acceleration = 1000.0)
    {
        if (axis == null)
            throw new ArgumentNullException(nameof(axis));

        try
        {
            var moveParams = new MoveParameters
            {
                Position = position,
                Velocity = velocity,
                Acceleration = acceleration,
                MoveType = MoveType.Absolute
            };

            await axis.MoveAsync(moveParams);
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"绝对定位失败: {ex.Message}");
            return false;
        }
    }

    [BlueprintMethod("回原点", IsAsync = true)]
    public static async Task<bool> HomeAxis(
        [BlueprintPin(PinDirection.Input, "轴对象")] IAxis axis,
        [BlueprintPin(PinDirection.Input, "回零方式")] HomeMethod method = HomeMethod.NegativeLimit)
    {
        if (axis == null)
            throw new ArgumentNullException(nameof(axis));

        try
        {
            await axis.HomeAsync(method);
            return axis.IsHomed;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"回原点失败: {ex.Message}");
            return false;
        }
    }

    [BlueprintMethod("获取轴状态", ExecutionMode = ExecutionMode.Pure)]
    public static AxisStatus GetAxisStatus(
        [BlueprintPin(PinDirection.Input, "轴对象")] IAxis axis)
    {
        return axis?.Status ?? new AxisStatus();
    }

    [BlueprintMethod("等待运动完成", IsAsync = true)]
    public static async Task<bool> WaitForMotionComplete(
        [BlueprintPin(PinDirection.Input, "轴对象")] IAxis axis,
        [BlueprintPin(PinDirection.Input, "超时时间(ms)")] int timeoutMs = 30000)
    {
        if (axis == null)
            return false;

        var startTime = DateTime.Now;
        while (axis.IsMoving)
        {
            if ((DateTime.Now - startTime).TotalMilliseconds > timeoutMs)
                return false;

            await Task.Delay(10);
        }

        return true;
    }
}
```

### 2. 参数配置系统

#### 节点参数配置器
```csharp
public class NodeParameterConfigurator
{
    public NodeParameterConfiguration CreateConfiguration(NodeDefinition nodeDefinition)
    {
        var config = new NodeParameterConfiguration
        {
            NodeType = nodeDefinition.NodeType,
            DisplayName = nodeDefinition.DisplayName,
            Parameters = new List<ParameterConfiguration>()
        };

        foreach (var method in nodeDefinition.Methods)
        {
            foreach (var pin in method.InputPins.Where(p => !p.IsExecutionPin))
            {
                var paramConfig = new ParameterConfiguration
                {
                    Name = pin.Name,
                    DisplayName = pin.DisplayName,
                    DataType = pin.DataType,
                    DefaultValue = pin.DefaultValue,
                    IsOptional = pin.IsOptional,
                    ValidationRules = CreateValidationRules(pin)
                };

                config.Parameters.Add(paramConfig);
            }
        }

        return config;
    }

    private List<ValidationRule> CreateValidationRules(PinDefinition pin)
    {
        var rules = new List<ValidationRule>();

        // 基于数据类型创建验证规则
        if (pin.DataType == typeof(int) || pin.DataType == typeof(double))
        {
            rules.Add(new NumericRangeRule());
        }
        else if (pin.DataType == typeof(string))
        {
            rules.Add(new StringLengthRule());
        }

        return rules;
    }
}

public class NodeParameterConfiguration
{
    public Type NodeType { get; set; }
    public string DisplayName { get; set; }
    public List<ParameterConfiguration> Parameters { get; set; }
}

public class ParameterConfiguration
{
    public string Name { get; set; }
    public string DisplayName { get; set; }
    public Type DataType { get; set; }
    public object DefaultValue { get; set; }
    public bool IsOptional { get; set; }
    public List<ValidationRule> ValidationRules { get; set; }
}
```

### 3. UI自动生成

#### 节点视图生成器
```csharp
public class NodeViewGenerator
{
    public FrameworkElement GenerateNodeView(BlueprintNode node)
    {
        var nodeView = new Border
        {
            Background = GetNodeBackgroundBrush(node),
            BorderBrush = Brushes.Gray,
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(5),
            Margin = new Thickness(2)
        };

        var mainGrid = new Grid();
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

        // 标题栏
        var titleBar = CreateTitleBar(node);
        Grid.SetRow(titleBar, 0);
        mainGrid.Children.Add(titleBar);

        // 内容区域
        var contentArea = CreateContentArea(node);
        Grid.SetRow(contentArea, 1);
        mainGrid.Children.Add(contentArea);

        nodeView.Child = mainGrid;
        return nodeView;
    }

    private FrameworkElement CreateTitleBar(BlueprintNode node)
    {
        var titleBar = new Border
        {
            Background = GetTitleBarBrush(node),
            Padding = new Thickness(8, 4)
        };

        var titleText = new TextBlock
        {
            Text = node.DisplayName,
            Foreground = Brushes.White,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        titleBar.Child = titleText;
        return titleBar;
    }

    private FrameworkElement CreateContentArea(BlueprintNode node)
    {
        var contentGrid = new Grid();
        contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // 输入引脚
        var inputPinsPanel = CreateInputPinsPanel(node);
        Grid.SetColumn(inputPinsPanel, 0);
        contentGrid.Children.Add(inputPinsPanel);

        // 节点内容
        var nodeContent = CreateNodeContent(node);
        Grid.SetColumn(nodeContent, 1);
        contentGrid.Children.Add(nodeContent);

        // 输出引脚
        var outputPinsPanel = CreateOutputPinsPanel(node);
        Grid.SetColumn(outputPinsPanel, 2);
        contentGrid.Children.Add(outputPinsPanel);

        return contentGrid;
    }

    private FrameworkElement CreateInputPinsPanel(BlueprintNode node)
    {
        var panel = new StackPanel { Orientation = Orientation.Vertical };

        foreach (var pin in node.InputPins)
        {
            var pinView = CreatePinView(pin, PinDirection.Input);
            panel.Children.Add(pinView);
        }

        return panel;
    }

    private FrameworkElement CreateOutputPinsPanel(BlueprintNode node)
    {
        var panel = new StackPanel { Orientation = Orientation.Vertical };

        foreach (var pin in node.OutputPins)
        {
            var pinView = CreatePinView(pin, PinDirection.Output);
            panel.Children.Add(pinView);
        }

        return panel;
    }

    private FrameworkElement CreatePinView(BlueprintPin pin, PinDirection direction)
    {
        var pinGrid = new Grid { Margin = new Thickness(2) };
        pinGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        pinGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        // 引脚连接点
        var pinConnector = new Ellipse
        {
            Width = 12,
            Height = 12,
            Fill = GetPinBrush(pin),
            Stroke = Brushes.Black,
            StrokeThickness = 1
        };

        // 引脚标签
        var pinLabel = new TextBlock
        {
            Text = pin.DisplayName,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(4, 0)
        };

        if (direction == PinDirection.Input)
        {
            Grid.SetColumn(pinConnector, 0);
            Grid.SetColumn(pinLabel, 1);
            pinLabel.HorizontalAlignment = HorizontalAlignment.Left;
        }
        else
        {
            Grid.SetColumn(pinLabel, 0);
            Grid.SetColumn(pinConnector, 1);
            pinLabel.HorizontalAlignment = HorizontalAlignment.Right;
        }

        pinGrid.Children.Add(pinConnector);
        pinGrid.Children.Add(pinLabel);

        return pinGrid;
    }

    private FrameworkElement CreateNodeContent(BlueprintNode node)
    {
        // 根据节点类型创建不同的内容
        if (node.NodeDefinition.Methods.Any(m => m.ExecutionMode == ExecutionMode.Event))
        {
            return CreateEventNodeContent(node);
        }
        else if (node.NodeDefinition.Methods.Any(m => m.ExecutionMode == ExecutionMode.Pure))
        {
            return CreatePureFunctionContent(node);
        }
        else
        {
            return CreateDefaultContent(node);
        }
    }

    private FrameworkElement CreateEventNodeContent(BlueprintNode node)
    {
        return new TextBlock
        {
            Text = "EVENT",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontStyle = FontStyles.Italic,
            Foreground = Brushes.Orange
        };
    }

    private FrameworkElement CreatePureFunctionContent(BlueprintNode node)
    {
        return new TextBlock
        {
            Text = "PURE",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontStyle = FontStyles.Italic,
            Foreground = Brushes.LightBlue
        };
    }

    private FrameworkElement CreateDefaultContent(BlueprintNode node)
    {
        return new TextBlock
        {
            Text = node.NodeDefinition.Description ?? "",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            TextWrapping = TextWrapping.Wrap,
            MaxWidth = 150
        };
    }

    private Brush GetNodeBackgroundBrush(BlueprintNode node)
    {
        var category = node.NodeDefinition.Category;
        return category switch
        {
            "运动控制" => new SolidColorBrush(Color.FromRgb(100, 150, 200)),
            "数字IO" => new SolidColorBrush(Color.FromRgb(150, 100, 200)),
            "逻辑运算" => new SolidColorBrush(Color.FromRgb(200, 150, 100)),
            _ => new SolidColorBrush(Color.FromRgb(128, 128, 128))
        };
    }

    private Brush GetTitleBarBrush(BlueprintNode node)
    {
        var baseBrush = GetNodeBackgroundBrush(node) as SolidColorBrush;
        var color = baseBrush.Color;
        
        // 标题栏颜色稍深一些
        var darkerColor = Color.FromRgb(
            (byte)(color.R * 0.8),
            (byte)(color.G * 0.8),
            (byte)(color.B * 0.8));

        return new SolidColorBrush(darkerColor);
    }

    private Brush GetPinBrush(BlueprintPin pin)
    {
        if (pin.IsExecutionPin)
            return Brushes.White;

        return pin.DataType switch
        {
            var t when t == typeof(bool) => Brushes.Red,
            var t when t == typeof(int) || t == typeof(double) || t == typeof(float) => Brushes.Green,
            var t when t == typeof(string) => Brushes.Magenta,
            _ => Brushes.Gray
        };
    }
}
```

---

## 📋 蓝图系统检查清单

### 节点发现
- [ ] 反射扫描机制
- [ ] 特性标记系统
- [ ] 动态插件加载
- [ ] 节点分类管理
- [ ] 版本兼容性检查

### 连接验证
- [ ] 类型兼容性检查
- [ ] 执行流验证
- [ ] 循环依赖检测
- [ ] 自定义验证规则
- [ ] 实时验证反馈

### 执行引擎
- [ ] 异步执行框架
- [ ] 状态管理系统
- [ ] 错误处理机制
- [ ] 调试支持
- [ ] 性能监控

### 节点扩展
- [ ] 自定义节点开发
- [ ] 参数配置系统
- [ ] UI自动生成
- [ ] 节点库管理
- [ ] 文档生成

### 性能优化
- [ ] 执行路径优化
- [ ] 内存管理
- [ ] 并发执行
- [ ] 缓存机制
- [ ] 资源池化

---

## 🎯 总结

蓝图系统的核心价值在于**可视化编程**和**模块化扩展**：

1. **节点发现**：自动化的节点扫描和分类管理
2. **连接验证**：严格的类型检查和规则验证
3. **执行引擎**：高效的异步执行和状态管理
4. **扩展机制**：灵活的节点库和UI生成系统

通过这些技术的有机结合，构建出一个功能强大、易于扩展的可视化编程环境，为工业自动化领域提供了直观、高效的编程工具。