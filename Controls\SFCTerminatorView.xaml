<UserControl
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:PC_Control2.Demo.ViewModels"
             xmlns:controls="clr-namespace:PC_Control2.Demo.Controls"
             xmlns:models="clr-namespace:PC_Control2.Demo.Models"
             xmlns:config="clr-namespace:PC_Control2.Demo.Configuration"
             xmlns:av="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="av" x:Class="PC_Control2.Demo.Controls.SFCTerminatorView"
             Width="50"
             Height="50">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/SFCBlueprintStyles.xaml"/>
                <ResourceDictionary Source="../Styles/Controls/SFCTerminatorStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 布尔值到可见性转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- 主画布容器 -->
    <Canvas x:Name="MainCanvas"
            Background="Transparent"
            ClipToBounds="False">

        <!-- Canvas样式定义 -->
        <Canvas.Style>
            <Style TargetType="Canvas">
                <Style.Triggers>
                    <!-- 整体选中状态 -->
                    <DataTrigger Binding="{Binding IsSelected}" Value="True">
                        <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                    </DataTrigger>
                    <!-- 整体鼠标悬停效果 -->
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        <Setter Property="Opacity" Value="0.9"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </Canvas.Style>

        <!-- 右键菜单 -->
        <Canvas.ContextMenu>
            <ContextMenu Style="{StaticResource BlueprintContextMenuStyle}">
                <MenuItem Header="编辑终止元素"
                         Command="{Binding EditPropertiesCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="⚙"/>
                <Separator/>
                <MenuItem Header="复制终止元素"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="📋"/>
                <MenuItem Header="删除终止元素"
                         Command="{Binding DeleteCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="🗑"/>
                <Separator/>
                <MenuItem Header="属性"
                         Command="{Binding EditPropertiesCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="⚙"/>
            </ContextMenu>
        </Canvas.ContextMenu>

        <!-- 透明交互层 - 覆盖整个顺控器终止区域 -->
        <Rectangle x:Name="InteractionLayer"
                   Width="50"
                   Height="50"
                   Canvas.Left="0"
                   Canvas.Top="0"
                   Fill="Transparent"
                   Cursor="Hand"
                   Panel.ZIndex="100">
            <Rectangle.Style>
                <Style TargetType="Rectangle">
                    <Style.Triggers>
                        <!-- 选中状态视觉反馈 -->
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedBorderBrush}"/>
                            <Setter Property="StrokeThickness" Value="2"/>
                            <Setter Property="StrokeDashArray" Value="3,2"/>
                            <Setter Property="Opacity" Value="0.3"/>
                        </DataTrigger>
                        <!-- 悬停状态视觉反馈 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Stroke" Value="#FF00FF00"/>
                            <Setter Property="StrokeThickness" Value="1"/>
                            <Setter Property="StrokeDashArray" Value="2,1"/>
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 顶部连接点 - 输入连接点 -->
        <controls:SFCConnectPoint x:Name="TopConnectPoint"
                 Canvas.Left="20" Canvas.Top="6"
                 HorizontalAlignment="Left" VerticalAlignment="Center"
                 PointType="Input" Index="0" ElementId="{Binding Id}" ElementType="Step">
            <controls:SFCConnectPoint.Style>
                <Style TargetType="{x:Type controls:SFCConnectPoint}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </controls:SFCConnectPoint.Style>
        </controls:SFCConnectPoint>

        <!-- 垂直线段 - 从连接点向下延伸 -->
        <Rectangle x:Name="VerticalLine"
                   Canvas.Left="23.5" Canvas.Top="15"
                   Width="3" Height="20"
                   Fill="#FF888888" HorizontalAlignment="Left" VerticalAlignment="Center">
            <Rectangle.Style>
                <Style TargetType="{x:Type Rectangle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Fill" Value="#FF4A90E2"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 水平横线 - 位于垂直线段底部 -->
        <Rectangle x:Name="HorizontalLine"
                   Canvas.Left="18.5" Canvas.Top="35"
                   Width="13" Height="4.5"
                   Fill="#FF888888">
            <Rectangle.Style>
                <Style TargetType="{x:Type Rectangle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Fill" Value="#FF4A90E2"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 标签文本 - 显示"顺控器终止" -->
        <TextBlock x:Name="TerminatorLabel" Canvas.Top="40"
                   Width="50"
                   Text="顺控器终止"
                   FontSize="8"
                   FontWeight="SemiBold"
                   Foreground="#FF888888"
                   HorizontalAlignment="Left"
                   TextAlignment="Center"
                   TextTrimming="CharacterEllipsis" VerticalAlignment="Center">
            <TextBlock.Style>
                <Style TargetType="{x:Type TextBlock}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Foreground" Value="#FF4A90E2"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </TextBlock.Style>
        </TextBlock>

    </Canvas>
</UserControl>