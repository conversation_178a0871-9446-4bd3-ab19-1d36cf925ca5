# 开源项目复用指导书 v2.0 - SFC-蓝图深度融合版

## 📋 项目背景与设计理念变革

### 复用目标的重新定位
基于SFC-蓝图深度融合的设计理念，将成熟的开源PLC项目的**流程控制思路**和**验证技术**复用到我们的PC_Control2项目中，同时采用C#原生编译器策略，实现最适合PC环境的高性能执行。

### 核心设计理念：职责分离与深度融合
- **SFC**：专注于宏观流程控制和状态管理，完全放弃内部逻辑编辑
- **蓝图**：专注于微观逻辑实现和复杂计算，承担所有逻辑编辑功能
- **统一执行**：运行时SFC和蓝图深度融合，无缝协作

### 选定的开源项目与新定位
1. **Beremiz** - 复用SFC流程控制设计思路和验证规则（不复用内部逻辑编辑）
2. **MatIEC编译器** - 重新定位为验证工具和标准符合性保证（不作为主要编译器）

### 复用原则的重大调整
- **架构融合优先**：所有复用都服务于SFC-蓝图融合架构的建立
- **职责分离明确**：SFC复用流程控制，蓝图承担逻辑实现
- **C#原生编译**：采用C#编译器策略，不编译成ST代码
- **标准符合保证**：通过MatIEC验证确保IEC 61131-3标准符合性

## 🎯 Beremiz SFC流程控制思路复用

### 复用价值的重新分析

#### 为什么仍然选择Beremiz？
- **流程控制成熟度**：在SFC流程控制方面有丰富的工业实践
- **状态管理机制**：完整的SFC状态转换和管理逻辑
- **验证规则体系**：涵盖所有SFC流程控制规范
- **硬件抽象设计**：成熟的硬件抽象层和设备管理

#### 我们复用什么？（重新定义）
- **流程控制数据模型**：SFC元素间的连接和状态管理
- **状态转换逻辑**：步骤激活、转换触发、分支处理
- **验证规则体系**：流程结构的正确性检查
- **硬件抽象机制**：设备管理和I/O处理逻辑

#### 我们不复用什么？（明确排除）
- ❌ **内部逻辑编辑器**：转换条件和步骤动作的编辑界面
- ❌ **表达式解析器**：条件表达式的解析和执行
- ❌ **ST代码生成**：生成结构化文本代码的功能
- ❌ **Python特有实现**：Python语言特有的技术实现

### 核心文件分析（重新聚焦）

#### 重点研究的Beremiz文件（调整后）
```
beremiz/
├── plcopen/
│   ├── structures.py         # SFC流程控制数据模型（重点复用）
│   └── plcopen.py           # IEC标准流程结构（重点复用）
├── editors/
│   ├── SFCViewer.py          # SFC状态管理逻辑（复用思路）
│   └── Viewer.py             # 编辑器框架（参考设计）
├── runtime/
│   ├── PLCObject.py         # 运行时状态管理（复用逻辑）
│   └── ServicePublisher.py  # 硬件抽象（复用设计）
└── util/
    ├── ProcessLogger.py     # 执行监控（复用机制）
    └── BitmapLibrary.py     # 可视化支持（参考实现）
```

#### 不再研究的文件（明确排除）
```
❌ 不复用的部分：
├── editors/
│   ├── CodeFileEditor.py    # 代码编辑器（蓝图替代）
│   └── TextViewer.py        # 文本编辑器（蓝图替代）
├── PLCGenerator.py          # ST代码生成（C#编译器替代）
└── targets/                 # PLC目标平台（PC平台替代）
```

### 分阶段复用策略（重新设计）

#### 第一阶段：流程控制数据模型复用（Week2-3）

**Beremiz的流程控制模型**：
```python
# Beremiz的SFC结构定义（复用思路）
class SFCStep:
    def __init__(self):
        self.name = ""
        self.initial = False
        self.connections = []
        # 注意：不复用action编辑功能

class SFCTransition:
    def __init__(self):
        self.name = ""
        self.condition_reference = ""  # 指向外部条件，不是内部表达式
        self.connections = []
```

**转换为我们的融合模型**：
```csharp
// 基于Beremiz思路的SFC-蓝图融合模型
public class SFCStepModel : SFCElementModelBase
{
    // 复用Beremiz的基础属性
    public string Name { get; set; }
    public bool IsInitial { get; set; }
    public List<SFCConnection> Connections { get; set; }
    
    // 融合架构：关联蓝图而非内部编辑
    public List<string> ActionBlueprintIds { get; set; }  // 替代内部动作编辑
    public List<BlueprintCallInfo> ActionCalls { get; set; }
}

public class SFCTransitionModel : SFCElementModelBase
{
    // 复用Beremiz的基础属性
    public string Name { get; set; }
    public List<SFCConnection> Connections { get; set; }
    
    // 融合架构：关联蓝图而非内部表达式
    public string ConditionBlueprintId { get; set; }  // 替代内部条件编辑
    public BlueprintCallInfo CallInfo { get; set; }
}
```

**复用收益**：
- 获得工业级的SFC流程控制数据结构
- 保持IEC 61131-3标准的流程控制语义
- 为SFC-蓝图融合提供坚实的数据基础

#### 第二阶段：状态管理机制复用（Week8）

**Beremiz的状态管理逻辑**：
```python
# Beremiz的状态转换逻辑（复用思路）
def execute_sfc_cycle(sfc_model):
    active_steps = get_active_steps(sfc_model)
    
    for step in active_steps:
        # 检查出口转换条件（我们改为调用蓝图）
        for transition in step.outgoing_transitions:
            if evaluate_condition(transition.condition):  # 我们改为蓝图调用
                fire_transition(transition)
```

**转换为我们的融合执行**：
```csharp
// 基于Beremiz逻辑的SFC-蓝图融合执行
public class SFCBlueprintStateMachine
{
    public async Task ExecuteSFCCycleAsync(SFCModel sfcModel)
    {
        var activeSteps = GetActiveSteps(sfcModel);
        
        foreach (var step in activeSteps)
        {
            // 执行步骤动作（调用蓝图，不是内部编辑）
            await ExecuteStepActionsAsync(step);
            
            // 检查转换条件（调用蓝图，不是表达式解析）
            foreach (var transition in step.OutgoingTransitions)
            {
                if (await EvaluateTransitionConditionAsync(transition))
                {
                    FireTransition(transition);
                }
            }
        }
    }
    
    private async Task<bool> EvaluateTransitionConditionAsync(SFCTransitionModel transition)
    {
        // 深度融合：直接调用蓝图执行器
        var result = await _blueprintExecutor.ExecuteAsync(
            transition.ConditionBlueprintId, 
            _sharedContext);
            
        return result.GetValue<bool>();
    }
}
```

**复用收益**：
- 获得经过工业验证的SFC状态管理逻辑
- 保持标准的SFC执行语义
- 为SFC-蓝图统一执行提供可靠基础

#### 第三阶段：验证规则体系复用（Week9）

**Beremiz的验证规则**：
```python
# Beremiz的SFC验证逻辑（完全复用）
def validate_sfc_structure(sfc_model):
    errors = []
    
    # 规则1：步骤和转换必须交替
    if not check_step_transition_alternation(sfc_model):
        errors.append("步骤和转换必须交替出现")
    
    # 规则2：必须有且仅有一个初始步骤
    initial_steps = get_initial_steps(sfc_model)
    if len(initial_steps) != 1:
        errors.append("必须有且仅有一个初始步骤")
    
    # 规则3：分支结构必须正确闭合
    if not validate_branch_closure(sfc_model):
        errors.append("分支结构必须正确闭合")
    
    return errors
```

**转换为我们的融合验证**：
```csharp
// 基于Beremiz规则的SFC-蓝图融合验证
public class BeremizStyleSFCValidator : ISFCValidator
{
    public ValidationResult Validate(SFCModel model)
    {
        var result = new ValidationResult();
        
        // 完全复用Beremiz的流程控制验证规则
        ValidateStepTransitionAlternation(model, result);
        ValidateInitialStepUniqueness(model, result);
        ValidateBranchStructure(model, result);
        
        // 新增：SFC-蓝图关联验证
        ValidateBlueprintAssociations(model, result);
        ValidateBlueprintCompatibility(model, result);
        
        return result;
    }
    
    private void ValidateBlueprintAssociations(SFCModel model, ValidationResult result)
    {
        // 验证每个转换都有关联的条件蓝图
        foreach (var transition in model.Transitions)
        {
            if (string.IsNullOrEmpty(transition.ConditionBlueprintId))
            {
                result.AddError("转换必须关联条件蓝图", transition);
            }
        }
    }
}
```

**复用收益**：
- 获得完整的IEC 61131-3标准验证规则
- 确保SFC流程控制的正确性
- 在标准验证基础上扩展SFC-蓝图融合验证

## 🔧 MatIEC编译器重新定位：验证工具与标准保证

### 角色转变：从主编译器到验证工具

#### 原来的定位（v1.0）
- **主要编译器**：直接集成作为编译后端
- **ST代码生成**：将SFC编译为结构化文本代码
- **运行时执行**：编译后的ST代码在MatIEC运行时执行

#### 新的定位（v2.0）
- **验证工具**：确保SFC-蓝图融合后的IEC 61131-3标准符合性
- **兼容性保证**：生成标准IEC代码用于与其他PLC系统交互
- **技术参考**：为C#编译器提供编译优化技术参考

### 重新定位的集成策略

#### 第一层：标准验证工具
```csharp
public class MatIECValidator
{
    // 使用MatIEC验证SFC-蓝图融合的标准符合性
    public async Task<ValidationResult> ValidateIECComplianceAsync(SFCModel sfcModel)
    {
        // 1. 将SFC-蓝图模型转换为MatIEC可理解的格式
        var iecModel = ConvertToIECFormat(sfcModel);
        
        // 2. 调用MatIEC进行标准验证
        var matiecResult = await _matiecProcess.ValidateAsync(iecModel);
        
        // 3. 将验证结果转换回我们的格式
        return ConvertValidationResult(matiecResult);
    }
}
```

#### 第二层：兼容性导出工具
```csharp
public class MatIECExporter
{
    // 使用MatIEC生成标准IEC代码用于导出
    public async Task<string> ExportToIECCodeAsync(SFCModel sfcModel)
    {
        // 1. 将SFC-蓝图逻辑转换为等效的IEC结构
        var iecEquivalent = ConvertSFCBlueprintToIEC(sfcModel);
        
        // 2. 使用MatIEC生成标准代码
        var iecCode = await _matiecProcess.GenerateCodeAsync(iecEquivalent);
        
        // 3. 返回可用于其他PLC系统的标准代码
        return iecCode;
    }
}
```

#### 第三层：技术参考和优化指导
```csharp
public class MatIECOptimizationReference
{
    // 学习MatIEC的编译优化技术，改进我们的C#编译器
    public CompilerOptimizationStrategy AnalyzeMatIECOptimizations()
    {
        return new CompilerOptimizationStrategy
        {
            // 从MatIEC学习的优化技术
            DeadCodeElimination = true,
            ConstantFolding = true,
            LoopOptimization = true,
            InlineExpansion = true
        };
    }
}
```

### 集成收益的重新评估

#### 技术收益
- **标准符合性保证**：100%确保符合IEC 61131-3标准
- **兼容性支持**：支持与传统PLC系统的互操作
- **技术学习**：从成熟编译器学习优化技术

#### 架构收益
- **职责清晰**：MatIEC专注验证，C#编译器专注执行
- **风险降低**：不依赖外部编译器作为主要执行路径
- **性能优化**：C#原生编译获得最佳PC执行性能

## 🚀 C#原生编译器策略

### 编译器架构设计

#### 统一编译流程
```
SFC模型 + 蓝图模型 → SFC-蓝图统一编译器 → C#程序集 → .NET运行时执行
```

#### 核心编译组件
```csharp
public class UnifiedSFCBlueprintCompiler
{
    public async Task<CompilationResult> CompileAsync(SFCModel sfcModel)
    {
        // 1. 收集所有关联的蓝图
        var referencedBlueprints = await CollectReferencedBlueprintsAsync(sfcModel);
        
        // 2. 编译蓝图为C#方法
        var blueprintMethods = await CompileBlueprintsToMethodsAsync(referencedBlueprints);
        
        // 3. 生成SFC主控制逻辑
        var sfcMainMethod = await CompileSFCToMainMethodAsync(sfcModel);
        
        // 4. 组合为统一的C#类
        var unifiedClass = GenerateUnifiedClass(blueprintMethods, sfcMainMethod);
        
        // 5. 编译为.NET程序集
        return await CompileToAssemblyAsync(unifiedClass);
    }
}
```

### 与MatIEC的协作关系

#### 开发阶段
1. **设计验证**：使用MatIEC验证SFC-蓝图设计的标准符合性
2. **编译执行**：使用C#编译器生成高性能执行代码
3. **调试优化**：使用.NET调试器进行源码级调试

#### 部署阶段
1. **PC执行**：使用C#编译的程序集在PC上高性能执行
2. **PLC导出**：使用MatIEC生成标准IEC代码导出到传统PLC
3. **兼容性保证**：确保两种执行方式的逻辑一致性

## 📊 复用策略总结

### 复用内容清单

#### 从Beremiz复用
✅ **流程控制数据模型**：SFC元素结构和连接关系
✅ **状态管理机制**：SFC执行周期和状态转换逻辑
✅ **验证规则体系**：完整的IEC 61131-3验证规则
✅ **硬件抽象设计**：设备管理和I/O处理机制

#### 从MatIEC复用
✅ **标准验证功能**：IEC 61131-3符合性检查
✅ **兼容性导出**：标准IEC代码生成
✅ **优化技术参考**：编译优化策略学习

### 不复用内容清单

#### 明确不复用
❌ **内部逻辑编辑器**：转换条件和步骤动作的编辑界面
❌ **表达式解析器**：条件表达式的解析和执行
❌ **ST代码生成**：作为主要执行路径的ST代码生成
❌ **Python/C特有实现**：语言特有的技术实现细节

### 预期收益

#### 开发效率提升
- **设计时间减少60%**：复用成熟的流程控制设计模式
- **验证开发减少80%**：直接移植工业级验证规则
- **调试功能减少70%**：利用.NET原生调试能力

#### 质量保证提升
- **IEC标准100%符合**：基于标准参考实现和验证工具
- **工业级验证**：复用Beremiz的完整验证体系
- **高性能执行**：C#原生编译的PC优化执行

#### 技术风险降低
- **设计风险**：基于成熟项目，避免重复踩坑
- **兼容性风险**：符合工业标准，确保互操作性
- **维护风险**：利用开源社区的持续维护和.NET生态
- **性能风险**：C#原生编译确保最佳PC执行性能

## 🛠️ 详细实施方案

### Beremiz流程控制逻辑移植

#### 核心算法移植示例

**Beremiz的分支处理逻辑**：
```python
# Beremiz的分支执行逻辑（移植参考）
def execute_parallel_branch(branch_steps):
    """并行分支：所有路径都必须完成"""
    all_completed = True
    for step in branch_steps:
        if not step.is_completed():
            all_completed = False
            break
    return all_completed

def execute_selection_branch(branch_steps):
    """选择分支：任意一个路径完成即可"""
    for step in branch_steps:
        if step.is_completed():
            return True
    return False
```

**转换为我们的C#实现**：
```csharp
// 基于Beremiz逻辑的分支处理
public class SFCBranchExecutor
{
    public bool ExecuteParallelBranch(List<SFCStepModel> branchSteps, SharedExecutionContext context)
    {
        // 完全复用Beremiz的并行分支逻辑
        foreach (var step in branchSteps)
        {
            if (!IsStepCompleted(step, context))
            {
                return false;  // 所有路径都必须完成
            }
        }
        return true;
    }

    public bool ExecuteSelectionBranch(List<SFCStepModel> branchSteps, SharedExecutionContext context)
    {
        // 完全复用Beremiz的选择分支逻辑
        foreach (var step in branchSteps)
        {
            if (IsStepCompleted(step, context))
            {
                return true;  // 任意一个路径完成即可
            }
        }
        return false;
    }

    private bool IsStepCompleted(SFCStepModel step, SharedExecutionContext context)
    {
        // 融合架构：通过蓝图判断步骤完成状态
        return context.ActiveSteps.Contains(step.Id) &&
               AllStepActionsCompleted(step, context);
    }
}
```

#### 硬件抽象层移植

**Beremiz的硬件抽象设计**：
```python
# Beremiz的硬件抽象接口（移植参考）
class PLCObject:
    def __init__(self):
        self.PLCStatus = "Stopped"
        self.variables = {}

    def StartPLC(self):
        self.PLCStatus = "Started"

    def StopPLC(self):
        self.PLCStatus = "Stopped"

    def GetPLCstatus(self):
        return self.PLCStatus

    def SetTraceVariablesList(self, variables):
        self.variables = variables
```

**转换为我们的硬件抽象扩展**：
```csharp
// 基于Beremiz设计的硬件抽象扩展
public interface IEnhancedHardwareManager : IHardwareService
{
    // 复用Beremiz的PLC状态管理概念
    PLCStatus GetPLCStatus();
    Task<bool> StartPLCAsync();
    Task<bool> StopPLCAsync();

    // 复用Beremiz的变量跟踪机制
    Task SetTraceVariablesAsync(List<string> variableNames);
    Task<Dictionary<string, object>> GetTraceVariablesAsync();

    // 扩展：SFC-蓝图特有功能
    Task<bool> ExecuteSFCCycleAsync(SharedExecutionContext context);
    Task<bool> ExecuteBlueprintAsync(string blueprintId, Dictionary<string, object> inputs);
}

public class BeremizStyleHardwareManager : IEnhancedHardwareManager
{
    private PLCStatus _plcStatus = PLCStatus.Stopped;
    private readonly Dictionary<string, object> _traceVariables = new();

    public PLCStatus GetPLCStatus() => _plcStatus;

    public async Task<bool> StartPLCAsync()
    {
        // 复用Beremiz的启动逻辑
        _plcStatus = PLCStatus.Starting;

        // 初始化硬件连接
        await InitializeHardwareConnectionsAsync();

        _plcStatus = PLCStatus.Started;
        return true;
    }

    public async Task<bool> ExecuteSFCCycleAsync(SharedExecutionContext context)
    {
        // 融合架构：执行SFC-蓝图统一周期
        if (_plcStatus != PLCStatus.Started) return false;

        // 读取输入
        await ReadInputVariablesAsync(context);

        // 执行SFC-蓝图逻辑（由统一执行引擎处理）
        // 这里只负责硬件I/O部分

        // 写入输出
        await WriteOutputVariablesAsync(context);

        return true;
    }
}
```

### MatIEC验证工具集成

#### 验证管道设计
```csharp
public class MatIECValidationPipeline
{
    private readonly IMatIECProcess _matiecProcess;
    private readonly ISFCBlueprintConverter _converter;

    public async Task<ComprehensiveValidationResult> ValidateAsync(SFCModel sfcModel)
    {
        var result = new ComprehensiveValidationResult();

        // 第一步：SFC-蓝图融合验证
        var fusionValidation = await ValidateSFCBlueprintFusionAsync(sfcModel);
        result.FusionValidation = fusionValidation;

        // 第二步：IEC标准符合性验证
        var iecValidation = await ValidateIECComplianceAsync(sfcModel);
        result.IECValidation = iecValidation;

        // 第三步：兼容性验证
        var compatibilityValidation = await ValidateCompatibilityAsync(sfcModel);
        result.CompatibilityValidation = compatibilityValidation;

        return result;
    }

    private async Task<ValidationResult> ValidateIECComplianceAsync(SFCModel sfcModel)
    {
        // 1. 将SFC-蓝图模型转换为MatIEC可理解的等效模型
        var iecEquivalentModel = await _converter.ConvertToIECEquivalentAsync(sfcModel);

        // 2. 使用MatIEC进行标准验证
        var matiecResult = await _matiecProcess.ValidateAsync(iecEquivalentModel);

        // 3. 将MatIEC的验证结果映射回我们的模型
        return MapMatIECValidationResult(matiecResult, sfcModel);
    }
}
```

#### SFC-蓝图到IEC等效模型转换
```csharp
public class SFCBlueprintToIECConverter
{
    public async Task<IECModel> ConvertToIECEquivalentAsync(SFCModel sfcModel)
    {
        var iecModel = new IECModel();

        // 转换SFC结构（保持不变）
        iecModel.Steps = ConvertSteps(sfcModel.Steps);
        iecModel.Transitions = ConvertTransitions(sfcModel.Transitions);
        iecModel.Branches = ConvertBranches(sfcModel.Branches);

        // 关键：将蓝图逻辑转换为等效的IEC表达式
        foreach (var transition in sfcModel.Transitions)
        {
            var blueprint = GetBlueprint(transition.ConditionBlueprintId);
            var equivalentExpression = await ConvertBlueprintToIECExpressionAsync(blueprint);
            iecModel.Transitions[transition.Id].Condition = equivalentExpression;
        }

        foreach (var step in sfcModel.Steps)
        {
            foreach (var actionBlueprintId in step.ActionBlueprintIds)
            {
                var blueprint = GetBlueprint(actionBlueprintId);
                var equivalentAction = await ConvertBlueprintToIECActionAsync(blueprint);
                iecModel.Steps[step.Id].Actions.Add(equivalentAction);
            }
        }

        return iecModel;
    }

    private async Task<string> ConvertBlueprintToIECExpressionAsync(BlueprintModel blueprint)
    {
        // 将蓝图逻辑转换为等效的IEC表达式
        // 这是一个复杂的转换过程，需要分析蓝图的节点图
        var analyzer = new BlueprintLogicAnalyzer();
        var logicTree = await analyzer.AnalyzeAsync(blueprint);

        var expressionGenerator = new IECExpressionGenerator();
        return expressionGenerator.Generate(logicTree);
    }
}
```

### C#编译器优化策略

#### 编译优化技术
```csharp
public class SFCBlueprintCompilerOptimizer
{
    public CompilationResult OptimizeCompilation(SFCModel sfcModel, List<BlueprintModel> blueprints)
    {
        var optimizedResult = new CompilationResult();

        // 1. 内联优化：简单蓝图直接内联到SFC代码中
        var inlineableBlueprints = IdentifyInlineableBlueprints(blueprints);
        foreach (var blueprint in inlineableBlueprints)
        {
            var inlinedCode = GenerateInlineCode(blueprint);
            optimizedResult.InlinedMethods.Add(blueprint.Id, inlinedCode);
        }

        // 2. 缓存优化：复杂蓝图编译结果缓存
        var cachableBlueprints = IdentifyCachableBlueprints(blueprints);
        foreach (var blueprint in cachableBlueprints)
        {
            var cachedMethod = GetOrCompileCachedMethod(blueprint);
            optimizedResult.CachedMethods.Add(blueprint.Id, cachedMethod);
        }

        // 3. 并行优化：独立蓝图并行编译
        var parallelBlueprints = IdentifyParallelizableBlueprints(blueprints);
        var parallelTasks = parallelBlueprints.Select(CompileBlueprintAsync);
        var parallelResults = await Task.WhenAll(parallelTasks);

        // 4. 死代码消除：移除未使用的蓝图
        var usedBlueprints = AnalyzeBlueprintUsage(sfcModel);
        optimizedResult.DeadCodeElimination = blueprints.Except(usedBlueprints).ToList();

        return optimizedResult;
    }

    private bool IsInlineable(BlueprintModel blueprint)
    {
        // 判断蓝图是否适合内联
        return blueprint.Nodes.Count <= 5 &&
               !blueprint.HasLoops &&
               !blueprint.HasAsyncOperations &&
               blueprint.EstimatedComplexity < ComplexityThreshold.Low;
    }
}
```

#### 运行时性能监控
```csharp
public class SFCBlueprintPerformanceMonitor
{
    private readonly Dictionary<string, PerformanceMetrics> _metrics = new();

    public void StartMonitoring(string elementId)
    {
        _metrics[elementId] = new PerformanceMetrics
        {
            StartTime = DateTime.UtcNow,
            ExecutionCount = 0,
            TotalExecutionTime = TimeSpan.Zero
        };
    }

    public void RecordExecution(string elementId, TimeSpan executionTime)
    {
        if (_metrics.TryGetValue(elementId, out var metrics))
        {
            metrics.ExecutionCount++;
            metrics.TotalExecutionTime += executionTime;
            metrics.AverageExecutionTime = TimeSpan.FromTicks(
                metrics.TotalExecutionTime.Ticks / metrics.ExecutionCount);

            // 检查是否需要优化建议
            if (ShouldSuggestOptimization(metrics))
            {
                GenerateOptimizationSuggestion(elementId, metrics);
            }
        }
    }

    private bool ShouldSuggestOptimization(PerformanceMetrics metrics)
    {
        return metrics.ExecutionCount > 1000 &&
               metrics.AverageExecutionTime > TimeSpan.FromMilliseconds(10);
    }

    private void GenerateOptimizationSuggestion(string elementId, PerformanceMetrics metrics)
    {
        var suggestions = new List<string>();

        if (metrics.AverageExecutionTime > TimeSpan.FromMilliseconds(50))
        {
            suggestions.Add("考虑将此蓝图编译为内联代码");
        }

        if (metrics.ExecutionCount > 10000)
        {
            suggestions.Add("考虑使用JIT编译优化");
        }

        OnOptimizationSuggested?.Invoke(elementId, suggestions);
    }
}
```

## 🎯 实施时间表和里程碑

### Phase 1: Beremiz流程控制复用（Week2-8）
- **Week2-3**：流程控制数据模型移植
- **Week5-6**：分支处理逻辑移植
- **Week7-8**：状态管理机制移植
- **Week9**：验证规则体系移植

### Phase 2: MatIEC验证工具集成（Week9-13）
- **Week9**：MatIEC验证管道建立
- **Week10-11**：SFC-蓝图到IEC转换器开发
- **Week12-13**：兼容性导出功能实现

### Phase 3: C#编译器优化（Week10-16）
- **Week10-12**：基础编译器开发
- **Week13-14**：编译优化策略实现
- **Week15-16**：性能监控和调优

### 关键里程碑
- **M1（Week3）**：Beremiz流程控制模型成功移植
- **M2（Week8）**：SFC-蓝图融合状态管理完成
- **M3（Week11）**：MatIEC验证工具集成完成
- **M4（Week14）**：C#编译器基础功能完成
- **M5（Week16）**：完整的SFC-蓝图统一执行系统

## 📊 成功指标和验收标准

### 技术指标
- **编译性能**：SFC-蓝图联合编译时间 < 5秒（1000个元素）
- **运行性能**：转换条件评估时间 < 1ms
- **内存使用**：运行时内存占用 < 200MB
- **标准符合性**：100%通过MatIEC验证

### 功能指标
- **Beremiz复用度**：流程控制逻辑复用率 > 80%
- **验证覆盖率**：IEC标准验证规则覆盖率 > 95%
- **编译成功率**：SFC-蓝图编译成功率 > 99%
- **兼容性**：与传统PLC系统兼容性 > 90%

### 质量指标
- **代码质量**：复用代码质量评分 > 8.5/10
- **文档完整性**：技术文档完整性 > 95%
- **测试覆盖率**：单元测试覆盖率 > 85%
- **用户满意度**：开发者使用满意度 > 90%

这个更新版的复用指导书完全基于SFC-蓝图深度融合的设计理念，明确了复用的边界和策略，提供了详细的实施方案和技术细节，为项目的成功实施提供了清晰的技术路线图。
