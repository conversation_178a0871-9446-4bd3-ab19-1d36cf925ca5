# PC_Control2 SFC编辑器核心功能实现

## 📋 概述
本文档详细介绍了PC_Control2项目中SFC（顺序功能图）编辑器的核心功能实现，包括元素创建、拖拽交互、连接管理、验证系统和代码生成等关键技术。

---

## 🏗️ SFC编辑器架构

### 核心组件关系
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SFCCanvas     │◄──►│EnhancedSFCVM    │◄──►│   SFCModel      │
│   (画布控件)     │    │  (主视图模型)    │    │  (数据模型)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
    UI交互处理              业务逻辑协调              数据持久化
```

### 文件结构
```
SFC编辑器相关文件:
├── ViewModels/
│   ├── EnhancedSFCViewModel.cs      # 主编辑器ViewModel
│   ├── SFCStepViewModel.cs          # 步骤元素ViewModel
│   ├── SFCTransitionViewModel.cs    # 转换元素ViewModel
│   └── SFCBranchViewModel.cs        # 分支元素ViewModel
├── Controls/
│   ├── SFCCanvas.cs                 # 画布控件
│   ├── SFCStepView.xaml/.cs         # 步骤视图
│   ├── SFCTransitionView.xaml/.cs   # 转换视图
│   └── SFCBranchView.xaml/.cs       # 分支视图
├── Models/
│   ├── SFCModel.cs                  # SFC数据模型
│   └── SFCConnectionManager.cs      # 连接管理器
└── Services/
    ├── SFCValidator.cs              # 验证服务
    └── SFCCodeGenerator.cs          # 代码生成器
```

---

## 🎨 元素创建和管理

### 1. SFC元素类型定义

```csharp
public enum SFCElementType
{
    Step,           // 步骤
    Transition,     // 转换条件
    SelectionBranch,// 选择分支
    ParallelBranch, // 并行分支
    Jump,           // 跳转
    Terminator      // 终止符
}

public abstract class SFCElementViewModel : ViewModelBase
{
    private string _id;
    private string _name;
    private Point _position;
    private bool _isSelected;

    public string Id
    {
        get => _id;
        set => SetProperty(ref _id, value);
    }

    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    public Point Position
    {
        get => _position;
        set => SetProperty(ref _position, value);
    }

    public bool IsSelected
    {
        get => _isSelected;
        set => SetProperty(ref _isSelected, value);
    }

    public abstract SFCElementType ElementType { get; }
}
```

### 2. 步骤元素实现

```csharp
public class SFCStepViewModel : SFCElementViewModel
{
    private string _actionCode;
    private TimeSpan _minTime;
    private TimeSpan _maxTime;
    private ObservableCollection<SFCConnectPointAdapter> _connectPointAdapters;

    public override SFCElementType ElementType => SFCElementType.Step;

    public string ActionCode
    {
        get => _actionCode;
        set => SetProperty(ref _actionCode, value);
    }

    public TimeSpan MinTime
    {
        get => _minTime;
        set => SetProperty(ref _minTime, value);
    }

    public TimeSpan MaxTime
    {
        get => _maxTime;
        set => SetProperty(ref _maxTime, value);
    }

    public ObservableCollection<SFCConnectPointAdapter> ConnectPointAdapters
    {
        get => _connectPointAdapters;
        set => SetProperty(ref _connectPointAdapters, value);
    }

    public SFCStepViewModel()
    {
        Id = Guid.NewGuid().ToString();
        Name = "新步骤";
        ConnectPointAdapters = new ObservableCollection<SFCConnectPointAdapter>();
        InitializeConnectPoints();
    }

    private void InitializeConnectPoints()
    {
        // 步骤有两个连接点：输入(上)和输出(下)
        ConnectPointAdapters.Add(new SFCConnectPointAdapter(
            Id, SFCElementType.Step, ConnectPointDirection.Input, 0, SFCDataFlowType.ControlFlow));
        
        ConnectPointAdapters.Add(new SFCConnectPointAdapter(
            Id, SFCElementType.Step, ConnectPointDirection.Output, 0, SFCDataFlowType.ControlFlow));
    }
}
```

### 3. 转换条件元素实现

```csharp
public class SFCTransitionViewModel : SFCElementViewModel
{
    private string _condition;
    private bool _isAlwaysTrue;
    private ObservableCollection<SFCConnectPointAdapter> _connectPointAdapters;

    public override SFCElementType ElementType => SFCElementType.Transition;

    public string Condition
    {
        get => _condition;
        set => SetProperty(ref _condition, value);
    }

    public bool IsAlwaysTrue
    {
        get => _isAlwaysTrue;
        set
        {
            if (SetProperty(ref _isAlwaysTrue, value))
            {
                if (value)
                    Condition = "1"; // 始终为真
            }
        }
    }

    public ObservableCollection<SFCConnectPointAdapter> ConnectPointAdapters
    {
        get => _connectPointAdapters;
        set => SetProperty(ref _connectPointAdapters, value);
    }

    public SFCTransitionViewModel()
    {
        Id = Guid.NewGuid().ToString();
        Name = "转换条件";
        Condition = "1"; // 默认始终为真
        IsAlwaysTrue = true;
        ConnectPointAdapters = new ObservableCollection<SFCConnectPointAdapter>();
        InitializeConnectPoints();
    }

    private void InitializeConnectPoints()
    {
        // 转换条件有两个连接点：输入(上)和输出(下)
        ConnectPointAdapters.Add(new SFCConnectPointAdapter(
            Id, SFCElementType.Transition, ConnectPointDirection.Input, 0, SFCDataFlowType.ControlFlow));
        
        ConnectPointAdapters.Add(new SFCConnectPointAdapter(
            Id, SFCElementType.Transition, ConnectPointDirection.Output, 0, SFCDataFlowType.ControlFlow));
    }
}
```

### 4. 分支元素实现

```csharp
public class SFCBranchViewModel : SFCElementViewModel
{
    private SFCBranchType _branchType;
    private ObservableCollection<SFCConnectPointAdapter> _connectPointAdapters;

    public override SFCElementType ElementType => SFCElementType.SelectionBranch; // 或ParallelBranch

    public SFCBranchType BranchType
    {
        get => _branchType;
        set
        {
            if (SetProperty(ref _branchType, value))
            {
                OnPropertyChanged(nameof(IsSelectionBranch));
                OnPropertyChanged(nameof(IsParallelBranch));
                InitializeConnectPointAdapters(); // 重新初始化连接点
            }
        }
    }

    public bool IsSelectionBranch => BranchType == SFCBranchType.Selection;
    public bool IsParallelBranch => BranchType == SFCBranchType.Parallel;

    public ObservableCollection<SFCConnectPointAdapter> ConnectPointAdapters
    {
        get => _connectPointAdapters;
        set => SetProperty(ref _connectPointAdapters, value);
    }

    public SFCBranchViewModel(SFCBranchType branchType)
    {
        Id = Guid.NewGuid().ToString();
        BranchType = branchType;
        Name = branchType == SFCBranchType.Selection ? "选择分支" : "并行分支";
        ConnectPointAdapters = new ObservableCollection<SFCConnectPointAdapter>();
        InitializeConnectPointAdapters();
    }

    private void InitializeConnectPointAdapters()
    {
        ConnectPointAdapters.Clear();

        if (BranchType == SFCBranchType.Selection)
        {
            // 选择分支：4个连接点
            ConnectPointAdapters.Add(new SFCConnectPointAdapter(Id, SFCElementType.SelectionBranch, ConnectPointDirection.Input, 0, SFCDataFlowType.ControlFlow));   // 左上
            ConnectPointAdapters.Add(new SFCConnectPointAdapter(Id, SFCElementType.SelectionBranch, ConnectPointDirection.Input, 1, SFCDataFlowType.ControlFlow));   // 左下
            ConnectPointAdapters.Add(new SFCConnectPointAdapter(Id, SFCElementType.SelectionBranch, ConnectPointDirection.Output, 2, SFCDataFlowType.ControlFlow));  // 右上
            ConnectPointAdapters.Add(new SFCConnectPointAdapter(Id, SFCElementType.SelectionBranch, ConnectPointDirection.Output, 3, SFCDataFlowType.ControlFlow));  // 右下
        }
        else if (BranchType == SFCBranchType.Parallel)
        {
            // 并行分支：3个连接点
            ConnectPointAdapters.Add(new SFCConnectPointAdapter(Id, SFCElementType.ParallelBranch, ConnectPointDirection.Output, 0, SFCDataFlowType.ControlFlow)); // 左上（输出）
            ConnectPointAdapters.Add(new SFCConnectPointAdapter(Id, SFCElementType.ParallelBranch, ConnectPointDirection.Input, 1, SFCDataFlowType.ControlFlow));  // 左双线（输入）
            ConnectPointAdapters.Add(new SFCConnectPointAdapter(Id, SFCElementType.ParallelBranch, ConnectPointDirection.Output, 0, SFCDataFlowType.ControlFlow)); // 右双线（输出）
        }
    }
}
```

---

## 🖱️ 拖拽交互实现

### 1. SFCCanvas拖拽处理

```csharp
public class SFCCanvas : Canvas
{
    private bool _isDragging;
    private Point _dragStartPoint;
    private SFCElementViewModel _draggedElement;
    private readonly List<SFCElementViewModel> _selectedElements = new();

    protected override void OnMouseLeftButtonDown(MouseButtonEventArgs e)
    {
        var hitElement = GetElementAtPoint(e.GetPosition(this));
        
        if (hitElement != null)
        {
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            _draggedElement = hitElement;
            
            // 处理多选
            if (!Keyboard.IsKeyDown(Key.LeftCtrl))
            {
                ClearSelection();
            }
            
            SelectElement(hitElement);
            CaptureMouse();
        }
        else
        {
            // 点击空白区域，清除选择
            ClearSelection();
        }

        base.OnMouseLeftButtonDown(e);
    }

    protected override void OnMouseMove(MouseEventArgs e)
    {
        if (_isDragging && _draggedElement != null)
        {
            var currentPoint = e.GetPosition(this);
            var offset = currentPoint - _dragStartPoint;

            // 移动选中的所有元素
            foreach (var element in _selectedElements)
            {
                var newPosition = new Point(
                    element.Position.X + offset.X,
                    element.Position.Y + offset.Y);
                
                // 边界检查
                newPosition = ConstrainToCanvas(newPosition);
                element.Position = newPosition;
            }

            _dragStartPoint = currentPoint;
            
            // 更新连接线
            UpdateConnectionsForElements(_selectedElements);
        }

        base.OnMouseMove(e);
    }

    protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
    {
        if (_isDragging)
        {
            _isDragging = false;
            _draggedElement = null;
            ReleaseMouseCapture();
            
            // 对齐到网格
            AlignSelectedElementsToGrid();
        }

        base.OnMouseLeftButtonUp(e);
    }

    private SFCElementViewModel GetElementAtPoint(Point point)
    {
        var hitTest = VisualTreeHelper.FindElementsInHostCoordinates(point, this);
        
        foreach (var element in hitTest)
        {
            if (element is FrameworkElement fe && fe.DataContext is SFCElementViewModel vm)
            {
                return vm;
            }
        }
        
        return null;
    }

    private Point ConstrainToCanvas(Point position)
    {
        return new Point(
            Math.Max(0, Math.Min(ActualWidth - 100, position.X)),
            Math.Max(0, Math.Min(ActualHeight - 100, position.Y)));
    }

    private void AlignSelectedElementsToGrid()
    {
        const double gridSize = 20;
        
        foreach (var element in _selectedElements)
        {
            element.Position = new Point(
                Math.Round(element.Position.X / gridSize) * gridSize,
                Math.Round(element.Position.Y / gridSize) * gridSize);
        }
    }
}
```

### 2. 元素选择管理

```csharp
public partial class SFCCanvas : Canvas
{
    public static readonly DependencyProperty SelectedElementProperty =
        DependencyProperty.Register(nameof(SelectedElement), typeof(SFCElementViewModel), 
            typeof(SFCCanvas), new PropertyMetadata(null, OnSelectedElementChanged));

    public SFCElementViewModel SelectedElement
    {
        get => (SFCElementViewModel)GetValue(SelectedElementProperty);
        set => SetValue(SelectedElementProperty, value);
    }

    private static void OnSelectedElementChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCCanvas canvas)
        {
            canvas.OnSelectedElementChanged(e.OldValue as SFCElementViewModel, e.NewValue as SFCElementViewModel);
        }
    }

    private void OnSelectedElementChanged(SFCElementViewModel oldElement, SFCElementViewModel newElement)
    {
        // 更新旧元素的选择状态
        if (oldElement != null)
        {
            oldElement.IsSelected = false;
        }

        // 更新新元素的选择状态
        if (newElement != null)
        {
            newElement.IsSelected = true;
        }

        // 触发选择变化事件
        SelectionChanged?.Invoke(this, new SelectionChangedEventArgs(oldElement, newElement));
    }

    public event EventHandler<SelectionChangedEventArgs> SelectionChanged;

    private void SelectElement(SFCElementViewModel element)
    {
        if (!_selectedElements.Contains(element))
        {
            _selectedElements.Add(element);
            element.IsSelected = true;
        }

        SelectedElement = element; // 设置主选择元素
    }

    private void ClearSelection()
    {
        foreach (var element in _selectedElements)
        {
            element.IsSelected = false;
        }
        
        _selectedElements.Clear();
        SelectedElement = null;
    }
}

public class SelectionChangedEventArgs : EventArgs
{
    public SFCElementViewModel OldElement { get; }
    public SFCElementViewModel NewElement { get; }

    public SelectionChangedEventArgs(SFCElementViewModel oldElement, SFCElementViewModel newElement)
    {
        OldElement = oldElement;
        NewElement = newElement;
    }
}
```

---

## 🔗 连接管理系统

### 1. 连接点适配器

```csharp
public class SFCConnectPointAdapter : ViewModelBase
{
    private string _elementId;
    private SFCElementType _elementType;
    private ConnectPointDirection _direction;
    private int _index;
    private SFCDataFlowType _dataFlowType;
    private bool _isConnected;
    private ObservableCollection<string> _connectionIds;

    public string ElementId
    {
        get => _elementId;
        set => SetProperty(ref _elementId, value);
    }

    public SFCElementType ElementType
    {
        get => _elementType;
        set => SetProperty(ref _elementType, value);
    }

    public ConnectPointDirection Direction
    {
        get => _direction;
        set => SetProperty(ref _direction, value);
    }

    public int Index
    {
        get => _index;
        set => SetProperty(ref _index, value);
    }

    public bool IsConnected
    {
        get => _isConnected;
        set => SetProperty(ref _isConnected, value);
    }

    public ObservableCollection<string> ConnectionIds
    {
        get => _connectionIds;
        set => SetProperty(ref _connectionIds, value);
    }

    public SFCConnectPointAdapter(string elementId, SFCElementType elementType, 
        ConnectPointDirection direction, int index, SFCDataFlowType dataFlowType)
    {
        ElementId = elementId;
        ElementType = elementType;
        Direction = direction;
        Index = index;
        _dataFlowType = dataFlowType;
        ConnectionIds = new ObservableCollection<string>();
        
        // 监听连接ID集合变化
        ConnectionIds.CollectionChanged += OnConnectionIdsChanged;
    }

    private void OnConnectionIdsChanged(object sender, NotifyCollectionChangedEventArgs e)
    {
        IsConnected = ConnectionIds.Count > 0;
    }

    public bool CanConnectTo(SFCConnectPointAdapter target)
    {
        if (target == null || target.ElementId == ElementId)
            return false;

        // 方向必须相反
        if (Direction == target.Direction)
            return false;

        // 数据流类型必须匹配
        if (_dataFlowType != target._dataFlowType)
            return false;

        // 检查元素类型兼容性
        return IsElementTypeCompatible(ElementType, target.ElementType);
    }

    private bool IsElementTypeCompatible(SFCElementType sourceType, SFCElementType targetType)
    {
        // 步骤可以连接到转换条件
        if (sourceType == SFCElementType.Step && targetType == SFCElementType.Transition)
            return true;

        // 转换条件可以连接到步骤
        if (sourceType == SFCElementType.Transition && targetType == SFCElementType.Step)
            return true;

        // 分支之间的连接
        if ((sourceType == SFCElementType.SelectionBranch || sourceType == SFCElementType.ParallelBranch) &&
            (targetType == SFCElementType.SelectionBranch || targetType == SFCElementType.ParallelBranch))
            return true;

        return false;
    }
}
```

### 2. 连接创建和管理

```csharp
public class SFCConnectionManager
{
    private readonly ObservableCollection<SFCConnectionViewModel> _connections;
    private readonly Dictionary<string, SFCElementViewModel> _elements;

    public SFCConnectionManager()
    {
        _connections = new ObservableCollection<SFCConnectionViewModel>();
        _elements = new Dictionary<string, SFCElementViewModel>();
    }

    public bool CreateConnection(string sourceElementId, int sourceIndex, 
        string targetElementId, int targetIndex)
    {
        // 验证连接有效性
        if (!ValidateConnection(sourceElementId, sourceIndex, targetElementId, targetIndex))
            return false;

        // 创建连接
        var connection = new SFCConnectionViewModel
        {
            Id = Guid.NewGuid().ToString(),
            SourceElementId = sourceElementId,
            SourceConnectPointIndex = sourceIndex,
            TargetElementId = targetElementId,
            TargetConnectPointIndex = targetIndex
        };

        _connections.Add(connection);

        // 更新连接点状态
        UpdateConnectPointStates(sourceElementId, sourceIndex, targetElementId, targetIndex, connection.Id);

        return true;
    }

    public bool RemoveConnection(string connectionId)
    {
        var connection = _connections.FirstOrDefault(c => c.Id == connectionId);
        if (connection == null)
            return false;

        _connections.Remove(connection);

        // 更新连接点状态
        RemoveConnectionFromAdapters(connection);

        return true;
    }

    private bool ValidateConnection(string sourceElementId, int sourceIndex, 
        string targetElementId, int targetIndex)
    {
        if (!_elements.TryGetValue(sourceElementId, out var sourceElement) ||
            !_elements.TryGetValue(targetElementId, out var targetElement))
            return false;

        var sourceAdapter = GetConnectPointAdapter(sourceElement, sourceIndex, true);
        var targetAdapter = GetConnectPointAdapter(targetElement, targetIndex, false);

        return sourceAdapter?.CanConnectTo(targetAdapter) == true;
    }

    private SFCConnectPointAdapter GetConnectPointAdapter(SFCElementViewModel element, int index, bool isSource)
    {
        if (element is SFCStepViewModel step)
        {
            return step.ConnectPointAdapters.FirstOrDefault(a => 
                a.Index == index && 
                a.Direction == (isSource ? ConnectPointDirection.Output : ConnectPointDirection.Input));
        }
        else if (element is SFCTransitionViewModel transition)
        {
            return transition.ConnectPointAdapters.FirstOrDefault(a => 
                a.Index == index && 
                a.Direction == (isSource ? ConnectPointDirection.Output : ConnectPointDirection.Input));
        }
        else if (element is SFCBranchViewModel branch)
        {
            return branch.ConnectPointAdapters.FirstOrDefault(a => a.Index == index);
        }

        return null;
    }

    private void UpdateConnectPointStates(string sourceElementId, int sourceIndex, 
        string targetElementId, int targetIndex, string connectionId)
    {
        if (_elements.TryGetValue(sourceElementId, out var sourceElement))
        {
            var sourceAdapter = GetConnectPointAdapter(sourceElement, sourceIndex, true);
            sourceAdapter?.ConnectionIds.Add(connectionId);
        }

        if (_elements.TryGetValue(targetElementId, out var targetElement))
        {
            var targetAdapter = GetConnectPointAdapter(targetElement, targetIndex, false);
            targetAdapter?.ConnectionIds.Add(connectionId);
        }
    }

    private void RemoveConnectionFromAdapters(SFCConnectionViewModel connection)
    {
        if (_elements.TryGetValue(connection.SourceElementId, out var sourceElement))
        {
            var sourceAdapter = GetConnectPointAdapter(sourceElement, connection.SourceConnectPointIndex, true);
            sourceAdapter?.ConnectionIds.Remove(connection.Id);
        }

        if (_elements.TryGetValue(connection.TargetElementId, out var targetElement))
        {
            var targetAdapter = GetConnectPointAdapter(targetElement, connection.TargetConnectPointIndex, false);
            targetAdapter?.ConnectionIds.Remove(connection.Id);
        }
    }

    public void RegisterElement(SFCElementViewModel element)
    {
        _elements[element.Id] = element;
    }

    public void UnregisterElement(string elementId)
    {
        // 移除相关连接
        var connectionsToRemove = _connections.Where(c => 
            c.SourceElementId == elementId || c.TargetElementId == elementId).ToList();

        foreach (var connection in connectionsToRemove)
        {
            RemoveConnection(connection.Id);
        }

        _elements.Remove(elementId);
    }
}
```

---

## ✅ 验证系统

### 1. SFC验证规则

```csharp
public class SFCValidator : ISFCValidator
{
    public ValidationResult ValidateSFC(SFCModel sfcModel)
    {
        var result = new ValidationResult();

        // 基本结构验证
        ValidateBasicStructure(sfcModel, result);

        // 连接验证
        ValidateConnections(sfcModel, result);

        // 分支验证
        ValidateBranches(sfcModel, result);

        // 循环检测
        DetectCycles(sfcModel, result);

        return result;
    }

    private void ValidateBasicStructure(SFCModel sfcModel, ValidationResult result)
    {
        // 必须有初始步骤
        if (!sfcModel.Steps.Any(s => s.IsInitial))
        {
            result.AddError("SFC图必须有一个初始步骤");
        }

        // 步骤和转换条件必须交替出现
        foreach (var connection in sfcModel.Connections)
        {
            var sourceElement = GetElementById(sfcModel, connection.SourceElementId);
            var targetElement = GetElementById(sfcModel, connection.TargetElementId);

            if (sourceElement?.ElementType == targetElement?.ElementType)
            {
                result.AddWarning($"步骤和转换条件应该交替连接: {sourceElement.Name} -> {targetElement.Name}");
            }
        }
    }

    private void ValidateConnections(SFCModel sfcModel, ValidationResult result)
    {
        foreach (var step in sfcModel.Steps)
        {
            var inputConnections = GetInputConnections(sfcModel, step.Id);
            var outputConnections = GetOutputConnections(sfcModel, step.Id);

            // 非初始步骤必须有输入连接
            if (!step.IsInitial && inputConnections.Count == 0)
            {
                result.AddError($"步骤 '{step.Name}' 缺少输入连接");
            }

            // 步骤必须有输出连接（除非是终止步骤）
            if (outputConnections.Count == 0 && !step.IsTerminal)
            {
                result.AddWarning($"步骤 '{step.Name}' 缺少输出连接");
            }
        }

        foreach (var transition in sfcModel.Transitions)
        {
            var inputConnections = GetInputConnections(sfcModel, transition.Id);
            var outputConnections = GetOutputConnections(sfcModel, transition.Id);

            // 转换条件必须有输入和输出连接
            if (inputConnections.Count == 0)
            {
                result.AddError($"转换条件 '{transition.Name}' 缺少输入连接");
            }

            if (outputConnections.Count == 0)
            {
                result.AddError($"转换条件 '{transition.Name}' 缺少输出连接");
            }

            // 验证转换条件表达式
            if (string.IsNullOrWhiteSpace(transition.Condition))
            {
                result.AddError($"转换条件 '{transition.Name}' 缺少条件表达式");
            }
        }
    }

    private void ValidateBranches(SFCModel sfcModel, ValidationResult result)
    {
        foreach (var branch in sfcModel.Branches)
        {
            if (branch.BranchType == SFCBranchType.Selection)
            {
                ValidateSelectionBranch(sfcModel, branch, result);
            }
            else if (branch.BranchType == SFCBranchType.Parallel)
            {
                ValidateParallelBranch(sfcModel, branch, result);
            }
        }
    }

    private void ValidateSelectionBranch(SFCModel sfcModel, SFCBranchViewModel branch, ValidationResult result)
    {
        var leftConnections = GetConnectionsByIndex(sfcModel, branch.Id, 1); // 左侧连接点
        var rightConnections = GetConnectionsByIndex(sfcModel, branch.Id, 2); // 右侧连接点

        if (leftConnections.Count == 0)
        {
            result.AddWarning($"选择分支 '{branch.Name}' 左侧缺少连接");
        }

        if (rightConnections.Count == 0)
        {
            result.AddWarning($"选择分支 '{branch.Name}' 右侧缺少连接");
        }
    }

    private void ValidateParallelBranch(SFCModel sfcModel, SFCBranchViewModel branch, ValidationResult result)
    {
        var leftConnections = GetConnectionsByIndex(sfcModel, branch.Id, 1); // 左侧双线连接点
        var rightConnections = GetConnectionsByIndex(sfcModel, branch.Id, 0); // 右侧双线连接点

        if (leftConnections.Count == 0)
        {
            result.AddError($"并行分支 '{branch.Name}' 左侧缺少连接");
        }

        if (rightConnections.Count == 0)
        {
            result.AddError($"并行分支 '{branch.Name}' 右侧缺少连接");
        }
    }

    private void DetectCycles(SFCModel sfcModel, ValidationResult result)
    {
        var visited = new HashSet<string>();
        var recursionStack = new HashSet<string>();

        foreach (var step in sfcModel.Steps.Where(s => s.IsInitial))
        {
            if (HasCycle(sfcModel, step.Id, visited, recursionStack))
            {
                result.AddError("检测到循环依赖");
                break;
            }
        }
    }

    private bool HasCycle(SFCModel sfcModel, string elementId, HashSet<string> visited, HashSet<string> recursionStack)
    {
        if (recursionStack.Contains(elementId))
            return true;

        if (visited.Contains(elementId))
            return false;

        visited.Add(elementId);
        recursionStack.Add(elementId);

        var outputConnections = GetOutputConnections(sfcModel, elementId);
        foreach (var connection in outputConnections)
        {
            if (HasCycle(sfcModel, connection.TargetElementId, visited, recursionStack))
                return true;
        }

        recursionStack.Remove(elementId);
        return false;
    }

    // 辅助方法
    private SFCElementViewModel GetElementById(SFCModel sfcModel, string elementId)
    {
        return sfcModel.Steps.Cast<SFCElementViewModel>()
            .Concat(sfcModel.Transitions)
            .Concat(sfcModel.Branches)
            .FirstOrDefault(e => e.Id == elementId);
    }

    private List<SFCConnectionViewModel> GetInputConnections(SFCModel sfcModel, string elementId)
    {
        return sfcModel.Connections.Where(c => c.TargetElementId == elementId).ToList();
    }

    private List<SFCConnectionViewModel> GetOutputConnections(SFCModel sfcModel, string elementId)
    {
        return sfcModel.Connections.Where(c => c.SourceElementId == elementId).ToList();
    }

    private List<SFCConnectionViewModel> GetConnectionsByIndex(SFCModel sfcModel, string elementId, int index)
    {
        return sfcModel.Connections.Where(c => 
            (c.SourceElementId == elementId && c.SourceConnectPointIndex == index) ||
            (c.TargetElementId == elementId && c.TargetConnectPointIndex == index)).ToList();
    }
}

public class ValidationResult
{
    public List<ValidationMessage> Errors { get; } = new();
    public List<ValidationMessage> Warnings { get; } = new();

    public bool IsValid => Errors.Count == 0;

    public void AddError(string message)
    {
        Errors.Add(new ValidationMessage(ValidationLevel.Error, message));
    }

    public void AddWarning(string message)
    {
        Warnings.Add(new ValidationMessage(ValidationLevel.Warning, message));
    }
}

public class ValidationMessage
{
    public ValidationLevel Level { get; }
    public string Message { get; }
    public DateTime Timestamp { get; }

    public ValidationMessage(ValidationLevel level, string message)
    {
        Level = level;
        Message = message;
        Timestamp = DateTime.Now;
    }
}

public enum ValidationLevel
{
    Error,
    Warning,
    Info
}
```

---

## 🔧 代码生成系统

### 1. ST代码生成器

```csharp
public class SFCCodeGenerator : ISFCCodeGenerator
{
    public string GenerateSTCode(SFCModel sfcModel)
    {
        var sb = new StringBuilder();

        // 生成程序头部
        GenerateHeader(sb, sfcModel);

        // 生成变量声明
        GenerateVariableDeclarations(sb, sfcModel);

        // 生成SFC主体
        GenerateSFCBody(sb, sfcModel);

        // 生成程序尾部
        GenerateFooter(sb);

        return sb.ToString();
    }

    private void GenerateHeader(StringBuilder sb, SFCModel sfcModel)
    {
        sb.AppendLine($"PROGRAM {sfcModel.Name}");
        sb.AppendLine("VAR");
    }

    private void GenerateVariableDeclarations(StringBuilder sb, SFCModel sfcModel)
    {
        // 生成步骤变量
        foreach (var step in sfcModel.Steps)
        {
            sb.AppendLine($"    {step.Name}_X : BOOL := {(step.IsInitial ? "TRUE" : "FALSE")};");
            sb.AppendLine($"    {step.Name}_T : TIME;");
        }

        sb.AppendLine();

        // 生成转换条件变量
        foreach (var transition in sfcModel.Transitions)
        {
            if (!transition.IsAlwaysTrue)
            {
                sb.AppendLine($"    {transition.Name}_Condition : BOOL;");
            }
        }

        sb.AppendLine("END_VAR");
        sb.AppendLine();
    }

    private void GenerateSFCBody(StringBuilder sb, SFCModel sfcModel)
    {
        sb.AppendLine("SFC_CHART");
        sb.AppendLine();

        // 生成步骤和转换条件
        var processedElements = new HashSet<string>();
        var initialStep = sfcModel.Steps.FirstOrDefault(s => s.IsInitial);

        if (initialStep != null)
        {
            GenerateElementChain(sb, sfcModel, initialStep.Id, processedElements, 0);
        }

        sb.AppendLine("END_SFC");
    }

    private void GenerateElementChain(StringBuilder sb, SFCModel sfcModel, string elementId, 
        HashSet<string> processedElements, int indentLevel)
    {
        if (processedElements.Contains(elementId))
            return;

        processedElements.Add(elementId);
        var indent = new string(' ', indentLevel * 4);

        var element = GetElementById(sfcModel, elementId);
        if (element == null)
            return;

        switch (element.ElementType)
        {
            case SFCElementType.Step:
                GenerateStep(sb, element as SFCStepViewModel, indent);
                break;

            case SFCElementType.Transition:
                GenerateTransition(sb, element as SFCTransitionViewModel, indent);
                break;

            case SFCElementType.SelectionBranch:
                GenerateSelectionBranch(sb, sfcModel, element as SFCBranchViewModel, processedElements, indentLevel);
                return;

            case SFCElementType.ParallelBranch:
                GenerateParallelBranch(sb, sfcModel, element as SFCBranchViewModel, processedElements, indentLevel);
                return;
        }

        // 继续处理下一个元素
        var nextConnections = GetOutputConnections(sfcModel, elementId);
        foreach (var connection in nextConnections)
        {
            GenerateElementChain(sb, sfcModel, connection.TargetElementId, processedElements, indentLevel);
        }
    }

    private void GenerateStep(StringBuilder sb, SFCStepViewModel step, string indent)
    {
        sb.AppendLine($"{indent}STEP {step.Name}:");
        
        if (!string.IsNullOrWhiteSpace(step.ActionCode))
        {
            sb.AppendLine($"{indent}    {step.ActionCode}");
        }

        if (step.MinTime > TimeSpan.Zero)
        {
            sb.AppendLine($"{indent}    MIN_TIME := T#{step.MinTime.TotalMilliseconds}ms;");
        }

        if (step.MaxTime > TimeSpan.Zero)
        {
            sb.AppendLine($"{indent}    MAX_TIME := T#{step.MaxTime.TotalMilliseconds}ms;");
        }

        sb.AppendLine($"{indent}END_STEP");
        sb.AppendLine();
    }

    private void GenerateTransition(StringBuilder sb, SFCTransitionViewModel transition, string indent)
    {
        sb.AppendLine($"{indent}TRANSITION {transition.Name}:");
        
        if (transition.IsAlwaysTrue)
        {
            sb.AppendLine($"{indent}    TRUE");
        }
        else
        {
            sb.AppendLine($"{indent}    {transition.Condition}");
        }

        sb.AppendLine($"{indent}END_TRANSITION");
        sb.AppendLine();
    }

    private void GenerateSelectionBranch(StringBuilder sb, SFCModel sfcModel, SFCBranchViewModel branch, 
        HashSet<string> processedElements, int indentLevel)
    {
        var indent = new string(' ', indentLevel * 4);
        
        sb.AppendLine($"{indent}SELECTION");

        // 获取分支路径
        var leftConnections = GetConnectionsByIndex(sfcModel, branch.Id, 1);
        var rightConnections = GetConnectionsByIndex(sfcModel, branch.Id, 2);

        // 生成左分支
        if (leftConnections.Any())
        {
            sb.AppendLine($"{indent}    BRANCH_LEFT:");
            foreach (var connection in leftConnections)
            {
                GenerateElementChain(sb, sfcModel, connection.TargetElementId, processedElements, indentLevel + 2);
            }
        }

        // 生成右分支
        if (rightConnections.Any())
        {
            sb.AppendLine($"{indent}    BRANCH_RIGHT:");
            foreach (var connection in rightConnections)
            {
                GenerateElementChain(sb, sfcModel, connection.TargetElementId, processedElements, indentLevel + 2);
            }
        }

        sb.AppendLine($"{indent}END_SELECTION");
        sb.AppendLine();
    }

    private void GenerateParallelBranch(StringBuilder sb, SFCModel sfcModel, SFCBranchViewModel branch, 
        HashSet<string> processedElements, int indentLevel)
    {
        var indent = new string(' ', indentLevel * 4);
        
        sb.AppendLine($"{indent}PARALLEL");

        // 获取并行分支路径
        var connections = GetOutputConnections(sfcModel, branch.Id);

        foreach (var connection in connections)
        {
            sb.AppendLine($"{indent}    BRANCH:");
            GenerateElementChain(sb, sfcModel, connection.TargetElementId, processedElements, indentLevel + 2);
        }

        sb.AppendLine($"{indent}END_PARALLEL");
        sb.AppendLine();
    }

    private void GenerateFooter(StringBuilder sb)
    {
        sb.AppendLine("END_PROGRAM");
    }

    // 辅助方法（与验证器中的相同）
    private SFCElementViewModel GetElementById(SFCModel sfcModel, string elementId)
    {
        return sfcModel.Steps.Cast<SFCElementViewModel>()
            .Concat(sfcModel.Transitions)
            .Concat(sfcModel.Branches)
            .FirstOrDefault(e => e.Id == elementId);
    }

    private List<SFCConnectionViewModel> GetOutputConnections(SFCModel sfcModel, string elementId)
    {
        return sfcModel.Connections.Where(c => c.SourceElementId == elementId).ToList();
    }

    private List<SFCConnectionViewModel> GetConnectionsByIndex(SFCModel sfcModel, string elementId, int index)
    {
        return sfcModel.Connections.Where(c => 
            (c.SourceElementId == elementId && c.SourceConnectPointIndex == index) ||
            (c.TargetElementId == elementId && c.TargetConnectPointIndex == index)).ToList();
    }
}
```

---

## 📋 SFC编辑器功能检查清单

### 元素管理
- [ ] 步骤元素创建和编辑
- [ ] 转换条件元素创建和编辑
- [ ] 选择分支元素创建和编辑
- [ ] 并行分支元素创建和编辑
- [ ] 元素属性面板绑定

### 交互功能
- [ ] 元素拖拽移动
- [ ] 多选和批量操作
- [ ] 网格对齐功能
- [ ] 撤销/重做操作
- [ ] 右键菜单功能

### 连接系统
- [ ] 连接点可视化
- [ ] 拖拽创建连接
- [ ] 连接验证规则
- [ ] 连接状态管理
- [ ] 连接线样式

### 验证系统
- [ ] 实时语法验证
- [ ] 结构完整性检查
- [ ] 循环依赖检测
- [ ] 错误和警告显示
- [ ] 验证结果面板

### 代码生成
- [ ] ST代码生成
- [ ] 代码格式化
- [ ] 变量声明生成
- [ ] 分支结构生成
- [ ] 代码导出功能

---

## 🎯 总结

SFC编辑器的核心功能实现涉及多个层面的技术：

1. **数据模型设计**：清晰的元素类型定义和属性管理
2. **UI交互实现**：流畅的拖拽操作和选择管理
3. **连接系统**：灵活的连接点适配器和验证机制
4. **验证引擎**：全面的规则检查和错误反馈
5. **代码生成**：标准的ST代码输出

这些功能的有机结合，构成了一个完整、可用的SFC编辑器系统，为工业自动化编程提供了强大的图形化工具支持。