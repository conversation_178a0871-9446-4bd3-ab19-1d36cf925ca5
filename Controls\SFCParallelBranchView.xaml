<UserControl x:Class="PC_Control2.Demo.Controls.SFCParallelBranchView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:PC_Control2.Demo.Controls"
             xmlns:controls="clr-namespace:PC_Control2.Demo.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="50" d:DesignWidth="200">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/SFCBlueprintStyles.xaml"/>
                <ResourceDictionary Source="../Styles/SFCStyles.xaml"/>
                <ResourceDictionary Source="../Styles/Controls/SFCBranchStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 布尔值到可见性转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="57*"/>
            <ColumnDefinition Width="143*"/>
        </Grid.ColumnDefinitions>
        <!-- 右键菜单 -->
        <Grid.ContextMenu>
            <ContextMenu Style="{StaticResource BlueprintContextMenuStyle}">
                <MenuItem Header="切换分支类型"
                         Command="{Binding ToggleBranchTypeCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="🔄"/>
                <MenuItem Header="设为汇聚分支"
                         IsCheckable="True"
                         IsChecked="{Binding IsConvergence}"
                         Command="{Binding ToggleConvergenceCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="⚡"/>
                <Separator/>
                <MenuItem Header="插入新并行分支"
                          Command="{Binding DataContext.InsertParallelBranchFromAnyPartCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Style="{StaticResource BlueprintMenuItemStyle}"
                          Icon="➕"/>
                <Separator/>
                <MenuItem Header="复制分支"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="📋"/>
                <MenuItem Header="删除分支"
                         Command="{Binding DeleteCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="🗑"/>
                <Separator/>
                <MenuItem Header="属性"
                         Command="{Binding EditPropertiesCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="⚙"/>
            </ContextMenu>
        </Grid.ContextMenu>

        <!-- 并行分支标记 - 使用Canvas精确定位 -->
        <Canvas x:Name="MainCanvas" Width="200" Height="50" Grid.ColumnSpan="2" Background="Transparent" Cursor="Hand" Margin="-1,0,1,0">

            <!-- 统一交互层 - 覆盖整个同步分支区域 -->
            <Rectangle x:Name="ParallelBranchInteractionLayer"
                       Width="176"
                       Height="50"
                       Canvas.Left="24"
                       Canvas.Top="0"
                       Fill="Transparent"
                       Cursor="Hand"
                       Panel.ZIndex="100" HorizontalAlignment="Left" VerticalAlignment="Center">
                <Rectangle.Style>
                    <Style TargetType="Rectangle">
                        <Style.Triggers>
                            <!-- 选中状态视觉反馈 -->
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedBorderBrush}"/>
                                <Setter Property="StrokeThickness" Value="2"/>
                                <Setter Property="StrokeDashArray" Value="3,2"/>
                                <Setter Property="Opacity" Value="0.3"/>
                            </DataTrigger>
                            <!-- 悬停状态视觉反馈 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Stroke" Value="#FF00FF00"/>
                                <Setter Property="StrokeThickness" Value="1"/>
                                <Setter Property="StrokeDashArray" Value="2,1"/>
                                <Setter Property="Opacity" Value="0.2"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Rectangle.Style>
            </Rectangle>

            <!-- 左侧竖直连接线 -->
            <Line x:Name="LeftVerticalLine"
                  X1="20" Y1="11" X2="20" Y2="35"
                  Canvas.Left="22" Canvas.Top="-15" HorizontalAlignment="Left" VerticalAlignment="Center"
                  IsHitTestVisible="False" Height="48" Width="35">
                <Line.Style>
                    <Style TargetType="Line" BasedOn="{StaticResource BlueprintConnectionPathStyle}">
                        <Style.Triggers>
                            <!-- 根据ViewType控制可见性：只有Initial类型才显示左侧竖直连接线 -->
                            <DataTrigger Binding="{Binding ViewType}" Value="Subsequent">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                                <Setter Property="StrokeThickness" Value="3"/>
                                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Line.Style>
            </Line>

            <!-- 上线 - 并行分支的双线上线 -->
            <Rectangle Width="146"
                      Height="3"
                      Canvas.Left="42" Canvas.Top="19.5" HorizontalAlignment="Center" VerticalAlignment="Top"
                      IsHitTestVisible="False">
                <Rectangle.Style>
                    <Style TargetType="Rectangle" BasedOn="{StaticResource BlueprintConnectionLineStyle}">
                        <Setter Property="Fill" Value="#FF4A90E2"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Fill" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Rectangle.Style>
            </Rectangle>

            <!-- 下线 - 并行分支的双线下线 -->
            <Rectangle Width="146"
                      Height="3"
                      Canvas.Left="42" Canvas.Top="26.5" HorizontalAlignment="Center" VerticalAlignment="Top"
                      IsHitTestVisible="False">
                <Rectangle.Style>
                    <Style TargetType="Rectangle" BasedOn="{StaticResource BlueprintConnectionLineStyle}">
                        <Setter Property="Fill" Value="#FF4A90E2"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Fill" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Rectangle.Style>
            </Rectangle>

            <!-- 左侧垂直连接线 -->
            <Rectangle Width="2"
                      Height="10"
                      Canvas.Left="41" Canvas.Top="19.5" HorizontalAlignment="Center" VerticalAlignment="Top"
                      IsHitTestVisible="False">
                <Rectangle.Style>
                    <Style TargetType="Rectangle" BasedOn="{StaticResource BlueprintConnectionLineStyle}">
                        <Setter Property="Fill" Value="#FF4A90E2"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Fill" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Rectangle.Style>
            </Rectangle>

            <!-- 右侧垂直连接线 -->
            <Rectangle Width="3"
                      Height="10"
                      Canvas.Left="186" Canvas.Top="19.5" HorizontalAlignment="Center" VerticalAlignment="Top"
                      IsHitTestVisible="False">
                <Rectangle.Style>
                    <Style TargetType="Rectangle" BasedOn="{StaticResource BlueprintConnectionLineStyle}">
                        <Setter Property="Fill" Value="#FF4A90E2"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Fill" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Rectangle.Style>
            </Rectangle>

            <!-- 左侧竖线上端连接点 -->
            <controls:SFCConnectPoint x:Name="LeftTopConnectPoint"
                Canvas.Left="37" HorizontalAlignment="Center" VerticalAlignment="Top" Canvas.Top="-9"
                IsHitTestVisible="True" PointType="Input" Index="0" ElementId="{Binding Id}" ElementType="Branch">
                <controls:SFCConnectPoint.Style>
                    <Style TargetType="controls:SFCConnectPoint">
                        <Style.Triggers>
                            <!-- 根据ViewType控制可见性：只有Initial类型才显示左侧上端连接点 -->
                            <DataTrigger Binding="{Binding ViewType}" Value="Subsequent">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsInitialStep}" Value="True">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </controls:SFCConnectPoint.Style>
            </controls:SFCConnectPoint>

            <!-- 左侧双线连接点 -->
            <controls:SFCConnectPoint x:Name="LeftParallelPoint"
                Canvas.Left="37" HorizontalAlignment="Center" VerticalAlignment="Top" Canvas.Top="19.5"
                IsHitTestVisible="True" PointType="Output" Index="1" ElementId="{Binding Id}" ElementType="Branch">
                <controls:SFCConnectPoint.Style>
                    <Style TargetType="controls:SFCConnectPoint">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsInitialStep}" Value="True">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </controls:SFCConnectPoint.Style>
            </controls:SFCConnectPoint>

            <!-- 右侧双线连接点 -->
            <controls:SFCConnectPoint x:Name="RightParallelPoint"
                Canvas.Left="183" HorizontalAlignment="Left" VerticalAlignment="Center" Canvas.Top="19.5"
                IsHitTestVisible="True" PointType="Output" Index="2" ElementId="{Binding Id}" ElementType="Branch">
                <controls:SFCConnectPoint.Style>
                    <Style TargetType="controls:SFCConnectPoint">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsInitialStep}" Value="True">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </controls:SFCConnectPoint.Style>
            </controls:SFCConnectPoint>
        </Canvas>

        <!-- 汇聚标识 -->
    </Grid>
</UserControl>
