<UserControl x:Class="PC_Control2.Demo.Controls.SFCJumpView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:PC_Control2.Demo.ViewModels"
             xmlns:controls="clr-namespace:PC_Control2.Demo.Controls"
             xmlns:models="clr-namespace:PC_Control2.Demo.Models"
             xmlns:config="clr-namespace:PC_Control2.Demo.Configuration"
             Width="60"
             Height="50">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/SFCBlueprintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 布尔值到可见性转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- 主画布容器 -->
    <Canvas x:Name="MainCanvas"
            Background="Transparent"
            ClipToBounds="False">

        <!-- 右键菜单 -->
        <Canvas.ContextMenu>
            <ContextMenu Style="{StaticResource BlueprintContextMenuStyle}"
                        PlacementTarget="{Binding RelativeSource={RelativeSource Self}, Path=Parent}">
                <MenuItem Header="编辑跳转目标"
                         Command="{Binding EditJumpTargetCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="⚙"/>
                <Separator/>
                <MenuItem Header="复制跳转"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="📋"/>
                <MenuItem Header="删除跳转"
                         Command="{Binding DeleteCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="🗑"/>
                <Separator/>
                <MenuItem Header="属性"
                         Command="{Binding EditPropertiesCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="⚙"/>
            </ContextMenu>
        </Canvas.ContextMenu>

        <!-- Canvas样式定义 -->
        <Canvas.Style>
            <Style TargetType="Canvas">
                <Style.Triggers>
                    <!-- 整体选中状态 -->
                    <DataTrigger Binding="{Binding IsSelected}" Value="True">
                        <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                    </DataTrigger>
                    <!-- 整体鼠标悬停效果 -->
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        <Setter Property="Opacity" Value="0.9"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </Canvas.Style>

        <!-- 透明交互层 - 覆盖整个跳转元素区域 -->
        <Rectangle x:Name="InteractionLayer"
                   Width="60"
                   Height="50"
                   Canvas.Top="0"
                   Fill="Transparent"
                   Cursor="Hand"
                   Panel.ZIndex="100" HorizontalAlignment="Left" VerticalAlignment="Center" Canvas.Left="8">
            <!-- 交互层右键菜单 -->
            <Rectangle.ContextMenu>
                <ContextMenu Style="{StaticResource BlueprintContextMenuStyle}"
                            PlacementTarget="{Binding RelativeSource={RelativeSource Self}, Path=Parent}">
                    <MenuItem Header="编辑跳转目标"
                             Command="{Binding EditJumpTargetCommand}"
                             Style="{StaticResource BlueprintMenuItemStyle}"
                             Icon="⚙"/>
                    <Separator/>
                    <MenuItem Header="复制跳转"
                             Style="{StaticResource BlueprintMenuItemStyle}"
                             Icon="📋"/>
                    <MenuItem Header="删除跳转"
                             Command="{Binding DeleteCommand}"
                             Style="{StaticResource BlueprintMenuItemStyle}"
                             Icon="🗑"/>
                    <Separator/>
                    <MenuItem Header="属性"
                             Command="{Binding EditPropertiesCommand}"
                             Style="{StaticResource BlueprintMenuItemStyle}"
                             Icon="⚙"/>
                </ContextMenu>
            </Rectangle.ContextMenu>
            <Rectangle.Style>
                <Style TargetType="Rectangle">
                    <Style.Triggers>
                        <!-- 选中状态视觉反馈 -->
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedBorderBrush}"/>
                            <Setter Property="StrokeThickness" Value="2"/>
                            <Setter Property="StrokeDashArray" Value="3,2"/>
                            <Setter Property="Opacity" Value="0.3"/>
                        </DataTrigger>
                        <!-- 悬停状态视觉反馈 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Stroke" Value="#FF00FF00"/>
                            <Setter Property="StrokeThickness" Value="1"/>
                            <Setter Property="StrokeDashArray" Value="2,1"/>
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 顶部连接点 - 输入连接点 -->
        <controls:SFCConnectPoint x:Name="TopConnectPoint"
                 Canvas.Left="25" Canvas.Top="6"
                 HorizontalAlignment="Left" VerticalAlignment="Center"
                 PointType="Input" Index="0" ElementId="{Binding Id}" ElementType="Jump">
            <controls:SFCConnectPoint.Style>
                <Style TargetType="{x:Type controls:SFCConnectPoint}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </controls:SFCConnectPoint.Style>
        </controls:SFCConnectPoint>

        <!-- 垂直线段 - 从连接点向下延伸（复用SFCTerminatorView样式） -->
        <Rectangle x:Name="VerticalLine"
                   Canvas.Left="29" Canvas.Top="15"
                   Width="2" Height="20"
                   Fill="#FF888888" HorizontalAlignment="Left" VerticalAlignment="Center">
            <Rectangle.Style>
                <Style TargetType="{x:Type Rectangle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Fill" Value="#FF4A90E2"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                        <!-- 错误状态显示 -->
                        <DataTrigger Binding="{Binding IsTargetValid}" Value="False">
                            <Setter Property="Fill" Value="#FFFF4444"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 箭头图形 - 指向右侧 -->
        <Path x:Name="JumpArrow"
              Canvas.Left="26" Canvas.Top="35"
              Data="M0,0 L8,4 L0,8 Z"
              Fill="#FF888888"
              HorizontalAlignment="Left" VerticalAlignment="Center" RenderTransformOrigin="0.5,0.5">
            <Path.RenderTransform>
                <TransformGroup>
                    <ScaleTransform/>
                    <SkewTransform/>
                    <RotateTransform Angle="90.168"/>
                    <TranslateTransform/>
                </TransformGroup>
            </Path.RenderTransform>
            <Path.Style>
                <Style TargetType="{x:Type Path}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Fill" Value="#FF4A90E2"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                        <!-- 错误状态显示 -->
                        <DataTrigger Binding="{Binding IsTargetValid}" Value="False">
                            <Setter Property="Fill" Value="#FFFF4444"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Path.Style>
        </Path>

        <!-- 步号标签 - 显示跳转目标步骤号 -->
        <TextBlock x:Name="StepNumberLabel"
                   Canvas.Left="38" Canvas.Top="32"
                   Text="{Binding JumpDisplayText}"
                   FontSize="10"
                   FontWeight="Bold"
                   Foreground="#FF333333"
                   HorizontalAlignment="Left" VerticalAlignment="Center">
            <TextBlock.Style>
                <Style TargetType="{x:Type TextBlock}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Foreground" Value="#FF4A90E2"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                        <!-- 错误状态显示 -->
                        <DataTrigger Binding="{Binding IsTargetValid}" Value="False">
                            <Setter Property="Foreground" Value="#FFFF4444"/>
                        </DataTrigger>
                        <!-- 未设置目标时的样式 -->
                        <DataTrigger Binding="{Binding TargetStepNumber}" Value="0">
                            <Setter Property="Foreground" Value="#FFAAAAAA"/>
                            <Setter Property="FontStyle" Value="Italic"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </TextBlock.Style>
        </TextBlock>

        <!-- 验证错误指示器 -->
        <Ellipse x:Name="ErrorIndicator"
                 Canvas.Left="50" Canvas.Top="7"
                 Width="8" Height="8"
                 Fill="#FFFF4444"
                 Stroke="#FFAA0000"
                 StrokeThickness="1"
                 Visibility="{Binding IsTargetValid, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Invert}"
                 ToolTip="{Binding ValidationMessage}">
        </Ellipse>

    </Canvas>
</UserControl>
