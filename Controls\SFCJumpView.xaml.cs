using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using PC_Control2.Demo.ViewModels;

namespace PC_Control2.Demo.Controls
{
    /// <summary>
    /// SFCJumpView.xaml 的交互逻辑
    /// 实现SFC跳转元素的完整UI交互功能
    /// </summary>
    public partial class SFCJumpView : UserControl
    {
        #region 私有字段

        private SFCJumpViewModel? _viewModel;
        private bool _isDragging = false;
        private Point _dragStartPoint;

        #endregion

        #region 依赖属性

        /// <summary>
        /// 跳转目标步骤号依赖属性
        /// </summary>
        public static readonly DependencyProperty TargetStepNumberProperty =
            DependencyProperty.Register(nameof(TargetStepNumber), typeof(int), typeof(SFCJumpView),
                new PropertyMetadata(0, OnTargetStepNumberChanged));

        /// <summary>
        /// 跳转目标步骤号
        /// </summary>
        public int TargetStepNumber
        {
            get => (int)GetValue(TargetStepNumberProperty);
            set => SetValue(TargetStepNumberProperty, value);
        }

        /// <summary>
        /// 目标有效性依赖属性
        /// </summary>
        public static readonly DependencyProperty IsTargetValidProperty =
            DependencyProperty.Register(nameof(IsTargetValid), typeof(bool), typeof(SFCJumpView),
                new PropertyMetadata(false, OnIsTargetValidChanged));

        /// <summary>
        /// 目标是否有效
        /// </summary>
        public bool IsTargetValid
        {
            get => (bool)GetValue(IsTargetValidProperty);
            set => SetValue(IsTargetValidProperty, value);
        }

        #endregion

        #region 构造函数

        public SFCJumpView()
        {
            InitializeComponent();

            // 数据上下文变化事件
            DataContextChanged += OnDataContextChanged;

            // 控件加载完成事件
            Loaded += OnLoaded;

            // 设置可拖拽
            this.MouseLeftButtonDown += OnMouseLeftButtonDown;
            this.MouseLeftButtonUp += OnMouseLeftButtonUp;
            this.MouseMove += OnMouseMove;

            // 双击编辑事件
            this.MouseDoubleClick += OnMouseDoubleClick;

            // 右键事件处理 - 阻止事件冒泡到SFCCanvas
            this.MouseRightButtonDown += OnMouseRightButtonDown;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 数据上下文变化处理
        /// </summary>
        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.NewValue is SFCJumpViewModel jumpViewModel)
            {
                _viewModel = jumpViewModel;

                // 初始化连接点（确保适配器正确绑定）
                InitializeConnectPoints();

                // 绑定选中状态变化事件
                jumpViewModel.PropertyChanged += (s, args) =>
                {
                    if (args.PropertyName == nameof(SFCJumpViewModel.IsSelected))
                    {
                        UpdateVisualState();
                    }
                };

                System.Diagnostics.Debug.WriteLine($"[SFCJumpView] 数据上下文已更新: {jumpViewModel.Id}");
            }
        }

        /// <summary>
        /// 控件加载完成处理
        /// </summary>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 初始化连接点
            InitializeConnectPoints();

            System.Diagnostics.Debug.WriteLine($"[SFCJumpView] 控件加载完成，已初始化连接点: {_viewModel?.Id}");
        }

        /// <summary>
        /// 鼠标左键按下处理
        /// </summary>
        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_viewModel == null) return;

            // 选中跳转元素
            _viewModel.SelectCommand?.Execute(null);

            // 开始拖拽
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            this.CaptureMouse();

            // 不设置e.Handled = true，让事件继续冒泡到SFCCanvas
        }

        /// <summary>
        /// 鼠标左键释放处理
        /// </summary>
        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                this.ReleaseMouseCapture();
                e.Handled = true;
            }
        }

        /// <summary>
        /// 鼠标移动处理
        /// </summary>
        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (_isDragging && _viewModel != null && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPosition = e.GetPosition(this);
                var deltaX = currentPosition.X - _dragStartPoint.X;
                var deltaY = currentPosition.Y - _dragStartPoint.Y;

                // 更新跳转元素位置
                var newPosition = new Point(
                    _viewModel.Position.X + deltaX,
                    _viewModel.Position.Y + deltaY);

                System.Diagnostics.Debug.WriteLine($"[SFCJumpView] 拖动更新位置: {_viewModel.Id} -> {newPosition}");
                _viewModel.Position = newPosition;
                e.Handled = true;
            }
        }

        /// <summary>
        /// 鼠标双击处理 - 编辑跳转目标
        /// </summary>
        private void OnMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (_viewModel == null) return;

            // 执行编辑跳转目标命令
            _viewModel.EditJumpTargetCommand?.Execute(null);

            e.Handled = true;
        }

        /// <summary>
        /// 鼠标右键按下处理 - 阻止事件冒泡到SFCCanvas
        /// </summary>
        private void OnMouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_viewModel == null) return;

            // 选中跳转元素
            _viewModel.SelectCommand?.Execute(null);

            // 动态创建右键菜单，确保数据绑定正确
            ShowJumpContextMenu(e.GetPosition(this));

            // 阻止事件冒泡到SFCCanvas
            e.Handled = true;

            System.Diagnostics.Debug.WriteLine($"[SFCJumpView] 右键点击跳转元素: {_viewModel.Id}，已显示右键菜单");
        }

        /// <summary>
        /// 显示跳转元素专用的右键菜单
        /// </summary>
        private void ShowJumpContextMenu(Point position)
        {
            if (_viewModel == null) return;

            try
            {
                // 动态创建ContextMenu，确保DataContext正确
                var contextMenu = new ContextMenu
                {
                    DataContext = _viewModel,
                    PlacementTarget = this,
                    Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint
                };

                // 应用样式
                if (this.FindResource("BlueprintContextMenuStyle") is Style menuStyle)
                {
                    contextMenu.Style = menuStyle;
                }

                // 创建菜单项
                var editTargetItem = new MenuItem
                {
                    Header = "编辑跳转目标",
                    Icon = "⚙",
                    Command = _viewModel.EditJumpTargetCommand
                };

                var separator1 = new Separator();

                var copyItem = new MenuItem
                {
                    Header = "复制跳转",
                    Icon = "📋"
                };

                var deleteItem = new MenuItem
                {
                    Header = "删除跳转",
                    Icon = "🗑",
                    Command = _viewModel.DeleteCommand
                };

                var separator2 = new Separator();

                var propertiesItem = new MenuItem
                {
                    Header = "属性",
                    Icon = "⚙",
                    Command = _viewModel.EditPropertiesCommand
                };

                // 应用菜单项样式
                if (this.FindResource("BlueprintMenuItemStyle") is Style itemStyle)
                {
                    editTargetItem.Style = itemStyle;
                    copyItem.Style = itemStyle;
                    deleteItem.Style = itemStyle;
                    propertiesItem.Style = itemStyle;
                }

                // 添加菜单项
                contextMenu.Items.Add(editTargetItem);
                contextMenu.Items.Add(separator1);
                contextMenu.Items.Add(copyItem);
                contextMenu.Items.Add(deleteItem);
                contextMenu.Items.Add(separator2);
                contextMenu.Items.Add(propertiesItem);

                // 显示菜单
                contextMenu.IsOpen = true;

                System.Diagnostics.Debug.WriteLine($"[SFCJumpView] 已动态创建并显示右键菜单");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SFCJumpView] 创建右键菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 目标步骤号变化处理
        /// </summary>
        private static void OnTargetStepNumberChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SFCJumpView jumpView)
            {
                jumpView.UpdateStepNumberDisplay();
            }
        }

        /// <summary>
        /// 目标有效性变化处理
        /// </summary>
        private static void OnIsTargetValidChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SFCJumpView jumpView)
            {
                jumpView.UpdateValidationDisplay();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化连接点
        /// </summary>
        private void InitializeConnectPoints()
        {
            if (_viewModel != null && TopConnectPoint != null)
            {
                // 设置连接点的元素ID和类型
                TopConnectPoint.ElementId = _viewModel.Id;
                TopConnectPoint.ElementType = Models.SFCElementType.Jump;
                TopConnectPoint.PointType = ConnectPointType.Input;
                TopConnectPoint.Index = 0;

                // 🔧 关键修复：绑定连接点适配器，确保连接状态正确显示
                if (_viewModel.ConnectPointAdapters != null && _viewModel.ConnectPointAdapters.Count > 0)
                {
                    TopConnectPoint.Adapter = _viewModel.ConnectPointAdapters[0];
                    System.Diagnostics.Debug.WriteLine($"[SFCJumpView] ✅ 已绑定连接点适配器: {_viewModel.Id}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[SFCJumpView] ❌ 警告：跳转元素 {_viewModel.Id} 缺少连接点适配器");
                }
            }
        }

        /// <summary>
        /// 更新视觉状态
        /// </summary>
        private void UpdateVisualState()
        {
            if (DataContext is SFCJumpViewModel jumpViewModel)
            {
                // 选中状态的视觉效果由XAML中的DataTrigger处理
                // 这里可以添加额外的状态更新逻辑
            }
        }

        /// <summary>
        /// 更新步骤号显示
        /// </summary>
        private void UpdateStepNumberDisplay()
        {
            if (StepNumberLabel != null)
            {
                StepNumberLabel.Text = TargetStepNumber > 0 ? $"S{TargetStepNumber}" : "S?";
            }
        }

        /// <summary>
        /// 更新验证显示
        /// </summary>
        private void UpdateValidationDisplay()
        {
            if (ErrorIndicator != null)
            {
                ErrorIndicator.Visibility = IsTargetValid ? Visibility.Collapsed : Visibility.Visible;
            }
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取顶部连接点的位置
        /// </summary>
        public Point GetTopConnectPointPosition()
        {
            // 返回顶部连接点的中心位置
            return new Point(30, 10); // Canvas.Left="25" + Width/2, Canvas.Top="6" + Height/2
        }

        /// <summary>
        /// 获取元素的边界矩形
        /// </summary>
        public Rect GetBounds()
        {
            return new Rect(0, 0, Width, Height);
        }

        /// <summary>
        /// 获取箭头位置（用于外部箭头指示器定位）
        /// </summary>
        public Point GetArrowPosition()
        {
            return new Point(22, 39); // 箭头的中心位置
        }

        #endregion
    }
}
