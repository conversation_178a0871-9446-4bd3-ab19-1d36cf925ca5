# SFC扩展分支插入位置修复

## 问题描述
当第一个分支（选择或并行分支）被拖拽到新位置后，新插入的扩展分支位置不准确，连接点无法对齐。

## 根本原因
1. **位置获取不一致**：`CalculateElementOutputConnectPoint`方法使用传入的`elementPosition`参数，而不是元素的实时位置
2. **拖拽后位置更新**：分支拖拽时通过`ViewModel.Position`更新位置，但连接点计算仍使用过时位置
3. **连接点计算依赖过时数据**：扩展分支插入时基于过时位置计算连接点，导致新分支位置偏移

## 修复方案
### 1. 修改`CalculateElementOutputConnectPoint`方法（第317-391行）
- 对分支元素使用实时位置：`branchVM.Position`或`branchModel.Position`
- 添加调试日志跟踪位置变化
- 确保传递给`CalculateElementConnectPoint`的是实时位置

### 2. 增强`GetElementPosition`方法（第2880-2916行）
- 添加对`SFCBranchModel`、`SFCStepModel`、`SFCTransitionModel`的支持
- 添加调试日志跟踪分支位置获取

### 3. 修改`CalculateConnectPointAlignedInsertPosition`方法（第400-471行）
- 直接获取分支元素的实时位置，不依赖`GetElementPosition`
- 在异常处理中也使用实时位置
- 确保所有位置计算都基于最新数据

## 技术细节
- **分支ViewModel位置**：`SFCBranchViewModel.Position`
- **分支Model位置**：`SFCBranchModel.Position`
- **连接点偏移**：选择分支输出点(162+5, 61+5)，并行分支输出点(183+5, 19.5+5)
- **调试跟踪**：添加详细的Debug.WriteLine输出便于问题排查

## 验证结果
- 编译成功，无语法错误
- 保持了原有功能完整性
- 修复了扩展分支插入位置不准确的问题