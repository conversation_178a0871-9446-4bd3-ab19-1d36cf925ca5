# PC_Control2 WPF自定义控件开发指南

## 📋 概述
本文档详细介绍了PC_Control2项目中WPF自定义控件的开发技术，包括Canvas绘图、用户控件设计、交互行为实现和样式主题系统。

---

## 🎨 Canvas绘图技术

### 1. 图形渲染基础

#### Canvas坐标系统
```csharp
public class SFCCanvas : Canvas
{
    // 逻辑坐标到屏幕坐标转换
    public Point LogicalToScreen(Point logicalPoint)
    {
        return new Point(
            logicalPoint.X * ZoomFactor + PanOffset.X,
            logicalPoint.Y * ZoomFactor + PanOffset.Y);
    }

    // 屏幕坐标到逻辑坐标转换
    public Point ScreenToLogical(Point screenPoint)
    {
        return new Point(
            (screenPoint.X - PanOffset.X) / ZoomFactor,
            (screenPoint.Y - PanOffset.Y) / ZoomFactor);
    }

    // 绘制网格
    protected override void OnRender(DrawingContext dc)
    {
        base.OnRender(dc);
        DrawGrid(dc);
        DrawConnections(dc);
    }

    private void DrawGrid(DrawingContext dc)
    {
        var gridSize = 20 * ZoomFactor;
        var pen = new Pen(Brushes.LightGray, 0.5);

        // 绘制垂直线
        for (double x = 0; x < ActualWidth; x += gridSize)
        {
            dc.DrawLine(pen, new Point(x, 0), new Point(x, ActualHeight));
        }

        // 绘制水平线
        for (double y = 0; y < ActualHeight; y += gridSize)
        {
            dc.DrawLine(pen, new Point(0, y), new Point(ActualWidth, y));
        }
    }
}
```

#### 贝塞尔曲线连接线
```csharp
public class SFCConnectionVisual : FrameworkElement
{
    public static readonly DependencyProperty StartPointProperty =
        DependencyProperty.Register(nameof(StartPoint), typeof(Point), typeof(SFCConnectionVisual),
            new FrameworkPropertyMetadata(default(Point), FrameworkPropertyMetadataOptions.AffectsRender));

    public static readonly DependencyProperty EndPointProperty =
        DependencyProperty.Register(nameof(EndPoint), typeof(Point), typeof(SFCConnectionVisual),
            new FrameworkPropertyMetadata(default(Point), FrameworkPropertyMetadataOptions.AffectsRender));

    public Point StartPoint
    {
        get => (Point)GetValue(StartPointProperty);
        set => SetValue(StartPointProperty, value);
    }

    public Point EndPoint
    {
        get => (Point)GetValue(EndPointProperty);
        set => SetValue(EndPointProperty, value);
    }

    protected override void OnRender(DrawingContext drawingContext)
    {
        var pen = new Pen(Brushes.White, 2);
        
        // 计算贝塞尔曲线控制点
        var controlPoint1 = new Point(StartPoint.X + 50, StartPoint.Y);
        var controlPoint2 = new Point(EndPoint.X - 50, EndPoint.Y);

        // 创建贝塞尔曲线路径
        var pathGeometry = new PathGeometry();
        var pathFigure = new PathFigure { StartPoint = StartPoint };
        
        var bezierSegment = new BezierSegment
        {
            Point1 = controlPoint1,
            Point2 = controlPoint2,
            Point3 = EndPoint
        };
        
        pathFigure.Segments.Add(bezierSegment);
        pathGeometry.Figures.Add(pathFigure);

        // 绘制连接线
        drawingContext.DrawGeometry(null, pen, pathGeometry);

        // 绘制箭头
        DrawArrow(drawingContext, EndPoint, GetArrowDirection(controlPoint2, EndPoint));
    }

    private void DrawArrow(DrawingContext dc, Point point, Vector direction)
    {
        var arrowLength = 10;
        var arrowAngle = Math.PI / 6; // 30度

        direction.Normalize();
        
        var arrowPoint1 = point - arrowLength * direction;
        var perpendicular = new Vector(-direction.Y, direction.X);
        
        var arrowTip1 = arrowPoint1 + arrowLength * 0.5 * Math.Tan(arrowAngle) * perpendicular;
        var arrowTip2 = arrowPoint1 - arrowLength * 0.5 * Math.Tan(arrowAngle) * perpendicular;

        var arrowGeometry = new PathGeometry();
        var arrowFigure = new PathFigure { StartPoint = point };
        arrowFigure.Segments.Add(new LineSegment(arrowTip1, true));
        arrowFigure.Segments.Add(new LineSegment(arrowTip2, true));
        arrowFigure.IsClosed = true;

        arrowGeometry.Figures.Add(arrowFigure);
        dc.DrawGeometry(Brushes.White, null, arrowGeometry);
    }
}
```

### 2. 坐标变换和缩放

#### 变换矩阵管理
```csharp
public class ZoomableCanvas : Canvas
{
    private double _zoomFactor = 1.0;
    private Point _panOffset = new Point(0, 0);
    private TransformGroup _transformGroup;
    private ScaleTransform _scaleTransform;
    private TranslateTransform _translateTransform;

    public double ZoomFactor
    {
        get => _zoomFactor;
        set
        {
            _zoomFactor = Math.Max(0.1, Math.Min(5.0, value));
            UpdateTransform();
        }
    }

    public Point PanOffset
    {
        get => _panOffset;
        set
        {
            _panOffset = value;
            UpdateTransform();
        }
    }

    public ZoomableCanvas()
    {
        InitializeTransforms();
        
        // 启用鼠标滚轮缩放
        MouseWheel += OnMouseWheel;
        
        // 启用中键拖拽平移
        MouseDown += OnMouseDown;
        MouseMove += OnMouseMove;
        MouseUp += OnMouseUp;
    }

    private void InitializeTransforms()
    {
        _scaleTransform = new ScaleTransform();
        _translateTransform = new TranslateTransform();
        
        _transformGroup = new TransformGroup();
        _transformGroup.Children.Add(_scaleTransform);
        _transformGroup.Children.Add(_translateTransform);
        
        RenderTransform = _transformGroup;
    }

    private void UpdateTransform()
    {
        _scaleTransform.ScaleX = _zoomFactor;
        _scaleTransform.ScaleY = _zoomFactor;
        _translateTransform.X = _panOffset.X;
        _translateTransform.Y = _panOffset.Y;
    }

    private void OnMouseWheel(object sender, MouseWheelEventArgs e)
    {
        var zoomCenter = e.GetPosition(this);
        var oldZoom = ZoomFactor;
        
        // 计算新的缩放因子
        var delta = e.Delta > 0 ? 1.1 : 0.9;
        ZoomFactor *= delta;
        
        // 调整平移偏移以保持缩放中心不变
        var zoomChange = ZoomFactor / oldZoom;
        PanOffset = new Point(
            zoomCenter.X - (zoomCenter.X - PanOffset.X) * zoomChange,
            zoomCenter.Y - (zoomCenter.Y - PanOffset.Y) * zoomChange);
    }

    private bool _isPanning;
    private Point _lastPanPoint;

    private void OnMouseDown(object sender, MouseButtonEventArgs e)
    {
        if (e.MiddleButton == MouseButtonState.Pressed)
        {
            _isPanning = true;
            _lastPanPoint = e.GetPosition(this);
            CaptureMouse();
        }
    }

    private void OnMouseMove(object sender, MouseEventArgs e)
    {
        if (_isPanning)
        {
            var currentPoint = e.GetPosition(this);
            var delta = currentPoint - _lastPanPoint;
            
            PanOffset = new Point(PanOffset.X + delta.X, PanOffset.Y + delta.Y);
            _lastPanPoint = currentPoint;
        }
    }

    private void OnMouseUp(object sender, MouseButtonEventArgs e)
    {
        if (e.MiddleButton == MouseButtonState.Released && _isPanning)
        {
            _isPanning = false;
            ReleaseMouseCapture();
        }
    }
}
```

---

## 🎛️ 用户控件设计

### 1. 依赖属性实现

#### SFC连接点控件
```csharp
public partial class SFCConnectPoint : UserControl
{
    // 连接状态依赖属性
    public static readonly DependencyProperty IsConnectedProperty =
        DependencyProperty.Register(nameof(IsConnected), typeof(bool), typeof(SFCConnectPoint),
            new PropertyMetadata(false, OnIsConnectedChanged));

    // 连接点方向依赖属性
    public static readonly DependencyProperty DirectionProperty =
        DependencyProperty.Register(nameof(Direction), typeof(ConnectPointDirection), typeof(SFCConnectPoint),
            new PropertyMetadata(ConnectPointDirection.Input, OnDirectionChanged));

    // 连接点索引依赖属性
    public static readonly DependencyProperty IndexProperty =
        DependencyProperty.Register(nameof(Index), typeof(int), typeof(SFCConnectPoint),
            new PropertyMetadata(0));

    public bool IsConnected
    {
        get => (bool)GetValue(IsConnectedProperty);
        set => SetValue(IsConnectedProperty, value);
    }

    public ConnectPointDirection Direction
    {
        get => (ConnectPointDirection)GetValue(DirectionProperty);
        set => SetValue(DirectionProperty, value);
    }

    public int Index
    {
        get => (int)GetValue(IndexProperty);
        set => SetValue(IndexProperty, value);
    }

    // 属性变化回调
    private static void OnIsConnectedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCConnectPoint connectPoint)
        {
            connectPoint.UpdateVisualState();
        }
    }

    private static void OnDirectionChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCConnectPoint connectPoint)
        {
            connectPoint.UpdateDirectionVisual();
        }
    }

    private void UpdateVisualState()
    {
        // 更新连接点颜色
        var brush = IsConnected ? Brushes.LimeGreen : Brushes.White;
        if (FindName("ConnectPointEllipse") is Ellipse ellipse)
        {
            ellipse.Fill = brush;
        }
    }

    private void UpdateDirectionVisual()
    {
        // 根据方向更新连接点形状
        if (FindName("ConnectPointEllipse") is Ellipse ellipse)
        {
            ellipse.StrokeThickness = Direction == ConnectPointDirection.Input ? 2 : 1;
        }
    }
}
```

### 2. 附加属性实现

#### Canvas位置附加属性
```csharp
public static class CanvasExtensions
{
    // 逻辑位置附加属性
    public static readonly DependencyProperty LogicalPositionProperty =
        DependencyProperty.RegisterAttached("LogicalPosition", typeof(Point), typeof(CanvasExtensions),
            new PropertyMetadata(default(Point), OnLogicalPositionChanged));

    public static Point GetLogicalPosition(DependencyObject obj)
    {
        return (Point)obj.GetValue(LogicalPositionProperty);
    }

    public static void SetLogicalPosition(DependencyObject obj, Point value)
    {
        obj.SetValue(LogicalPositionProperty, value);
    }

    private static void OnLogicalPositionChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is FrameworkElement element && element.Parent is ZoomableCanvas canvas)
        {
            var logicalPosition = (Point)e.NewValue;
            var screenPosition = canvas.LogicalToScreen(logicalPosition);
            
            Canvas.SetLeft(element, screenPosition.X);
            Canvas.SetTop(element, screenPosition.Y);
        }
    }

    // 网格对齐附加属性
    public static readonly DependencyProperty SnapToGridProperty =
        DependencyProperty.RegisterAttached("SnapToGrid", typeof(bool), typeof(CanvasExtensions),
            new PropertyMetadata(false));

    public static bool GetSnapToGrid(DependencyObject obj)
    {
        return (bool)obj.GetValue(SnapToGridProperty);
    }

    public static void SetSnapToGrid(DependencyObject obj, bool value)
    {
        obj.SetValue(SnapToGridProperty, value);
    }
}
```

### 3. 模板化控件

#### SFC步骤模板控件
```csharp
[TemplatePart(Name = "PART_NameTextBlock", Type = typeof(TextBlock))]
[TemplatePart(Name = "PART_ActionTextBox", Type = typeof(TextBox))]
[TemplatePart(Name = "PART_ConnectPointContainer", Type = typeof(Panel))]
public class SFCStepControl : Control
{
    static SFCStepControl()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(SFCStepControl),
            new FrameworkPropertyMetadata(typeof(SFCStepControl)));
    }

    // 步骤名称属性
    public static readonly DependencyProperty StepNameProperty =
        DependencyProperty.Register(nameof(StepName), typeof(string), typeof(SFCStepControl),
            new PropertyMetadata(string.Empty));

    // 动作代码属性
    public static readonly DependencyProperty ActionCodeProperty =
        DependencyProperty.Register(nameof(ActionCode), typeof(string), typeof(SFCStepControl),
            new PropertyMetadata(string.Empty));

    // 是否选中属性
    public static readonly DependencyProperty IsSelectedProperty =
        DependencyProperty.Register(nameof(IsSelected), typeof(bool), typeof(SFCStepControl),
            new PropertyMetadata(false, OnIsSelectedChanged));

    public string StepName
    {
        get => (string)GetValue(StepNameProperty);
        set => SetValue(StepNameProperty, value);
    }

    public string ActionCode
    {
        get => (string)GetValue(ActionCodeProperty);
        set => SetValue(ActionCodeProperty, value);
    }

    public bool IsSelected
    {
        get => (bool)GetValue(IsSelectedProperty);
        set => SetValue(IsSelectedProperty, value);
    }

    private TextBlock _nameTextBlock;
    private TextBox _actionTextBox;
    private Panel _connectPointContainer;

    public override void OnApplyTemplate()
    {
        base.OnApplyTemplate();

        _nameTextBlock = GetTemplateChild("PART_NameTextBlock") as TextBlock;
        _actionTextBox = GetTemplateChild("PART_ActionTextBox") as TextBox;
        _connectPointContainer = GetTemplateChild("PART_ConnectPointContainer") as Panel;

        UpdateVisualState();
    }

    private static void OnIsSelectedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCStepControl control)
        {
            control.UpdateVisualState();
        }
    }

    private void UpdateVisualState()
    {
        VisualStateManager.GoToState(this, IsSelected ? "Selected" : "Normal", true);
    }
}
```

#### 控件样式模板
```xml
<Style TargetType="{x:Type local:SFCStepControl}">
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="{x:Type local:SFCStepControl}">
                <Border x:Name="PART_Border" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 连接点容器 -->
                        <StackPanel x:Name="PART_ConnectPointContainer" 
                                   Grid.Row="0" 
                                   Orientation="Horizontal"
                                   HorizontalAlignment="Center"/>
                        
                        <!-- 步骤内容 -->
                        <StackPanel Grid.Row="1" Margin="10">
                            <TextBlock x:Name="PART_NameTextBlock"
                                     Text="{TemplateBinding StepName}"
                                     FontWeight="Bold"
                                     HorizontalAlignment="Center"/>
                            
                            <TextBox x:Name="PART_ActionTextBox"
                                   Text="{TemplateBinding ActionCode}"
                                   AcceptsReturn="True"
                                   TextWrapping="Wrap"
                                   MinHeight="40"
                                   Margin="0,5,0,0"/>
                        </StackPanel>
                        
                        <!-- 底部连接点 -->
                        <StackPanel Grid.Row="2" 
                                   Orientation="Horizontal"
                                   HorizontalAlignment="Center"/>
                    </Grid>
                </Border>
                
                <ControlTemplate.Triggers>
                    <Trigger Property="IsSelected" Value="True">
                        <Setter TargetName="PART_Border" Property="BorderBrush" Value="Orange"/>
                        <Setter TargetName="PART_Border" Property="BorderThickness" Value="2"/>
                    </Trigger>
                </ControlTemplate.Triggers>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>
```

---

## 🖱️ 交互行为实现

### 1. 鼠标事件处理

#### 拖拽行为实现
```csharp
public class DragBehavior : Behavior<FrameworkElement>
{
    private bool _isDragging;
    private Point _startPoint;
    private Point _originalPosition;

    protected override void OnAttached()
    {
        base.OnAttached();
        AssociatedObject.MouseLeftButtonDown += OnMouseLeftButtonDown;
        AssociatedObject.MouseMove += OnMouseMove;
        AssociatedObject.MouseLeftButtonUp += OnMouseLeftButtonUp;
    }

    protected override void OnDetaching()
    {
        AssociatedObject.MouseLeftButtonDown -= OnMouseLeftButtonDown;
        AssociatedObject.MouseMove -= OnMouseMove;
        AssociatedObject.MouseLeftButtonUp -= OnMouseLeftButtonUp;
        base.OnDetaching();
    }

    private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        var element = sender as FrameworkElement;
        _isDragging = true;
        _startPoint = e.GetPosition(element.Parent as UIElement);
        _originalPosition = new Point(Canvas.GetLeft(element), Canvas.GetTop(element));
        
        element.CaptureMouse();
        e.Handled = true;
    }

    private void OnMouseMove(object sender, MouseEventArgs e)
    {
        if (!_isDragging) return;

        var element = sender as FrameworkElement;
        var currentPoint = e.GetPosition(element.Parent as UIElement);
        var offset = currentPoint - _startPoint;

        var newX = _originalPosition.X + offset.X;
        var newY = _originalPosition.Y + offset.Y;

        // 边界检查
        if (element.Parent is Canvas canvas)
        {
            newX = Math.Max(0, Math.Min(canvas.ActualWidth - element.ActualWidth, newX));
            newY = Math.Max(0, Math.Min(canvas.ActualHeight - element.ActualHeight, newY));
        }

        Canvas.SetLeft(element, newX);
        Canvas.SetTop(element, newY);
    }

    private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
    {
        if (!_isDragging) return;

        var element = sender as FrameworkElement;
        _isDragging = false;
        element.ReleaseMouseCapture();

        // 触发拖拽完成事件
        DragCompleted?.Invoke(this, new DragCompletedEventArgs(
            Canvas.GetLeft(element), Canvas.GetTop(element)));
    }

    public event EventHandler<DragCompletedEventArgs> DragCompleted;
}

public class DragCompletedEventArgs : EventArgs
{
    public double X { get; }
    public double Y { get; }

    public DragCompletedEventArgs(double x, double y)
    {
        X = x;
        Y = y;
    }
}
```

### 2. 键盘处理

#### 快捷键管理
```csharp
public class KeyboardShortcutManager
{
    private readonly Dictionary<KeyGesture, ICommand> _shortcuts = new();

    public void RegisterShortcut(Key key, ModifierKeys modifiers, ICommand command)
    {
        var gesture = new KeyGesture(key, modifiers);
        _shortcuts[gesture] = command;
    }

    public void RegisterShortcut(string gesture, ICommand command)
    {
        var keyGesture = (KeyGesture)new KeyGestureConverter().ConvertFromString(gesture);
        _shortcuts[keyGesture] = command;
    }

    public bool HandleKeyDown(KeyEventArgs e)
    {
        var gesture = new KeyGesture(e.Key, Keyboard.Modifiers);
        
        if (_shortcuts.TryGetValue(gesture, out var command) && command.CanExecute(null))
        {
            command.Execute(null);
            e.Handled = true;
            return true;
        }

        return false;
    }
}

// 在主窗口中使用
public partial class MainWindow : Window
{
    private readonly KeyboardShortcutManager _shortcutManager = new();

    public MainWindow()
    {
        InitializeComponent();
        InitializeShortcuts();
        
        KeyDown += OnKeyDown;
    }

    private void InitializeShortcuts()
    {
        _shortcutManager.RegisterShortcut("Ctrl+S", SaveCommand);
        _shortcutManager.RegisterShortcut("Ctrl+Z", UndoCommand);
        _shortcutManager.RegisterShortcut("Ctrl+Y", RedoCommand);
        _shortcutManager.RegisterShortcut("Delete", DeleteCommand);
        _shortcutManager.RegisterShortcut("Ctrl+A", SelectAllCommand);
    }

    private void OnKeyDown(object sender, KeyEventArgs e)
    {
        _shortcutManager.HandleKeyDown(e);
    }
}
```

### 3. 触摸支持

#### 多点触控处理
```csharp
public class TouchBehavior : Behavior<FrameworkElement>
{
    private readonly Dictionary<int, TouchPoint> _activeTouches = new();
    private double _initialDistance;
    private double _initialScale = 1.0;

    protected override void OnAttached()
    {
        base.OnAttached();
        AssociatedObject.IsManipulationEnabled = true;
        AssociatedObject.ManipulationStarting += OnManipulationStarting;
        AssociatedObject.ManipulationDelta += OnManipulationDelta;
        AssociatedObject.ManipulationCompleted += OnManipulationCompleted;
    }

    private void OnManipulationStarting(object sender, ManipulationStartingEventArgs e)
    {
        e.ManipulationContainer = AssociatedObject.Parent as UIElement;
        e.Handled = true;
    }

    private void OnManipulationDelta(object sender, ManipulationDeltaEventArgs e)
    {
        var element = sender as FrameworkElement;
        
        // 处理平移
        if (e.DeltaManipulation.Translation.Length > 0)
        {
            var transform = element.RenderTransform as TranslateTransform ?? new TranslateTransform();
            transform.X += e.DeltaManipulation.Translation.X;
            transform.Y += e.DeltaManipulation.Translation.Y;
            element.RenderTransform = transform;
        }

        // 处理缩放
        if (Math.Abs(e.DeltaManipulation.Scale.X - 1.0) > 0.01)
        {
            var scaleTransform = element.RenderTransform as ScaleTransform ?? new ScaleTransform();
            scaleTransform.ScaleX *= e.DeltaManipulation.Scale.X;
            scaleTransform.ScaleY *= e.DeltaManipulation.Scale.Y;
            scaleTransform.CenterX = e.ManipulationOrigin.X;
            scaleTransform.CenterY = e.ManipulationOrigin.Y;
            element.RenderTransform = scaleTransform;
        }

        e.Handled = true;
    }

    private void OnManipulationCompleted(object sender, ManipulationCompletedEventArgs e)
    {
        _activeTouches.Clear();
        e.Handled = true;
    }
}
```

---

## 🎨 样式和主题系统

### 1. 资源字典组织

#### 主题资源结构
```xml
<!-- Themes/DarkTheme.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 颜色定义 -->
    <Color x:Key="PrimaryColor">#FF2D2D30</Color>
    <Color x:Key="SecondaryColor">#FF3F3F46</Color>
    <Color x:Key="AccentColor">#FF007ACC</Color>
    <Color x:Key="TextColor">#FFFFFFFF</Color>
    
    <!-- 画刷定义 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}"/>
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="TextBrush" Color="{StaticResource TextColor}"/>
    
    <!-- 渐变画刷 -->
    <LinearGradientBrush x:Key="HeaderGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource SecondaryColor}" Offset="0"/>
        <GradientStop Color="{StaticResource PrimaryColor}" Offset="1"/>
    </LinearGradientBrush>
    
    <!-- 控件样式 -->
    <Style x:Key="DarkButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="10,5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="3">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
```

### 2. 样式继承

#### 基础样式定义
```xml
<!-- Styles/BaseStyles.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 基础控件样式 -->
    <Style x:Key="BaseControlStyle" TargetType="Control">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Margin" Value="2"/>
    </Style>
    
    <!-- 基础按钮样式 -->
    <Style x:Key="BaseButtonStyle" TargetType="Button" BasedOn="{StaticResource BaseControlStyle}">
        <Setter Property="MinWidth" Value="75"/>
        <Setter Property="MinHeight" Value="25"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>
    
    <!-- SFC特定按钮样式 -->
    <Style x:Key="SFCButtonStyle" TargetType="Button" BasedOn="{StaticResource BaseButtonStyle}">
        <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>
```

### 3. 动态主题切换

#### 主题管理器
```csharp
public class ThemeManager : INotifyPropertyChanged
{
    private static ThemeManager _instance;
    public static ThemeManager Instance => _instance ??= new ThemeManager();

    private string _currentTheme = "Dark";
    private readonly Dictionary<string, ResourceDictionary> _themes = new();

    public string CurrentTheme
    {
        get => _currentTheme;
        set
        {
            if (_currentTheme != value)
            {
                _currentTheme = value;
                ApplyTheme(value);
                OnPropertyChanged();
            }
        }
    }

    public event PropertyChangedEventHandler PropertyChanged;

    private ThemeManager()
    {
        LoadThemes();
    }

    private void LoadThemes()
    {
        // 加载深色主题
        var darkTheme = new ResourceDictionary
        {
            Source = new Uri("pack://application:,,,/Themes/DarkTheme.xaml")
        };
        _themes["Dark"] = darkTheme;

        // 加载浅色主题
        var lightTheme = new ResourceDictionary
        {
            Source = new Uri("pack://application:,,,/Themes/LightTheme.xaml")
        };
        _themes["Light"] = lightTheme;
    }

    public void ApplyTheme(string themeName)
    {
        if (!_themes.TryGetValue(themeName, out var themeDict))
            return;

        var app = Application.Current;
        
        // 移除旧主题
        var oldTheme = app.Resources.MergedDictionaries
            .FirstOrDefault(d => _themes.Values.Contains(d));
        if (oldTheme != null)
        {
            app.Resources.MergedDictionaries.Remove(oldTheme);
        }

        // 应用新主题
        app.Resources.MergedDictionaries.Add(themeDict);
        
        // 触发主题变化事件
        ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(themeName));
    }

    public IEnumerable<string> GetAvailableThemes()
    {
        return _themes.Keys;
    }

    public event EventHandler<ThemeChangedEventArgs> ThemeChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class ThemeChangedEventArgs : EventArgs
{
    public string ThemeName { get; }

    public ThemeChangedEventArgs(string themeName)
    {
        ThemeName = themeName;
    }
}
```

#### 主题绑定转换器
```csharp
public class ThemeResourceConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (parameter is string resourceKey)
        {
            return Application.Current.FindResource(resourceKey);
        }
        return DependencyProperty.UnsetValue;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

// 在XAML中使用
<Button Background="{Binding CurrentTheme, 
                           Converter={StaticResource ThemeResourceConverter}, 
                           ConverterParameter=PrimaryBrush}"/>
```

---

## 📋 WPF控件开发检查清单

### 依赖属性
- [ ] 正确注册依赖属性
- [ ] 实现属性变化回调
- [ ] 设置合适的默认值
- [ ] 添加属性验证
- [ ] 支持数据绑定

### 用户控件
- [ ] 继承合适的基类
- [ ] 实现INotifyPropertyChanged
- [ ] 正确处理资源释放
- [ ] 支持设计时数据
- [ ] 提供友好的API

### 交互行为
- [ ] 处理鼠标事件
- [ ] 支持键盘导航
- [ ] 实现触摸支持
- [ ] 提供可访问性支持
- [ ] 正确处理焦点

### 样式主题
- [ ] 组织资源字典结构
- [ ] 实现样式继承
- [ ] 支持主题切换
- [ ] 提供默认样式
- [ ] 考虑高DPI支持

### 性能优化
- [ ] 避免不必要的重绘
- [ ] 使用虚拟化技术
- [ ] 优化绑定性能
- [ ] 合理使用缓存
- [ ] 监控内存使用

---

## 🎯 总结

WPF自定义控件开发需要掌握的核心技术：

1. **Canvas绘图**：坐标变换、图形渲染、缩放平移
2. **控件设计**：依赖属性、模板化控件、用户控件
3. **交互实现**：鼠标键盘处理、触摸支持、行为模式
4. **样式主题**：资源管理、动态切换、继承机制

这些技术的综合运用，能够创建出功能强大、用户体验良好的工业级WPF应用程序。