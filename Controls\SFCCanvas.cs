using PC_Control2.Demo.ViewModels;
using PC_Control2.Demo.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Shapes;
using static PC_Control2.Demo.Controls.SFCConnectPoint;

namespace PC_Control2.Demo.Controls;

/// <summary>
/// SFC流程图专用画布控件 - 增强版交互功能
/// </summary>
public class SFCCanvas : Canvas
{
    private readonly Dictionary<string, FrameworkElement> _stepElements = new();
    private readonly Dictionary<string, FrameworkElement> _transitionElements = new();
    private readonly Dictionary<string, FrameworkElement> _branchElements = new();
    private readonly Dictionary<string, FrameworkElement> _jumpElements = new();
    private readonly Dictionary<string, FrameworkElement> _graphNodeElements = new();
    private readonly Dictionary<string, FrameworkElement> _connectionElements = new();
    private readonly Dictionary<string, Polygon> _connectionArrows = new();

    // 存储跳转箭头指示器的字典
    private readonly Dictionary<string, SFCJumpArrowIndicator> _jumpArrowIndicators = new();

    // 拖拽相关
    private object? _draggingElement;
    private Point _dragStartPoint;
    private Point _originalElementPosition; // 拖拽开始时第一个元素的原始位置
    private Dictionary<object, Point> _originalElementPositions = new(); // 所有选中元素的原始位置
    private bool _isDragging;
    private bool _isConnecting;
    private object? _connectionSource;
    private readonly double _dragThreshold = 5; // 设置合理的拖动阈值
    private Point _lastRightClickPosition; // 最后右键点击位置


    // 拖拽视觉反馈相关
    private Rectangle? _dropZoneIndicator; // 放置区域指示器
    private Ellipse? _positionPreview; // 位置预览圆点
    private Line? _gridLineX, _gridLineY; // 网格对齐线
    private TextBlock? _positionTooltip; // 位置提示文本

    // 对齐辅助相关
    private readonly List<Line> _alignmentLines = new(); // 对齐辅助线集合
    private readonly List<Rectangle> _alignmentHighlights = new(); // 对齐高亮框集合
    private Point _snapPosition; // 吸附后的位置
    private bool _isSnapped = false; // 是否已吸附
    private object? _currentDragData; // 当前拖拽的数据

    // 对齐配置
    private const double GRID_SIZE = 20.0; // 网格大小
    private const double SNAP_DISTANCE = 15.0; // 吸附距离
    private const double ELEMENT_SNAP_DISTANCE = 10.0; // 元素吸附距离

    // 缩放和平移相关
    private ScaleTransform _scaleTransform = null!;
    private TranslateTransform _translateTransform = null!;
    private TransformGroup _transformGroup = null!;
    private double _zoomFactor = 1.0;
    private const double MinZoom = 0.1;
    private const double MaxZoom = 5.0;
    private const double ZoomStep = 0.1;

    // 平移相关
    private bool _isPanning;
    private Point _panStartPoint;
    private Point _panStartOffset;

    // 选择相关
    private readonly List<object> _selectedElements = new();
    private bool _isSelecting;
    private Point _selectionStartPoint;
    private Rectangle? _selectionRectangle;

    // 状态显示
    private TextBlock? _statusTextBlock;

    // 拖拽连接相关变量
    private object? _connectionDragSource = null;
    private bool _isDraggingConnection = false;
    private Point _connectionDragStartPoint;
    private Path? _temporaryConnectionPath;
    private Polygon? _temporaryConnectionArrow;

    // 连接点拖拽功能已移除，只保留自动连接

    // 是否允许元素拖动（满足用户禁用拖动的需求）
    private bool _allowElementDragging = true; // 启用拖动以支持连接线动态更新

    // 添加连接交互管理器字段和属性
    private SFCConnectionUIManager? _connectionUIManager;

    #region 依赖属性

    public static readonly DependencyProperty StepsProperty =
        DependencyProperty.Register("Steps", typeof(ObservableCollection<SFCStepViewModel>),
            typeof(SFCCanvas), new PropertyMetadata(null, OnStepsChanged));

    public static readonly DependencyProperty TransitionsProperty =
        DependencyProperty.Register("Transitions", typeof(ObservableCollection<SFCTransitionViewModel>),
            typeof(SFCCanvas), new PropertyMetadata(null, OnTransitionsChanged));

    public static readonly DependencyProperty BranchesProperty =
        DependencyProperty.Register("Branches", typeof(ObservableCollection<SFCBranchViewModel>),
            typeof(SFCCanvas), new PropertyMetadata(null, OnBranchesChanged));

    public static readonly DependencyProperty ConnectionsProperty =
        DependencyProperty.Register("Connections", typeof(ObservableCollection<SFCConnectionViewModel>),
            typeof(SFCCanvas), new PropertyMetadata(null, OnConnectionsChanged));

    public static readonly DependencyProperty GraphNodesProperty =
        DependencyProperty.Register("GraphNodes", typeof(ObservableCollection<SFCGraphNodeViewModel>),
            typeof(SFCCanvas), new PropertyMetadata(null, OnGraphNodesChanged));

    public static readonly DependencyProperty JumpsProperty =
        DependencyProperty.Register("Jumps", typeof(ObservableCollection<SFCJumpViewModel>),
            typeof(SFCCanvas), new PropertyMetadata(null, OnJumpsChanged));

    /// <summary>
    /// SFC模型依赖属性
    /// </summary>
    public static readonly DependencyProperty SFCModelProperty =
        DependencyProperty.Register("SFCModel", typeof(SFCModel),
            typeof(SFCCanvas), new PropertyMetadata(null, OnSFCModelChanged));

    public ObservableCollection<SFCStepViewModel>? Steps
    {
        get => (ObservableCollection<SFCStepViewModel>?)GetValue(StepsProperty);
        set => SetValue(StepsProperty, value);
    }

    public ObservableCollection<SFCTransitionViewModel>? Transitions
    {
        get => (ObservableCollection<SFCTransitionViewModel>?)GetValue(TransitionsProperty);
        set => SetValue(TransitionsProperty, value);
    }

    public ObservableCollection<SFCBranchViewModel>? Branches
    {
        get => (ObservableCollection<SFCBranchViewModel>?)GetValue(BranchesProperty);
        set => SetValue(BranchesProperty, value);
    }

    public ObservableCollection<SFCConnectionViewModel>? Connections
    {
        get => (ObservableCollection<SFCConnectionViewModel>?)GetValue(ConnectionsProperty);
        set => SetValue(ConnectionsProperty, value);
    }

    public ObservableCollection<SFCGraphNodeViewModel>? GraphNodes
    {
        get => (ObservableCollection<SFCGraphNodeViewModel>?)GetValue(GraphNodesProperty);
        set => SetValue(GraphNodesProperty, value);
    }

    public ObservableCollection<SFCJumpViewModel>? Jumps
    {
        get => (ObservableCollection<SFCJumpViewModel>?)GetValue(JumpsProperty);
        set => SetValue(JumpsProperty, value);
    }

    #endregion

    /// <summary>
    /// 连接UI管理器
    /// </summary>
    public SFCConnectionUIManager? ConnectionUIManager
    {
        get => _connectionUIManager;
        set
        {
            _connectionUIManager = value;
            if (_connectionUIManager != null)
            {
                // 初始化连接视觉效果
                _connectionUIManager.InitializeConnections();
            }
        }
    }

    /// <summary>
    /// 初始化连接交互管理器
    /// </summary>
    private void InitializeConnectionUIManager()
    {
        if (_connectionUIManager == null && SFCModel != null)
        {
            // 创建连接UI管理器
            _connectionUIManager = new SFCConnectionUIManager(
                this, 
                SFCModel.ConnectionManager, 
                SFCModel.ValidationFeedback);
            
            // 初始化连接视觉效果
            _connectionUIManager.InitializeConnections();
            
            // 订阅事件
            _connectionUIManager.ConnectionCreated += OnConnectionCreated;
            _connectionUIManager.ConnectionDeleted += OnConnectionDeleted;
            _connectionUIManager.ConnectionSelected += OnConnectionSelected;
            _connectionUIManager.ValidationStatusChanged += OnValidationStatusChanged;
        }
    }

    /// <summary>
    /// 连接创建事件处理
    /// </summary>
    private void OnConnectionCreated(object? sender, SFCConnectionEventArgs e)
    {
        // 更新连接视觉效果
        _connectionUIManager?.UpdateConnections();
        
        // 更新验证状态
        UpdateValidationStatus();
    }

    /// <summary>
    /// 连接删除事件处理
    /// </summary>
    private void OnConnectionDeleted(object? sender, SFCConnectionEventArgs e)
    {
        // 更新验证状态
        UpdateValidationStatus();
    }

    /// <summary>
    /// 连接选择事件处理
    /// </summary>
    private void OnConnectionSelected(object? sender, SFCConnectionEventArgs e)
    {
        // 可以在这里处理连接选择逻辑
    }

    /// <summary>
    /// 验证状态变化事件处理
    /// </summary>
    private void OnValidationStatusChanged(object? sender, SFCValidationEventArgs e)
    {
        // 更新验证状态可视化
        UpdateValidationVisuals(e.ValidationResult);
    }

    /// <summary>
    /// 更新验证状态
    /// </summary>
    private async void UpdateValidationStatus()
    {
        if (SFCModel?.ValidationSystem != null)
        {
            // 执行验证
            var result = await SFCModel.ValidationSystem.ValidateAllAsync();

            // 更新验证反馈
            SFCModel.ValidationFeedback.UpdateValidationResult(result);

            // 更新验证可视化
            UpdateValidationVisuals(result);
        }
    }

        #region 连接点精确对齐插入位置计算

        /// <summary>
        /// 获取各元素类型的输入连接点相对于元素左上角的偏移量
        /// </summary>
        /// <param name="elementType">元素类型</param>
        /// <returns>输入连接点偏移量</returns>
        private Point GetElementInputConnectPointOffset(string elementType)
        {
            switch (elementType)
            {
                case "Step":
                    // 步骤的输入连接点：Canvas.Left="45" Canvas.Top="1"，连接点控件10x10，中心偏移+5
                    return new Point(45 + 5 + 0.5, 1 + 5 + 4);

                case "Transition":
                    // 转换条件的输入连接点：Canvas.Left="55.3" Canvas.Top="-5"，连接点控件10x10，中心偏移+5
                    return new Point(55.3 + 5 - 0.5, -5 + 5 - 4.5);

                case "Branch":
                case "SelectionBranch":
                    // 选择分支的输入连接点（左上连接点）：Canvas.Left="16" Canvas.Top="2"，连接点控件10x10，中心偏移+5
                    return new Point(16 + 5, 2 + 5);

                case "ParallelBranch":
                    // 并行分支的输入连接点（上端连接点）：Canvas.Left="37" Canvas.Top="-9"，连接点控件10x10，中心偏移+5
                    return new Point(37 + 5, -9 + 5);

                case "Jump":
                    // 跳转元素的输入连接点：Canvas.Left="25" Canvas.Top="6"，连接点控件10x10，中心偏移+5
                    return new Point(25 + 5, 6 + 5);

                default:
                    // 默认使用几何中心作为连接点
                    return new Point(50, 25);
            }
        }

        /// <summary>
        /// 计算元素的输出连接点坐标
        /// </summary>
        /// <param name="element">源元素</param>
        /// <param name="elementPosition">元素位置</param>
        /// <returns>输出连接点的绝对坐标</returns>
        private Point CalculateElementOutputConnectPoint(object element, Point elementPosition)
        {
            // 获取ViewModel来使用现有的精确计算方法
            var enhancedViewModel = DataContext as EnhancedSFCViewModel;
            if (enhancedViewModel != null)
            {
                // 对于分支元素，使用实时位置而不是传入的位置参数
                Point actualPosition = elementPosition;
                if (element is SFCBranchViewModel branchVM)
                {
                    actualPosition = branchVM.Position;
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementOutputConnectPoint] 分支ViewModel实时位置: {actualPosition}");
                }
                else if (element is SFCBranchModel branchModel)
                {
                    actualPosition = branchModel.Position;
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementOutputConnectPoint] 分支Model实时位置: {actualPosition}");
                }

                // 使用现有的精确连接点计算方法，传入实时位置
                return enhancedViewModel.CalculateElementConnectPoint(element, actualPosition, true, 0);
            }

            // 回退到简化计算 - 同样使用实时位置
            Point realPosition = elementPosition;
            if (element is SFCBranchViewModel branchVM2)
            {
                realPosition = branchVM2.Position;
            }
            else if (element is SFCBranchModel branchModel2)
            {
                realPosition = branchModel2.Position;
            }

            if (element is SFCStepViewModel || element is SFCStepModel)
            {
                // 步骤的输出连接点：Canvas.Left="45" Canvas.Top="117"，连接点控件10x10，中心偏移+5
                return new Point(realPosition.X + 45 + 5, realPosition.Y + 117 + 5);
            }
            else if (element is SFCTransitionViewModel || element is SFCTransitionModel)
            {
                // 转换条件的输出连接点：Canvas.Left="55.3" Canvas.Top="25"，连接点控件10x10，中心偏移+5
                return new Point(realPosition.X + 55.3 + 5, realPosition.Y + 25 + 5);
            }
            else if (element is SFCBranchViewModel branchVM3)
            {
                if (branchVM3.BranchType == SFCBranchType.Selection)
                {
                    // 选择分支的输出连接点（右下连接点）：Canvas.Left="162" Canvas.Top="61"，连接点控件10x10，中心偏移+5
                    return new Point(realPosition.X + 162 + 5, realPosition.Y + 61 + 5);
                }
                else if (branchVM3.BranchType == SFCBranchType.Parallel)
                {
                    // 并行分支的输出连接点（右侧连接点）：Canvas.Left="183" Canvas.Top="19.5"，连接点控件10x10，中心偏移+5
                    return new Point(realPosition.X + 183 + 5, realPosition.Y + 19.5 + 5);
                }
            }
            else if (element is SFCBranchModel branchModel3)
            {
                if (branchModel3.BranchType == SFCBranchType.Selection)
                {
                    // 选择分支的输出连接点（右下连接点）
                    return new Point(realPosition.X + 162 + 5, realPosition.Y + 61 + 5);
                }
                else if (branchModel3.BranchType == SFCBranchType.Parallel)
                {
                    // 并行分支的输出连接点（右侧连接点）
                    return new Point(realPosition.X + 183 + 5, realPosition.Y + 19.5 + 5);
                }
            }

            // 默认使用元素底部中心
            var elementSize = GetElementSize(element);
            return new Point(realPosition.X + elementSize.Width / 2, realPosition.Y + elementSize.Height);
        }

        /// <summary>
        /// 计算连接点精确对齐的插入位置
        /// </summary>
        /// <param name="sourceElement">选中的源元素</param>
        /// <param name="targetElementType">要插入的目标元素类型</param>
        /// <param name="targetElementSize">目标元素的UI尺寸</param>
        /// <returns>目标元素的最佳插入位置</returns>
        private Point CalculateConnectPointAlignedInsertPosition(object sourceElement, string targetElementType, Size targetElementSize)
        {
            try
            {
                // 1. 获取源元素实时位置（特别处理分支元素）
                Point sourcePosition;
                if (sourceElement is SFCBranchViewModel branchVM)
                {
                    sourcePosition = branchVM.Position;
                    System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 分支ViewModel实时位置: {sourcePosition}");
                }
                else if (sourceElement is SFCBranchModel branchModel)
                {
                    sourcePosition = branchModel.Position;
                    System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 分支Model实时位置: {sourcePosition}");
                }
                else
                {
                    sourcePosition = GetElementPosition(sourceElement);
                    System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 其他元素位置: {sourcePosition}");
                }

                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 目标元素类型: {targetElementType}");
                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 目标元素尺寸: {targetElementSize}");

                // 2. 计算源元素输出连接点坐标（传入实时位置）
                var sourceOutputPoint = CalculateElementOutputConnectPoint(sourceElement, sourcePosition);
                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 源元素输出连接点: {sourceOutputPoint}");

                // 3. 根据目标元素类型获取输入连接点偏移
                var targetInputOffset = GetElementInputConnectPointOffset(targetElementType);
                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 目标元素输入连接点偏移: {targetInputOffset}");

                // 4. 计算目标元素位置 = 源输出点 - 目标输入偏移
                var targetPosition = new Point(
                    sourceOutputPoint.X - targetInputOffset.X,
                    sourceOutputPoint.Y - targetInputOffset.Y
                );

                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 计算的目标位置: {targetPosition}");

                // 5. 应用网格对齐
                const double gridSize = 20;
                var alignedPosition = SnapToGrid(targetPosition, gridSize);
                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 网格对齐后位置: {alignedPosition}");

                // 6. 验证位置是否在画布范围内
                var validatedPosition = ValidatePositionInCanvas(alignedPosition);
                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 最终验证位置: {validatedPosition}");

                return validatedPosition;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedInsertPosition] 错误: {ex.Message}");
                // 发生错误时回退到原有算法
                Point fallbackPosition;
                if (sourceElement is SFCBranchViewModel branchVM2)
                {
                    fallbackPosition = branchVM2.Position;
                }
                else if (sourceElement is SFCBranchModel branchModel2)
                {
                    fallbackPosition = branchModel2.Position;
                }
                else
                {
                    fallbackPosition = GetElementPosition(sourceElement);
                }
                return CalculateOptimalPosition(fallbackPosition);
            }
        }

        /// <summary>
        /// 验证位置是否在画布范围内，如果不在则调整到合适位置
        /// </summary>
        /// <param name="position">待验证的位置</param>
        /// <returns>调整后的有效位置</returns>
        private Point ValidatePositionInCanvas(Point position)
        {
            const double margin = 50; // 边距
            
            var adjustedX = Math.Max(margin, Math.Min(position.X, ActualWidth - margin));
            var adjustedY = Math.Max(margin, Math.Min(position.Y, ActualHeight - margin));
            
            return new Point(adjustedX, adjustedY);
        }

        /// <summary>
        /// 根据元素类型获取标准UI尺寸
        /// </summary>
        /// <param name="elementType">元素类型</param>
        /// <returns>元素的标准尺寸</returns>
        private Size GetElementSizeByType(string elementType)
        {
            switch (elementType)
            {
                case "Step":
                    // 步骤元素的标准尺寸
                    return new Size(90, 117);

                case "Transition":
                    // 转换条件的标准尺寸
                    return new Size(74, 30);

                case "Branch":
                case "SelectionBranch":
                    // 选择分支的标准尺寸
                    return new Size(200, 80);

                case "ParallelBranch":
                    // 并行分支的标准尺寸
                    return new Size(220, 40);

                case "Jump":
                    // 跳转元素的标准尺寸
                    return new Size(60, 50);

                default:
                    // 默认尺寸
                    return new Size(100, 50);
            }
        }

        /// <summary>
        /// 根据元素对象获取其尺寸
        /// </summary>
        /// <param name="element">元素对象</param>
        /// <returns>元素尺寸</returns>
        private Size GetElementSize(object element)
        {
            if (element is SFCStepViewModel || element is SFCStepModel)
            {
                return GetElementSizeByType("Step");
            }
            else if (element is SFCTransitionViewModel || element is SFCTransitionModel)
            {
                return GetElementSizeByType("Transition");
            }
            else if (element is SFCBranchViewModel branchVM)
            {
                return branchVM.BranchType == SFCBranchType.Parallel 
                    ? GetElementSizeByType("ParallelBranch") 
                    : GetElementSizeByType("SelectionBranch");
            }
            else if (element is SFCBranchModel branchModel)
            {
                return branchModel.BranchType == SFCBranchType.Parallel 
                    ? GetElementSizeByType("ParallelBranch") 
                    : GetElementSizeByType("SelectionBranch");
            }
            else
            {
                // 默认尺寸
                return GetElementSizeByType("Step");
            }
        }

        /// <summary>
        /// 根据节点定义获取对应的元素类型
        /// </summary>
        /// <param name="nodeDefinition">节点定义</param>
        /// <returns>元素类型字符串</returns>
        private string GetElementTypeFromNodeDefinition(NodeDefinition nodeDefinition)
        {
            switch (nodeDefinition.Name)
            {
                case "插入步":
                case "SFC步骤":
                case "等待条件":
                case "延时执行":
                case "流程结束":
                case "子流程调用":
                    return "Step";

                case "转移条件":
                case "SFC转换":
                    return "Transition";

                case "并行分支":
                    return "ParallelBranch";

                case "选择分支":
                    return "SelectionBranch";

                case "SFC跳转":
                    return "Jump";

                default:
                    return "Step";
            }
        }

        #endregion

    /// <summary>
    /// 更新验证可视化
    /// </summary>
    private void UpdateValidationVisuals(SFCValidationResult validationResult)
    {
        if (validationResult == null)
            return;
        
        // 清除所有元素的验证视觉效果
        ClearAllValidationVisuals();
        
        // 为有问题的元素添加验证视觉效果
        foreach (var error in validationResult.Errors)
        {
            if (!string.IsNullOrEmpty(error.ElementId))
            {
                var element = GetElementById(error.ElementId);
                if (element is FrameworkElement frameworkElement)
                {
                    var status = SFCModel?.ValidationFeedback.GetElementValidationStatus(error.ElementId);
                    if (status != null)
                    {
                        Extensions.SFCValidationVisualizationExtensions.AddValidationVisual(frameworkElement, status);
                    }
                }
            }
        }
        
        foreach (var warning in validationResult.Warnings)
        {
            if (!string.IsNullOrEmpty(warning.ElementId))
            {
                var element = GetElementById(warning.ElementId);
                if (element is FrameworkElement frameworkElement)
                {
                    var status = SFCModel?.ValidationFeedback.GetElementValidationStatus(warning.ElementId);
                    if (status != null && !status.HasErrors()) // 只有没有错误时才显示警告
                    {
                        Extensions.SFCValidationVisualizationExtensions.AddValidationVisual(frameworkElement, status);
                    }
                }
            }
        }
        
        // 更新连接验证状态
        _connectionUIManager?.UpdateValidationStatus();
    }

    /// <summary>
    /// 清除所有验证视觉效果
    /// </summary>
    private void ClearAllValidationVisuals()
    {
        // 清除步骤元素的验证视觉效果
        foreach (var element in _stepElements.Values)
        {
            Extensions.SFCValidationVisualizationExtensions.RemoveValidationVisual(element);
        }
        
        // 清除转换元素的验证视觉效果
        foreach (var element in _transitionElements.Values)
        {
            Extensions.SFCValidationVisualizationExtensions.RemoveValidationVisual(element);
        }
        
        // 清除分支元素的验证视觉效果
        foreach (var element in _branchElements.Values)
        {
            Extensions.SFCValidationVisualizationExtensions.RemoveValidationVisual(element);
        }
        
        // 清除图节点元素的验证视觉效果
        foreach (var element in _graphNodeElements.Values)
        {
            Extensions.SFCValidationVisualizationExtensions.RemoveValidationVisual(element);
        }
    }

    /// <summary>
    /// 设置SFC模型
    /// </summary>
    public void SetSFCModel(SFCModel model)
    {
        SFCModel = model;
        
        // 初始化连接交互管理器
        InitializeConnectionUIManager();
        
        // 更新验证状态
        UpdateValidationStatus();
    }

    /// <summary>
    /// 创建UE5蓝图风格的背景
    /// </summary>
    private Brush CreateUE5BlueprintBackground()
    {
        // 创建网格背景
        var drawingGroup = new DrawingGroup();

        // 背景填充
        var backgroundDrawing = new GeometryDrawing
        {
            Geometry = new RectangleGeometry(new Rect(0, 0, 20, 20)),
            Brush = new SolidColorBrush(Color.FromRgb(26, 26, 26)) // 深灰色背景
        };
        drawingGroup.Children.Add(backgroundDrawing);

        // 小网格线
        var smallGridDrawing = new GeometryDrawing();
        var smallGridGeometry = new GeometryGroup();

        // 垂直线
        smallGridGeometry.Children.Add(new LineGeometry(new Point(20, 0), new Point(20, 20)));
        // 水平线
        smallGridGeometry.Children.Add(new LineGeometry(new Point(0, 20), new Point(20, 20)));

        smallGridDrawing.Geometry = smallGridGeometry;
        smallGridDrawing.Pen = new Pen(new SolidColorBrush(Color.FromRgb(42, 42, 42)), 0.5);
        drawingGroup.Children.Add(smallGridDrawing);

        // 创建画刷
        var drawingBrush = new DrawingBrush(drawingGroup)
        {
            TileMode = TileMode.Tile,
            Viewport = new Rect(0, 0, 20, 20),
            ViewportUnits = BrushMappingMode.Absolute,
            Stretch = Stretch.None
        };

        return drawingBrush;
    }

    public SFCCanvas()
    {
        // 确保左上角完全对齐
        Margin = new Thickness(0);

        // 设置画布背景为UE5蓝图风格的网格背景
        Background = CreateUE5BlueprintBackground();
        ClipToBounds = false; // 允许内容超出边界
        Focusable = true;

        // 确保能接收所有鼠标事件
        IsHitTestVisible = true;

        // 初始化变换 - 修复变换初始化
        InitializeTransforms();

        // 初始化状态显示
        InitializeStatusDisplay();

        // 事件处理
        MouseLeftButtonDown += OnMouseLeftButtonDown;
        MouseLeftButtonUp += OnMouseLeftButtonUp;
        MouseMove += OnMouseMove;
        MouseRightButtonDown += OnMouseRightButtonDown;
        MouseWheel += OnMouseWheel;
        KeyDown += OnKeyDown;
        KeyUp += OnKeyUp;

        // 监听DataContext变化以设置ViewModel属性变化监听
        DataContextChanged += OnDataContextChanged;

        // 延迟检查初始DataContext（因为构造时DataContext可能还未设置）
        Loaded += OnSFCCanvasLoaded;

        // 设置拖拽放置支持
        AllowDrop = true;
        DragEnter += OnDragEnter;
        DragOver += OnDragOver;
        DragLeave += OnDragLeave;
        Drop += OnDrop;
        MouseEnter += OnMouseEnter;
        PreviewMouseDown += OnPreviewMouseDown;
        PreviewMouseUp += OnPreviewMouseUp;
        SizeChanged += OnSizeChanged;

        // 设置光标
        Cursor = Cursors.Arrow;

        // 初始化状态显示
        InitializeStatusDisplay();
        
        // 在控件加载完成后初始化连接交互管理器
        this.Loaded += (s, e) => 
        {
            if (SFCModel != null)
            {
                InitializeConnectionUIManager();
                UpdateValidationStatus();
            }
        };
    }

    /// <summary>
    /// 鼠标进入时获取焦点，确保键盘事件能正常工作
    /// </summary>
    private void OnMouseEnter(object sender, MouseEventArgs e)
    {
        if (!IsFocused)
        {
            Focus();
        }
    }

    /// <summary>
    /// 尺寸变化时更新背景
    /// </summary>
    private void OnSizeChanged(object sender, SizeChangedEventArgs e)
    {
        // 更新UE5蓝图风格背景
        UpdateGridBackground();
        UpdateStatusDisplay($"画布尺寸: {e.NewSize.Width:F0}x{e.NewSize.Height:F0} | UE5蓝图风格画布");
    }

    /// <summary>
    /// 初始化状态显示
    /// </summary>
    private void InitializeStatusDisplay()
    {
        _statusTextBlock = new TextBlock
        {
            Text = "就绪 | Ctrl+滚轮:缩放 | 中键/Space+拖拽:平移 | Ctrl+0:重置",
            Foreground = new SolidColorBrush(Color.FromRgb(176, 176, 176)),
            FontSize = 12,
            Margin = new Thickness(10, 10, 10, 10),
            HorizontalAlignment = HorizontalAlignment.Left,
            VerticalAlignment = VerticalAlignment.Top,
            Background = new SolidColorBrush(Color.FromArgb(200, 45, 45, 48)),
            Padding = new Thickness(8, 4, 8, 4)
        };

        Children.Add(_statusTextBlock);
        Canvas.SetLeft(_statusTextBlock, 10);
        Canvas.SetTop(_statusTextBlock, 10);
        Canvas.SetZIndex(_statusTextBlock, 1000);
    }

    /// <summary>
    /// 更新状态显示
    /// </summary>
    private void UpdateStatusDisplay(string message)
    {
        if (_statusTextBlock != null)
        {
            _statusTextBlock.Text = message;
        }
    }

    /// <summary>
    /// 初始化变换组 - 修复无限画布变换
    /// </summary>
    private void InitializeTransforms()
    {
        _scaleTransform = new ScaleTransform(1.0, 1.0);
        _translateTransform = new TranslateTransform(0.0, 0.0);

        _transformGroup = new TransformGroup();
        _transformGroup.Children.Add(_scaleTransform);
        _transformGroup.Children.Add(_translateTransform);

        // 应用变换到画布内容，而不是画布本身
        RenderTransform = _transformGroup;
        RenderTransformOrigin = new Point(0, 0); // 修改为左上角原点，实现无限画布效果
    }

    #region 无限画布背景网格

    /// <summary>
    /// 创建无限网格背景 - 根据当前变换状态
    /// </summary>
    private Brush CreateInfiniteGridBackground()
    {
        var drawingGroup = new DrawingGroup();

        // 计算当前网格大小和偏移
        var baseGridSize = 20.0;
        var currentGridSize = _scaleTransform != null ? baseGridSize * _scaleTransform.ScaleX : baseGridSize;

        // 创建网格线
        var gridPen = new Pen(new SolidColorBrush(Color.FromRgb(42, 42, 42)), 0.5);
        gridPen.Freeze();

        // 小网格
        var smallGridDrawing = new GeometryDrawing();
        var smallGridGeometry = new GeometryGroup();

        // 垂直线
        for (int i = 0; i <= baseGridSize; i += (int)baseGridSize)
        {
            smallGridGeometry.Children.Add(new LineGeometry(new Point(i, 0), new Point(i, baseGridSize)));
        }
        // 水平线
        for (int i = 0; i <= baseGridSize; i += (int)baseGridSize)
        {
            smallGridGeometry.Children.Add(new LineGeometry(new Point(0, i), new Point(baseGridSize, i)));
        }

        smallGridDrawing.Geometry = smallGridGeometry;
        smallGridDrawing.Pen = gridPen;
        drawingGroup.Children.Add(smallGridDrawing);

        // 背景填充
        var backgroundDrawing = new GeometryDrawing
        {
            Geometry = new RectangleGeometry(new Rect(0, 0, baseGridSize, baseGridSize)),
            Brush = new SolidColorBrush(Color.FromRgb(30, 30, 30))
        };
        drawingGroup.Children.Insert(0, backgroundDrawing);

        var drawingBrush = new DrawingBrush(drawingGroup)
        {
            TileMode = TileMode.Tile,
            Viewport = new Rect(0, 0, currentGridSize, currentGridSize),
            ViewportUnits = BrushMappingMode.Absolute,
            Stretch = Stretch.None
        };

        // 计算平移偏移
        if (_translateTransform != null)
        {
            var offsetX = _translateTransform.X % currentGridSize;
            var offsetY = _translateTransform.Y % currentGridSize;
            drawingBrush.Transform = new TranslateTransform(offsetX, offsetY);
        }

        // 不要冻结，这样可以在需要时重新创建
        return drawingBrush;
    }

    /// <summary>
    /// 更新网格背景以适应当前的缩放和平移 - UE5蓝图风格
    /// </summary>
    private void UpdateGridBackground()
    {
        // 重新创建背景以适应当前的变换
        Background = CreateUE5BlueprintBackground();
    }

    #endregion

    #region 集合变化处理

    private static void OnStepsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCCanvas canvas)
        {
            if (e.OldValue is ObservableCollection<SFCStepViewModel> oldSteps)
            {
                oldSteps.CollectionChanged -= canvas.OnStepsCollectionChanged;
            }

            if (e.NewValue is ObservableCollection<SFCStepViewModel> newSteps)
            {
                newSteps.CollectionChanged += canvas.OnStepsCollectionChanged;
                canvas.RefreshSteps();
            }
        }
    }

    private static void OnTransitionsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCCanvas canvas)
        {
            if (e.OldValue is ObservableCollection<SFCTransitionViewModel> oldTransitions)
            {
                oldTransitions.CollectionChanged -= canvas.OnTransitionsCollectionChanged;
            }

            if (e.NewValue is ObservableCollection<SFCTransitionViewModel> newTransitions)
            {
                newTransitions.CollectionChanged += canvas.OnTransitionsCollectionChanged;
                canvas.RefreshTransitions();
            }
        }
    }

    private static void OnBranchesChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCCanvas canvas)
        {
            if (e.OldValue is ObservableCollection<SFCBranchViewModel> oldBranches)
            {
                oldBranches.CollectionChanged -= canvas.OnBranchesCollectionChanged;
            }

            if (e.NewValue is ObservableCollection<SFCBranchViewModel> newBranches)
            {
                newBranches.CollectionChanged += canvas.OnBranchesCollectionChanged;
                canvas.RefreshBranches();
            }
        }
    }

    private static void OnConnectionsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCCanvas canvas)
        {
            if (e.OldValue is ObservableCollection<SFCConnectionViewModel> oldConnections)
            {
                oldConnections.CollectionChanged -= canvas.OnConnectionsCollectionChanged;
            }

            if (e.NewValue is ObservableCollection<SFCConnectionViewModel> newConnections)
            {
                newConnections.CollectionChanged += canvas.OnConnectionsCollectionChanged;
                canvas.RefreshConnections();
            }
        }
    }

    private static void OnGraphNodesChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCCanvas canvas)
        {
            if (e.OldValue is ObservableCollection<SFCGraphNodeViewModel> oldGraphNodes)
            {
                oldGraphNodes.CollectionChanged -= canvas.OnGraphNodesCollectionChanged;
            }

            if (e.NewValue is ObservableCollection<SFCGraphNodeViewModel> newGraphNodes)
            {
                newGraphNodes.CollectionChanged += canvas.OnGraphNodesCollectionChanged;
                canvas.RefreshGraphNodes();
            }
        }
    }

    private static void OnJumpsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCCanvas canvas)
        {
            if (e.OldValue is ObservableCollection<SFCJumpViewModel> oldJumps)
            {
                oldJumps.CollectionChanged -= canvas.OnJumpsCollectionChanged;
            }

            if (e.NewValue is ObservableCollection<SFCJumpViewModel> newJumps)
            {
                newJumps.CollectionChanged += canvas.OnJumpsCollectionChanged;
                canvas.RefreshJumps();
            }
        }
    }

    private void OnStepsCollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        switch (e.Action)
        {
            case NotifyCollectionChangedAction.Add:
                if (e.NewItems != null)
                {
                    foreach (SFCStepViewModel step in e.NewItems)
                    {
                        AddStepElement(step);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Remove:
                if (e.OldItems != null)
                {
                    foreach (SFCStepViewModel step in e.OldItems)
                    {
                        RemoveStepElement(step);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Reset:
                RefreshSteps();
                break;
        }
    }

    private void OnTransitionsCollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        switch (e.Action)
        {
            case NotifyCollectionChangedAction.Add:
                if (e.NewItems != null)
                {
                    foreach (SFCTransitionViewModel transition in e.NewItems)
                    {
                        AddTransitionElement(transition);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Remove:
                if (e.OldItems != null)
                {
                    foreach (SFCTransitionViewModel transition in e.OldItems)
                    {
                        RemoveTransitionElement(transition);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Reset:
                RefreshTransitions();
                break;
        }
    }

    private void OnBranchesCollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        switch (e.Action)
        {
            case NotifyCollectionChangedAction.Add:
                if (e.NewItems != null)
                {
                    foreach (SFCBranchViewModel branch in e.NewItems)
                    {
                        AddBranchElement(branch);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Remove:
                if (e.OldItems != null)
                {
                    foreach (SFCBranchViewModel branch in e.OldItems)
                    {
                        RemoveBranchElement(branch);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Reset:
                RefreshBranches();
                break;
        }
    }

    private void OnConnectionsCollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        switch (e.Action)
        {
            case NotifyCollectionChangedAction.Add:
                if (e.NewItems != null)
                {
                    foreach (SFCConnectionViewModel connection in e.NewItems)
                    {
                        AddConnectionElement(connection);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Remove:
                if (e.OldItems != null)
                {
                    foreach (SFCConnectionViewModel connection in e.OldItems)
                    {
                        RemoveConnectionElement(connection);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Reset:
                RefreshConnections();
                break;
        }
    }

    private void OnGraphNodesCollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        switch (e.Action)
        {
            case NotifyCollectionChangedAction.Add:
                if (e.NewItems != null)
                {
                    foreach (SFCGraphNodeViewModel graphNode in e.NewItems)
                    {
                        AddGraphNodeElement(graphNode);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Remove:
                if (e.OldItems != null)
                {
                    foreach (SFCGraphNodeViewModel graphNode in e.OldItems)
                    {
                        RemoveGraphNodeElement(graphNode);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Reset:
                RefreshGraphNodes();
                break;
        }
    }

    private void OnJumpsCollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        switch (e.Action)
        {
            case NotifyCollectionChangedAction.Add:
                if (e.NewItems != null)
                {
                    foreach (SFCJumpViewModel jump in e.NewItems)
                    {
                        AddJumpElement(jump);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Remove:
                if (e.OldItems != null)
                {
                    foreach (SFCJumpViewModel jump in e.OldItems)
                    {
                        RemoveJumpElement(jump);
                    }
                }
                break;
            case NotifyCollectionChangedAction.Reset:
                RefreshJumps();
                break;
        }
    }

    #endregion

    #region 元素管理

    private void RefreshSteps()
    {
        // 清除现有步骤元素
        foreach (var element in _stepElements.Values)
        {
            Children.Remove(element);
        }
        _stepElements.Clear();

        // 添加新的步骤元素
        if (Steps != null)
        {
            foreach (var step in Steps)
            {
                AddStepElement(step);
            }
        }
    }

    private void RefreshTransitions()
    {
        // 清除现有转换元素
        foreach (var element in _transitionElements.Values)
        {
            Children.Remove(element);
        }
        _transitionElements.Clear();

        // 添加新的转换元素
        if (Transitions != null)
        {
            foreach (var transition in Transitions)
            {
                AddTransitionElement(transition);
            }
        }
    }

    private void RefreshBranches()
    {
        // 清除现有分支元素
        foreach (var element in _branchElements.Values)
        {
            Children.Remove(element);
        }
        _branchElements.Clear();

        // 添加新的分支元素
        if (Branches != null)
        {
            foreach (var branch in Branches)
            {
                AddBranchElement(branch);
            }
        }
    }

    private void RefreshConnections()
    {
        // 清除现有连接元素
        foreach (var element in _connectionElements.Values)
        {
            Children.Remove(element);
        }
        _connectionElements.Clear();
        
        // 清除现有箭头
        foreach (var arrow in _connectionArrows.Values)
        {
            Children.Remove(arrow);
        }
        _connectionArrows.Clear();

        // 添加新的连接元素
        if (Connections != null)
        {
            foreach (var connection in Connections)
            {
                AddConnectionElement(connection);
            }
        }
    }

    private void RefreshGraphNodes()
    {
        // 清除现有Graph节点元素
        foreach (var element in _graphNodeElements.Values)
        {
            Children.Remove(element);
        }
        _graphNodeElements.Clear();

        // 添加新的Graph节点元素
        if (GraphNodes != null)
        {
            foreach (var graphNode in GraphNodes)
            {
                AddGraphNodeElement(graphNode);
            }
        }
    }

    private void RefreshJumps()
    {
        // 清除现有跳转元素
        foreach (var element in _jumpElements.Values)
        {
            Children.Remove(element);
        }
        _jumpElements.Clear();

        // 添加新的跳转元素
        if (Jumps != null)
        {
            foreach (var jump in Jumps)
            {
                AddJumpElement(jump);
            }
        }
    }

    #endregion

    #region 元素创建和移除

    private void AddStepElement(SFCStepViewModel step)
    {
        FrameworkElement stepView;

        // 检查是否为终止元素，使用专用的终止控件
        if (step.Description == "TERMINATOR_ELEMENT")
        {
            stepView = new SFCTerminatorView
            {
                DataContext = step
            };
        }
        else
        {
            stepView = new SFCStepView
            {
                DataContext = step
            };
        }

        // 设置位置
        SetLeft(stepView, step.Position.X);
        SetTop(stepView, step.Position.Y);

        // 连接线样式由XAML中的定义控制，不再强制覆盖
        // 之前这里调用ApplyUniformConnectionLineStyle()导致XAML中的属性被覆盖，现在已禁用
        /*
        stepView.Loaded += (s, e) => {
            stepView.ApplyUniformConnectionLineStyle();
        };
        */

        // 监听位置变化（UI更新）
        step.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(step.Position))
            {
                SetLeft(stepView, step.Position.X);
                SetTop(stepView, step.Position.Y);
            }
        };

        // 添加连接线更新监听
        step.PropertyChanged += OnElementPositionChanged;
        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 为新创建的步骤添加位置变化监听: {step.Id}");

        _stepElements[step.Id] = stepView;
        Children.Add(stepView);
    }

    private void RemoveStepElement(SFCStepViewModel step)
    {
        if (_stepElements.TryGetValue(step.Id, out var element))
        {
            Children.Remove(element);
            _stepElements.Remove(step.Id);
        }
    }

    private void AddTransitionElement(SFCTransitionViewModel transition)
    {
        var transitionView = new SFCTransitionView
        {
            DataContext = transition
        };

        // 设置位置
        SetLeft(transitionView, transition.Position.X);
        SetTop(transitionView, transition.Position.Y);

        // 监听位置变化（UI更新）
        transition.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(transition.Position))
            {
                SetLeft(transitionView, transition.Position.X);
                SetTop(transitionView, transition.Position.Y);
            }
        };

        // 添加连接线更新监听
        transition.PropertyChanged += OnElementPositionChanged;
        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 为新创建的转换条件添加位置变化监听: {transition.Id}");

        _transitionElements[transition.Id] = transitionView;
        Children.Add(transitionView);
    }

    private void RemoveTransitionElement(SFCTransitionViewModel transition)
    {
        if (_transitionElements.TryGetValue(transition.Id, out var element))
        {
            Children.Remove(element);
            _transitionElements.Remove(transition.Id);
        }
    }

    private void AddBranchElement(SFCBranchViewModel branch)
    {
        var branchView = new SFCBranchView
        {
            DataContext = branch
        };

        // 设置位置
        SetLeft(branchView, branch.Position.X);
        SetTop(branchView, branch.Position.Y);

        // 监听位置变化（UI更新）
        branch.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(branch.Position))
            {
                SetLeft(branchView, branch.Position.X);
                SetTop(branchView, branch.Position.Y);
            }
        };

        // 添加连接线更新监听
        branch.PropertyChanged += OnElementPositionChanged;
        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 为新创建的分支添加位置变化监听: {branch.Id}");

        _branchElements[branch.Id] = branchView;
        Children.Add(branchView);
    }

    private void RemoveBranchElement(SFCBranchViewModel branch)
    {
        if (_branchElements.TryGetValue(branch.Id, out var element))
        {
            Children.Remove(element);
            _branchElements.Remove(branch.Id);
        }
    }

    private void AddGraphNodeElement(SFCGraphNodeViewModel graphNode)
    {
        FrameworkElement nodeView;

        // 对于步骤类型的GraphNode，使用统一的SFCStepView
        if (graphNode.NodeType == SFCGraphNodeType.Step)
        {
            var stepView = new SFCStepView
            {
                DataContext = graphNode
            };

            // 连接线样式由XAML中的定义控制，不再强制覆盖
            // 之前这里的强制设置导致XAML中的Height属性被覆盖，现在已禁用
            /*
            stepView.Loaded += (s, e) => {
                if (stepView.TopConnectionLine != null)
                {
                    stepView.TopConnectionLine.Height = 20;
                    stepView.TopConnectionLine.Margin = new Thickness(0, -20, 0, 0);
                }
                if (stepView.BottomConnectionLine != null)
                {
                    stepView.BottomConnectionLine.Height = 20;
                    stepView.BottomConnectionLine.Margin = new Thickness(0, 0, 0, -20);
                }
            };
            */

            nodeView = stepView;
        }
        else
        {
            // 其他类型继续使用SFCGraphNodeView
            nodeView = new SFCGraphNodeView
            {
                DataContext = graphNode
            };
        }

        // 设置位置
        SetLeft(nodeView, graphNode.Position.X);
        SetTop(nodeView, graphNode.Position.Y);

        // 监听位置变化
        graphNode.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(graphNode.Position))
            {
                SetLeft(nodeView, graphNode.Position.X);
                SetTop(nodeView, graphNode.Position.Y);
            }
        };

        _graphNodeElements[graphNode.Id] = nodeView;
        Children.Add(nodeView);
    }

    private void RemoveGraphNodeElement(SFCGraphNodeViewModel graphNode)
    {
        if (_graphNodeElements.TryGetValue(graphNode.Id, out var element))
        {
            Children.Remove(element);
            _graphNodeElements.Remove(graphNode.Id);
        }
    }

    private void AddJumpElement(SFCJumpViewModel jump)
    {
        var jumpView = new SFCJumpView
        {
            DataContext = jump
        };

        // 设置位置
        SetLeft(jumpView, jump.Position.X);
        SetTop(jumpView, jump.Position.Y);

        // 监听位置变化
        jump.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(jump.Position))
            {
                SetLeft(jumpView, jump.Position.X);
                SetTop(jumpView, jump.Position.Y);
            }
        };

        // 添加连接线更新监听
        jump.PropertyChanged += OnElementPositionChanged;
        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 为新创建的跳转元素添加位置变化监听: {jump.Id}");

        _jumpElements[jump.Id] = jumpView;
        Children.Add(jumpView);

        // 如果跳转目标有效，创建跳转指示器
        if (jump.IsTargetValid && jump.TargetStepNumber > 0)
        {
            // 通过DataContext获取ViewModel来查找目标步骤
            if (DataContext is EnhancedSFCViewModel viewModel)
            {
                var targetStep = viewModel.StepViewModels.FirstOrDefault(s => s.Id == jump.TargetStepId);
                if (targetStep != null)
                {
                    CreateJumpArrowIndicatorUI(jump.Id, jump.TargetStepNumber, targetStep.Position);
                }
            }
        }
    }

    private void RemoveJumpElement(SFCJumpViewModel jump)
    {
        if (_jumpElements.TryGetValue(jump.Id, out var element))
        {
            Children.Remove(element);
            _jumpElements.Remove(jump.Id);
        }

        // 同时移除对应的跳转指示器
        RemoveJumpArrowIndicatorUI(jump.Id);
    }

    private void AddConnectionElement(SFCConnectionViewModel connection)
    {
        // 检查连接是否已经存在，避免重复添加
        if (_connectionElements.ContainsKey(connection.Id))
        {
            System.Diagnostics.Debug.WriteLine($"[AddConnectionElement] 连接 {connection.Id} 已存在，跳过重复添加");
            return;
        }

        // 延迟创建连接线，确保连接点位置已经被正确更新
        Dispatcher.BeginInvoke(new Action(() =>
        {
            // 再次检查，因为在延迟执行期间可能已经被添加
            if (_connectionElements.ContainsKey(connection.Id))
            {
                System.Diagnostics.Debug.WriteLine($"[AddConnectionElement] 延迟执行时连接 {connection.Id} 已存在，跳过重复添加");
                return;
            }

            // 创建连接线（如果距离足够长）
            if (connection.PathPoints.Count >= 2)
            {
                var path = CreateConnectionPath(connection);
                if (path != null)
                {
                    _connectionElements[connection.Id] = path;
                    Children.Add(path);

                    // 监听连接属性变化
                    connection.PropertyChanged += (s, e) =>
                    {
                        if (e.PropertyName == nameof(connection.IsSelected))
                        {
                            UpdateConnectionStyle(connection, path);
                        }
                    };

                    // 监听PathPoints集合变化
                    connection.PathPoints.CollectionChanged += (s, e) =>
                    {
                        // PathPoints集合变化后，重新评估连接线是否应该显示
                        System.Diagnostics.Debug.WriteLine($"[PathPoints集合变化] 连接 {connection.Id} 的PathPoints已更新，重新评估连接线");
                        // 暂时注释掉自动更新，避免递归调用问题
                        // UpdateConnectionLine(connection);
                    };

                    System.Diagnostics.Debug.WriteLine($"添加连接线: {connection.Id}");
                }
                else
                {
                    // 连接点重叠，不显示连接线，但保持连接关系
                    System.Diagnostics.Debug.WriteLine($"连接 {connection.Id} 连接点重叠，使用连接点重叠方式");
                }
            }
        }), System.Windows.Threading.DispatcherPriority.Loaded);
    }
    
    // 处理连接线的鼠标进入事件
    private void OnConnectionMouseEnter(SFCConnectionViewModel connection, Path path)
    {
        // 高亮显示连接线
        path.StrokeThickness = 3;
        path.Stroke = new SolidColorBrush(Color.FromRgb(255, 215, 0)); // 金黄色
        
        // 高亮显示箭头
        if (_connectionArrows.TryGetValue(connection.Id, out var arrow))
        {
            arrow.Fill = new SolidColorBrush(Color.FromRgb(255, 215, 0));
        }
        
        // 改变鼠标光标
        Mouse.OverrideCursor = Cursors.Hand;
        
        // 更新状态显示
        UpdateStatusDisplay($"连接线: {connection.SourceId} → {connection.TargetId}");
    }

    // 处理连接线的鼠标离开事件
    private void OnConnectionMouseLeave(SFCConnectionViewModel connection, Path path)
    {
        // 如果连接线未被选中，恢复默认样式
        if (!connection.IsSelected)
        {
            // 恢复连接线样式
            path.StrokeThickness = 2;
            
            // 根据连接类型设置不同的样式
            switch (connection.ConnectionType)
            {
                case SFCConnectionType.Jump:
                    path.Stroke = new SolidColorBrush(Color.FromRgb(0, 120, 215));
                    if (_connectionArrows.TryGetValue(connection.Id, out var jumpArrow))
                    {
                        jumpArrow.Fill = new SolidColorBrush(Color.FromRgb(0, 120, 215));
                    }
                    break;
                case SFCConnectionType.Return:
                    path.Stroke = new SolidColorBrush(Color.FromRgb(215, 0, 120));
                    if (_connectionArrows.TryGetValue(connection.Id, out var returnArrow))
                    {
                        returnArrow.Fill = new SolidColorBrush(Color.FromRgb(215, 0, 120));
                    }
                    break;
                default:
                    path.Stroke = Brushes.White; // 纯白色
                    if (_connectionArrows.TryGetValue(connection.Id, out var defaultArrow))
                    {
                        defaultArrow.Fill = Brushes.White;
                    }
                    break;
            }
        }
        
        // 恢复鼠标光标
        Mouse.OverrideCursor = null;
        
        // 恢复状态显示
        UpdateStatusDisplay("就绪 | Ctrl+滚轮:缩放 | 中键/Space+拖拽:平移 | Ctrl+0:重置");
    }

    // 处理连接线的鼠标点击事件
    private void OnConnectionMouseLeftButtonDown(SFCConnectionViewModel connection, Path path, MouseButtonEventArgs e)
    {
        // 选中连接线
        ClearSelection();
        connection.IsSelected = true;
        
        // 高亮显示连接线
        path.StrokeThickness = 3;
        path.Stroke = new SolidColorBrush(Color.FromRgb(255, 149, 0)); // 橙色
        
        // 高亮显示箭头
        if (_connectionArrows.TryGetValue(connection.Id, out var arrow))
        {
            arrow.Fill = new SolidColorBrush(Color.FromRgb(255, 149, 0));
        }
        
        // 阻止事件冒泡
        e.Handled = true;
        
        // 更新状态显示
        UpdateStatusDisplay($"已选中连接线: {connection.SourceId} → {connection.TargetId}");
    }

    private void RemoveConnectionElement(SFCConnectionViewModel connection)
    {
        if (_connectionElements.TryGetValue(connection.Id, out var path))
        {
            Children.Remove(path);
            _connectionElements.Remove(connection.Id);
        }
        
        // 同时移除箭头
        if (_connectionArrows.TryGetValue(connection.Id, out var arrow))
        {
            Children.Remove(arrow);
            _connectionArrows.Remove(connection.Id);
        }
        
        // 找出并删除以connection.Id为Tag的所有元素
        List<UIElement> elementsToRemove = new List<UIElement>();
        foreach (UIElement element in Children)
        {
            if (element is FrameworkElement fwElement && 
                fwElement.Tag is SFCConnectionViewModel connVM && 
                connVM.Id == connection.Id)
            {
                elementsToRemove.Add(element);
            }
        }
        
        foreach (var element in elementsToRemove)
        {
            Children.Remove(element);
        }
    }

    private Path? CreateConnectionPath(SFCConnectionViewModel connection)
    {
        if (connection.PathPoints.Count < 2)
            return null;

        // 修改1：检查是否为并行分支之间的连接，如果是则不显示连接线
        if (IsParallelBranchConnection(connection))
        {
            return null; // 不创建连接线，避免显示白色斜线
        }

        // 计算连接线的长度
        var startPoint = connection.PathPoints[0];
        var endPoint = connection.PathPoints[connection.PathPoints.Count - 1];
        var distance = Math.Sqrt(Math.Pow(endPoint.X - startPoint.X, 2) + Math.Pow(endPoint.Y - startPoint.Y, 2));

        // 检查连接点是否重叠（连接点是10x10像素的圆形，半径为5像素）
        if (AreConnectPointsOverlapping(startPoint, endPoint))
        {
            System.Diagnostics.Debug.WriteLine($"[CreateConnectionPath] 连接 {connection.Id} 连接点重叠，隐藏连接线");
            System.Diagnostics.Debug.WriteLine($"[CreateConnectionPath] 起点: ({startPoint.X:F1}, {startPoint.Y:F1}), 终点: ({endPoint.X:F1}, {endPoint.Y:F1})");
            return null; // 连接点重叠，不创建连接线
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"[CreateConnectionPath] 连接 {connection.Id} 连接点不重叠，创建连接线");
            System.Diagnostics.Debug.WriteLine($"[CreateConnectionPath] 起点: ({startPoint.X:F1}, {startPoint.Y:F1}), 终点: ({endPoint.X:F1}, {endPoint.Y:F1}), 距离: {distance:F1}px");
        }

        var path = new Path
        {
            Stroke = Brushes.White, // 纯白色连接线
            StrokeThickness = 2,
            Fill = null,
            Tag = connection,
            StrokeLineJoin = PenLineJoin.Round,
            StrokeStartLineCap = PenLineCap.Round,
            StrokeEndLineCap = PenLineCap.Round
        };

        // 创建贝塞尔曲线几何
        var geometry = new PathGeometry();
        var figure = new PathFigure
        {
            StartPoint = connection.PathPoints[0],
            IsClosed = false
        };

        if (connection.PathPoints.Count == 2)
        {
            // 两点之间创建贝塞尔曲线，确保起点和终点精确落在连接点上
            var bezierStartPoint = connection.PathPoints[0];
            var bezierEndPoint = connection.PathPoints[1];

            // 计算控制点偏移，使用更保守的算法确保连接线不悬空
            double yDiff = Math.Abs(bezierEndPoint.Y - bezierStartPoint.Y);
            double xDiff = Math.Abs(bezierEndPoint.X - bezierStartPoint.X);

            // 控制点偏移基于Y轴距离，但不超过Y轴距离的一半，避免过度弯曲
            double controlPointOffset = Math.Min(50, Math.Max(10, yDiff * 0.4));

            // 如果Y轴距离很小，使用X轴距离的一部分作为偏移
            if (yDiff < 15)
            {
                controlPointOffset = Math.Min(30, Math.Max(8, xDiff * 0.2));
            }

            var controlPoint1 = new Point(bezierStartPoint.X, bezierStartPoint.Y + controlPointOffset);
            var controlPoint2 = new Point(bezierEndPoint.X, bezierEndPoint.Y - controlPointOffset);

            System.Diagnostics.Debug.WriteLine($"[贝塞尔曲线创建] 起点: ({bezierStartPoint.X:F1}, {bezierStartPoint.Y:F1}), 终点: ({bezierEndPoint.X:F1}, {bezierEndPoint.Y:F1})");
            System.Diagnostics.Debug.WriteLine($"[贝塞尔曲线创建] Y差异: {yDiff:F1}px, X差异: {xDiff:F1}px, 控制点偏移: {controlPointOffset:F1}px");
            System.Diagnostics.Debug.WriteLine($"[贝塞尔曲线创建] PathPoints[0]: ({connection.PathPoints[0].X:F1}, {connection.PathPoints[0].Y:F1}), PathPoints[1]: ({connection.PathPoints[1].X:F1}, {connection.PathPoints[1].Y:F1})");

            // 添加贝塞尔曲线段，确保终点就是连接点位置
            var bezierSegment = new BezierSegment(controlPoint1, controlPoint2, bezierEndPoint, true);
            figure.Segments.Add(bezierSegment);
        }
        else
        {
            // 多点连接，使用直线段（保持兼容性）
            for (int i = 1; i < connection.PathPoints.Count; i++)
            {
                figure.Segments.Add(new LineSegment(connection.PathPoints[i], true));
            }
        }

        geometry.Figures.Add(figure);
        path.Data = geometry;

        // 添加鼠标事件
        path.MouseEnter += (s, e) => OnConnectionMouseEnter(connection, path);
        path.MouseLeave += (s, e) => OnConnectionMouseLeave(connection, path);
        path.MouseLeftButtonDown += (s, e) => OnConnectionMouseLeftButtonDown(connection, path, e);

        return path;
    }

    /// <summary>
    /// 判断是否为并行分支之间的连接（修改1：用于识别需要隐藏的白色斜线连接）
    /// </summary>
    private bool IsParallelBranchConnection(SFCConnectionViewModel connection)
    {
        // 检查源元素和目标元素是否都是并行分支
        var sourceElement = FindElementById(connection.SourceId);
        var targetElement = FindElementById(connection.TargetId);

        // 并行分支之间的连接线处理
        if (IsParallelBranch(sourceElement) && IsParallelBranch(targetElement))
        {
            // 计算连接距离
            if (connection.PathPoints.Count >= 2)
            {
                var startPoint = connection.PathPoints[0];
                var endPoint = connection.PathPoints[connection.PathPoints.Count - 1];
                var distance = Math.Sqrt(Math.Pow(endPoint.X - startPoint.X, 2) + Math.Pow(endPoint.Y - startPoint.Y, 2));

                System.Diagnostics.Debug.WriteLine($"[IsParallelBranchConnection] 并行分支间连接: {connection.SourceId} -> {connection.TargetId}, 距离: {distance:F1}px");

                // 修复：并行分支链之间的连接线不应该被隐藏
                // 只有当距离极短（小于20px）时才隐藏，这通常表示是重叠的连接点
                // 正常的并行分支链连接（147.5px间距）应该显示连接线
                return distance < 20; // 大幅降低阈值，只隐藏真正重叠的连接点
            }
        }

        return false;
    }

    /// <summary>
    /// 判断元素是否为并行分支
    /// </summary>
    private bool IsParallelBranch(object? element)
    {
        if (element is SFCBranchViewModel branchVM)
        {
            return branchVM.BranchType == SFCBranchType.Parallel;
        }
        return false;
    }

    /// <summary>
    /// 根据ID查找元素
    /// </summary>
    private object? FindElementById(string elementId)
    {
        // 在步骤中查找
        var step = Steps?.FirstOrDefault(s => s.Id == elementId);
        if (step != null) return step;

        // 在转换中查找
        var transition = Transitions?.FirstOrDefault(t => t.Id == elementId);
        if (transition != null) return transition;

        // 在分支中查找
        var branch = Branches?.FirstOrDefault(b => b.Id == elementId);
        if (branch != null) return branch;

        return null;
    }

    private void UpdateConnectionStyle(SFCConnectionViewModel connection, Path path)
    {
        if (connection.IsSelected)
        {
            path.Stroke = Brushes.Orange; // 选中时为橙色
            path.StrokeThickness = 3;
        }
        else
        {
            path.Stroke = Brushes.White; // 默认为纯白色
            path.StrokeThickness = 2;
        }
    }

    // 更新连接线位置
    private void UpdateConnectionLine(SFCConnectionViewModel connection)
    {
        // 首先检查连接线是否应该显示
        if (connection.PathPoints.Count >= 2)
        {
            var startPoint = connection.PathPoints[0];
            var endPoint = connection.PathPoints[connection.PathPoints.Count - 1];
            var distance = Math.Sqrt(Math.Pow(endPoint.X - startPoint.X, 2) + Math.Pow(endPoint.Y - startPoint.Y, 2));

            // 检查连接点是否重叠
            if (AreConnectPointsOverlapping(startPoint, endPoint))
            {
                // 连接点重叠，应该隐藏连接线
                if (_connectionElements.TryGetValue(connection.Id, out var existingElement))
                {
                    Children.Remove(existingElement);
                    _connectionElements.Remove(connection.Id);
                    System.Diagnostics.Debug.WriteLine($"[UpdateConnectionLine] 隐藏连接线 {connection.Id}，连接点重叠");
                    System.Diagnostics.Debug.WriteLine($"[UpdateConnectionLine] 起点: ({startPoint.X:F1}, {startPoint.Y:F1}), 终点: ({endPoint.X:F1}, {endPoint.Y:F1})");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[UpdateConnectionLine] 连接 {connection.Id} 连接点重叠，但连接线不存在");
                }
                return;
            }
            else
            {
                // 距离足够，应该显示连接线
                if (!_connectionElements.ContainsKey(connection.Id))
                {
                    // 连接线不存在，需要重新创建
                    System.Diagnostics.Debug.WriteLine($"[UpdateConnectionLine] 连接线 {connection.Id} 不存在，重新创建");
                    var connectionPath = CreateConnectionPath(connection);
                    if (connectionPath != null)
                    {
                        _connectionElements[connection.Id] = connectionPath;
                        Children.Add(connectionPath);

                        System.Diagnostics.Debug.WriteLine($"[UpdateConnectionLine] ✅ 连接线 {connection.Id} 已重新创建");
                    }
                    return;
                }
            }
        }

        // 更新现有连接线的几何
        if (_connectionElements.TryGetValue(connection.Id, out var element) && element is Path path)
        {
            // 重新创建路径几何
            if (connection.PathPoints.Count >= 2)
            {
                var geometry = new PathGeometry();
                var figure = new PathFigure
                {
                    StartPoint = connection.PathPoints[0],
                    IsClosed = false
                };

                if (connection.PathPoints.Count == 2)
                {
                    // 两点之间创建贝塞尔曲线，确保起点和终点精确落在连接点上
                    var startPoint = connection.PathPoints[0];
                    var endPoint = connection.PathPoints[1];

                    // 计算控制点偏移，使用更保守的算法确保连接线不悬空
                    double yDiff = Math.Abs(endPoint.Y - startPoint.Y);
                    double xDiff = Math.Abs(endPoint.X - startPoint.X);

                    // 控制点偏移基于Y轴距离，但不超过Y轴距离的一半，避免过度弯曲
                    double controlPointOffset = Math.Min(50, Math.Max(10, yDiff * 0.4));

                    // 如果Y轴距离很小，使用X轴距离的一部分作为偏移
                    if (yDiff < 15)
                    {
                        controlPointOffset = Math.Min(30, Math.Max(8, xDiff * 0.2));
                    }

                    var controlPoint1 = new Point(startPoint.X, startPoint.Y + controlPointOffset);
                    var controlPoint2 = new Point(endPoint.X, endPoint.Y - controlPointOffset);

                    // 添加贝塞尔曲线段，确保终点就是连接点位置
                    var bezierSegment = new BezierSegment(controlPoint1, controlPoint2, endPoint, true);
                    figure.Segments.Add(bezierSegment);
                }
                else
                {
                    // 多点连接，使用直线段（保持兼容性）
                    for (int i = 1; i < connection.PathPoints.Count; i++)
                    {
                        figure.Segments.Add(new LineSegment(connection.PathPoints[i], true));
                    }
                }

                geometry.Figures.Add(figure);
                path.Data = geometry;
            }
        }
    }

    private FrameworkElement? GetElementById(string id)
    {
        if (_stepElements.TryGetValue(id, out var stepElement))
            return stepElement;
        if (_transitionElements.TryGetValue(id, out var transitionElement))
            return transitionElement;
        if (_branchElements.TryGetValue(id, out var branchElement))
            return branchElement;
        if (_jumpElements.TryGetValue(id, out var jumpElement))
            return jumpElement;
        if (_graphNodeElements.TryGetValue(id, out var graphNodeElement))
            return graphNodeElement;
        return null;
    }

    private Point GetElementCenter(FrameworkElement element)
    {
        var left = GetLeft(element);
        var top = GetTop(element);
        return new Point(
            left + element.ActualWidth / 2,
            top + element.ActualHeight / 2);
    }

    #endregion

    #region DataContext处理

    /// <summary>
    /// 处理DataContext变化，设置ViewModel属性变化监听
    /// </summary>
    private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] DataContext变化: {e.OldValue?.GetType().Name} -> {e.NewValue?.GetType().Name}");

        // 移除旧的监听
        if (e.OldValue is EnhancedSFCViewModel oldViewModel)
        {
            UnsubscribeFromViewModelEvents(oldViewModel);
        }

        // 添加新的监听
        if (e.NewValue is EnhancedSFCViewModel newViewModel)
        {
            SubscribeToViewModelEvents(newViewModel);
        }
        else
        {
            System.Diagnostics.Debug.WriteLine("[SFCCanvas] DataContext不是EnhancedSFCViewModel类型");
        }
    }

    /// <summary>
    /// SFCCanvas加载完成后检查DataContext
    /// </summary>
    private void OnSFCCanvasLoaded(object sender, RoutedEventArgs e)
    {
        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 控件加载完成，当前DataContext: {DataContext?.GetType().Name}");

        // 如果DataContext已经是EnhancedSFCViewModel，手动触发订阅
        if (DataContext is EnhancedSFCViewModel viewModel)
        {
            SubscribeToViewModelEvents(viewModel);
        }
    }

    /// <summary>
    /// 订阅ViewModel事件
    /// </summary>
    private void SubscribeToViewModelEvents(EnhancedSFCViewModel viewModel)
    {
        // 监听步骤位置变化
        foreach (var step in viewModel.StepViewModels)
        {
            step.PropertyChanged += OnElementPositionChanged;
            System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已订阅步骤位置变化: {step.Id}");
        }

        // 监听转换条件位置变化
        foreach (var transition in viewModel.TransitionViewModels)
        {
            transition.PropertyChanged += OnElementPositionChanged;
            System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已订阅转换条件位置变化: {transition.Id}");
        }

        // 监听分支位置变化
        foreach (var branch in viewModel.BranchViewModels)
        {
            branch.PropertyChanged += OnElementPositionChanged;
            System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已订阅分支位置变化: {branch.Id}");
        }

        // 监听跳转元素位置变化
        foreach (var jump in viewModel.JumpViewModels)
        {
            jump.PropertyChanged += OnElementPositionChanged;
            System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已订阅跳转元素位置变化: {jump.Id}");
        }

        // 监听跳转指示器操作事件
        viewModel.JumpArrowIndicatorOperation += OnJumpArrowIndicatorOperation;
        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已订阅跳转指示器操作事件");

        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已订阅ViewModel位置变化事件 - 步骤:{viewModel.StepViewModels.Count}, 转换:{viewModel.TransitionViewModels.Count}, 分支:{viewModel.BranchViewModels.Count}, 跳转:{viewModel.JumpViewModels.Count}");
    }

    /// <summary>
    /// 取消订阅ViewModel事件
    /// </summary>
    private void UnsubscribeFromViewModelEvents(EnhancedSFCViewModel viewModel)
    {
        // 取消监听步骤位置变化
        foreach (var step in viewModel.StepViewModels)
        {
            step.PropertyChanged -= OnElementPositionChanged;
        }

        // 取消监听转换条件位置变化
        foreach (var transition in viewModel.TransitionViewModels)
        {
            transition.PropertyChanged -= OnElementPositionChanged;
        }

        // 取消监听分支位置变化
        foreach (var branch in viewModel.BranchViewModels)
        {
            branch.PropertyChanged -= OnElementPositionChanged;
        }

        // 取消监听跳转元素位置变化
        foreach (var jump in viewModel.JumpViewModels)
        {
            jump.PropertyChanged -= OnElementPositionChanged;
        }

        // 取消监听跳转指示器操作事件
        viewModel.JumpArrowIndicatorOperation -= OnJumpArrowIndicatorOperation;
        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已取消订阅跳转指示器操作事件");
    }

    // 防重复日志的静态变量
    private static DateTime _lastPositionChangeLog = DateTime.MinValue;
    private static string _lastPositionChangeElement = "";

    /// <summary>
    /// 处理元素位置变化
    /// </summary>
    private void OnElementPositionChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        // 简化重复的位置变化日志（每秒最多输出一次相同元素的位置变化）
        var now = DateTime.Now;
        var elementName = sender?.GetType().Name ?? "Unknown";
        var logKey = $"{elementName}_{e.PropertyName}";

        if (e.PropertyName == "Position" &&
            (now - _lastPositionChangeLog).TotalMilliseconds > 500 ||
            _lastPositionChangeElement != logKey)
        {
            System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 位置变化: {elementName}.{e.PropertyName}");
            _lastPositionChangeLog = now;
            _lastPositionChangeElement = logKey;
        }

        if (e.PropertyName == "Position" && sender != null)
        {
            // 获取元素ID
            string? elementId = null;
            if (sender is SFCStepViewModel step)
                elementId = step.Id;
            else if (sender is SFCTransitionViewModel transition)
                elementId = transition.Id;
            else if (sender is SFCBranchViewModel branch)
                elementId = branch.Id;
            else if (sender is SFCJumpViewModel jump)
                elementId = jump.Id;

            if (!string.IsNullOrEmpty(elementId))
            {
                // 启用调试日志
                System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 检测到元素位置变化: {elementId}");
                UpdateConnectionsForElement(elementId);

                // 如果是步骤位置变化，更新相关的跳转指示器
                if (sender is SFCStepViewModel movedStep)
                {
                    UpdateJumpArrowIndicatorsForTargetStep(movedStep.Id, movedStep.Position);
                }
                // 跳转元素位置变化时，连接线会自动更新，无需特殊处理
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 无法获取元素ID，sender类型: {sender.GetType().Name}");
            }
        }
    }

    /// <summary>
    /// 处理跳转指示器操作事件
    /// </summary>
    private void OnJumpArrowIndicatorOperation(object? sender, EnhancedSFCViewModel.JumpArrowIndicatorEventArgs e)
    {
        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 跳转指示器操作: {e.Operation} - JumpId: {e.JumpId}");

        switch (e.Operation)
        {
            case "Create":
                CreateJumpArrowIndicatorUI(e.JumpId, e.SourceStepNumber, e.TargetPosition);
                break;
            case "Update":
                UpdateJumpArrowIndicatorUI(e.JumpId, e.SourceStepNumber, e.TargetPosition);
                break;
            case "Remove":
                RemoveJumpArrowIndicatorUI(e.JumpId);
                break;
        }
    }

    /// <summary>
    /// 创建跳转箭头指示器UI
    /// </summary>
    private void CreateJumpArrowIndicatorUI(string jumpId, int transitionNumber, Point targetPosition)
    {
        // 如果指示器已存在，先移除
        if (_jumpArrowIndicators.ContainsKey(jumpId))
        {
            RemoveJumpArrowIndicatorUI(jumpId);
        }

        // 创建新的跳转箭头指示器
        var indicator = new SFCJumpArrowIndicator();
        indicator.SetJumpInfo(transitionNumber, targetPosition);

        // 设置Z-Index，确保指示器显示在其他元素之上，但不遮挡状态显示
        Canvas.SetZIndex(indicator, 500);

        // 添加到画布和字典
        _jumpArrowIndicators[jumpId] = indicator;
        Children.Add(indicator);

        System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已创建跳转指示器: {jumpId} -> T{transitionNumber} at {targetPosition}");
    }

    /// <summary>
    /// 更新跳转箭头指示器UI
    /// </summary>
    private void UpdateJumpArrowIndicatorUI(string jumpId, int transitionNumber, Point targetPosition)
    {
        if (_jumpArrowIndicators.TryGetValue(jumpId, out var indicator))
        {
            indicator.SetJumpInfo(transitionNumber, targetPosition);
            System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已更新跳转指示器: {jumpId} -> T{transitionNumber} at {targetPosition}");
        }
        else
        {
            // 如果指示器不存在，创建一个新的
            CreateJumpArrowIndicatorUI(jumpId, transitionNumber, targetPosition);
        }
    }

    /// <summary>
    /// 移除跳转箭头指示器UI
    /// </summary>
    private void RemoveJumpArrowIndicatorUI(string jumpId)
    {
        if (_jumpArrowIndicators.TryGetValue(jumpId, out var indicator))
        {
            Children.Remove(indicator);
            _jumpArrowIndicators.Remove(jumpId);
            System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已移除跳转指示器: {jumpId}");
        }
    }

    /// <summary>
    /// 更新指向特定目标步骤的所有跳转指示器
    /// </summary>
    private void UpdateJumpArrowIndicatorsForTargetStep(string targetStepId, Point newPosition)
    {
        if (DataContext is EnhancedSFCViewModel viewModel)
        {
            // 查找所有指向该步骤的跳转元素
            var jumpViewModels = viewModel.JumpViewModels.Where(j => j.TargetStepId == targetStepId && j.IsTargetValid);

            foreach (var jump in jumpViewModels)
            {
                if (_jumpArrowIndicators.TryGetValue(jump.Id, out var indicator))
                {
                    // 更新指示器位置
                    indicator.SetJumpInfo(jump.TargetStepNumber, newPosition);
                    System.Diagnostics.Debug.WriteLine($"[SFCCanvas] 已更新跳转指示器位置: {jump.Id} -> {newPosition}");
                }
            }
        }
    }

    #endregion

    #region 鼠标事件处理

    private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        Focus();

        // 添加日志
        Console.WriteLine("[DEBUG] SFCCanvas.OnMouseLeftButtonDown - 鼠标左键点击");

        bool isMultiSelect = Keyboard.IsKeyDown(Key.LeftCtrl) || Keyboard.IsKeyDown(Key.RightCtrl);

        var position = e.GetPosition(this);
        var hitElement = e.OriginalSource as FrameworkElement;
        
        // 添加日志
        Console.WriteLine($"[DEBUG] 点击位置: {position}, 点击元素: {hitElement?.GetType().Name ?? "null"}");
        
        // 连接点不再支持手动拖拽，只用于显示

        if (hitElement != null)
        {
            UpdateStatusDisplay($"点击了元素: {hitElement.GetType().Name}");

            // 检查是否点击了SFC元素
            var sfcElement = FindSFCElement(hitElement);
            if (sfcElement != null)
            {
                var elementData = sfcElement.DataContext;
                
                // 添加日志
                Console.WriteLine($"[DEBUG] 找到SFC元素: {sfcElement.GetType().Name}, 数据上下文: {elementData?.GetType().Name ?? "null"}");
                
                UpdateStatusDisplay($"找到SFC元素数据: {elementData?.GetType().Name}");

                // 处理选择
                if (!isMultiSelect)
                {
                    ClearSelection();
                }

                SelectElement(elementData);

                e.Handled = true;
                return;
            }
            else
            {
                // 添加日志
                Console.WriteLine("[DEBUG] 未找到SFC元素");
                
                UpdateStatusDisplay($"未找到SFC元素，点击的是: {hitElement.GetType().Name}");
            }
        }
        else
        {
            UpdateStatusDisplay("点击了空白区域");
        }

        // 开始框选
        if (!isMultiSelect)
        {
            ClearSelection();
        }

        StartSelection(position);
        e.Handled = true;

        // 在OnMouseLeftButtonDown方法中添加拖拽连接的处理
        StartConnectionDrag(hitElement, position);
    }
    
    // 查找连接点元素
    private SFCConnectPoint? FindConnectPointFromHitElement(FrameworkElement? hitElement)
    {
        if (hitElement == null) return null;
        
        // 直接检查是否为连接点
        if (hitElement is SFCConnectPoint connectPoint)
            return connectPoint;
            
        // 检查父级元素
        DependencyObject current = hitElement;
        while (current != null && !(current is SFCConnectPoint) && !(current is SFCCanvas))
        {
            current = VisualTreeHelper.GetParent(current);
            if (current is SFCConnectPoint foundConnectPoint)
                return foundConnectPoint;
        }
        
        return null;
    }

    private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
    {
        if (_isPanning)
        {
            StopPanning();
        }

        if (_isSelecting)
        {
            StopSelection();
        }

        if (_isConnecting)
        {
            // 处理连接完成
            var hitElement = e.OriginalSource as FrameworkElement;
            if (hitElement != null)
            {
                var targetElement = FindSFCElement(hitElement);
                if (targetElement != null && targetElement.DataContext != _connectionSource)
                {
                    CreateConnection(_connectionSource, targetElement.DataContext);
                }
            }

            _isConnecting = false;
            _connectionSource = null;
        }

        // 在OnMouseLeftButtonUp方法中添加拖拽连接的处理
        EndConnectionDrag(e.GetPosition(this));
    }

    private void OnMouseMove(object sender, MouseEventArgs e)
    {
        // 如果事件参数为空，退出处理
        if (e == null) return;
        
        Point currentPosition = e.GetPosition(this);

        // 连接点拖拽功能已移除

        // 处理平移
        if (_isPanning)
        {
            UpdatePanning(currentPosition);
            return;
        }

        // 处理框选
        if (_isSelecting)
        {
            UpdateSelection(currentPosition);
            return;
        }
    }

    private void OnMouseRightButtonDown(object sender, MouseButtonEventArgs e)
    {
        var position = e.GetPosition(this);
        ShowContextMenu(position);
    }



    private void OnPreviewMouseDown(object sender, MouseButtonEventArgs e)
    {
        // 处理中键按下
        if (e.MiddleButton == MouseButtonState.Pressed)
        {
            var position = e.GetPosition(this);
            StartPanning(position);
            e.Handled = true;
        }
        // 处理双击
        else if (e.LeftButton == MouseButtonState.Pressed && e.ClickCount == 2 && e.OriginalSource == this)
        {
            FitToContent();
            e.Handled = true;
        }
    }

    private void OnPreviewMouseUp(object sender, MouseButtonEventArgs e)
    {
        // 处理中键释放
        if (e.MiddleButton == MouseButtonState.Released && _isPanning)
        {
            StopPanning();
            e.Handled = true;
        }
    }

    private void OnMouseWheel(object sender, MouseWheelEventArgs e)
    {
        var position = e.GetPosition(this);
        var delta = e.Delta > 0 ? ZoomStep : -ZoomStep;

        // 按住Ctrl键进行缩放
        if (Keyboard.IsKeyDown(Key.LeftCtrl) || Keyboard.IsKeyDown(Key.RightCtrl))
        {
            ZoomAt(position, delta);
            e.Handled = true;
        }
        // 否则进行垂直滚动
        else
        {
            var scrollDelta = e.Delta * 0.5;
            _translateTransform.Y += scrollDelta;
            e.Handled = true;
        }
    }

    private void OnKeyDown(object sender, KeyEventArgs e)
    {
        switch (e.Key)
        {
            case Key.Delete:
                DeleteSelectedElements();
                e.Handled = true;
                break;
            case Key.Escape:
                // 连接点拖拽功能已移除
                
                // 取消普通选择
                ClearSelection();
                _isConnecting = false;
                _connectionSource = null;
                e.Handled = true;
                break;
            case Key.A when Keyboard.IsKeyDown(Key.LeftCtrl) || Keyboard.IsKeyDown(Key.RightCtrl):
                SelectAllElements();
                e.Handled = true;
                break;
            case Key.Space:
                Cursor = Cursors.Hand;
                UpdateStatusDisplay("平移模式准备 | 按住鼠标左键拖拽移动画布 | 或直接使用中键拖拽");
                e.Handled = true;
                break;
            case Key.Add:
            case Key.OemPlus:
                if (Keyboard.IsKeyDown(Key.LeftCtrl) || Keyboard.IsKeyDown(Key.RightCtrl))
                {
                    ZoomIn();
                    e.Handled = true;
                }
                break;
            case Key.Subtract:
            case Key.OemMinus:
                if (Keyboard.IsKeyDown(Key.LeftCtrl) || Keyboard.IsKeyDown(Key.RightCtrl))
                {
                    ZoomOut();
                    e.Handled = true;
                }
                break;
            case Key.D0:
            case Key.NumPad0:
                if (Keyboard.IsKeyDown(Key.LeftCtrl) || Keyboard.IsKeyDown(Key.RightCtrl))
                {
                    ResetZoom();
                    e.Handled = true;
                }
                break;
        }
    }

    private void OnKeyUp(object sender, KeyEventArgs e)
    {
        switch (e.Key)
        {
            case Key.Space:
                if (!_isPanning)
                {
                    Cursor = Cursors.Arrow;
                    UpdateStatusDisplay("就绪 | Ctrl+滚轮:缩放 | 中键/Space+拖拽:平移 | Ctrl+0:重置");
                }
                e.Handled = true;
                break;
        }
    }

    #endregion

    #region 辅助方法

    private FrameworkElement? FindSFCElement(FrameworkElement element)
    {
        // 添加调试日志
        Console.WriteLine($"[DEBUG] FindSFCElement - 开始查找元素: {element.GetType().Name}");

        var current = element;
        while (current != null)
        {
            Console.WriteLine($"[DEBUG] FindSFCElement - 检查元素: {current.GetType().Name}");

            // 检查是否为SFC元素 - 添加SFCJumpView支持
            if (current is SFCStepView || current is SFCTransitionView || current is SFCBranchView ||
                current is SFCGraphNodeView || current is SFCJumpView)
            {
                UpdateStatusDisplay($"找到SFC元素: {current.GetType().Name}");
                Console.WriteLine($"[DEBUG] FindSFCElement - 找到SFC元素: {current.GetType().Name}");
                return current;
            }

            // 检查数据上下文 - 添加SFCJumpViewModel支持
            if (current.DataContext is SFCStepViewModel || current.DataContext is SFCTransitionViewModel ||
                current.DataContext is SFCBranchViewModel || current.DataContext is SFCGraphNodeViewModel ||
                current.DataContext is SFCJumpViewModel)
            {
                UpdateStatusDisplay($"找到SFC元素(通过DataContext): {current.GetType().Name}");
                Console.WriteLine($"[DEBUG] FindSFCElement - 通过DataContext找到SFC元素: {current.GetType().Name}");
                return current;
            }

            current = VisualTreeHelper.GetParent(current) as FrameworkElement;
        }

        UpdateStatusDisplay($"未找到SFC元素，原始元素: {element.GetType().Name}");
        Console.WriteLine($"[DEBUG] FindSFCElement - 未找到SFC元素");
        return null;
    }

    private void UpdateElementPosition(object element, double deltaX, double deltaY)
    {
        if (element is SFCStepViewModel stepViewModel)
        {
            var oldPosition = stepViewModel.Position;
            var newPosition = new Point(oldPosition.X + deltaX, oldPosition.Y + deltaY);
            stepViewModel.Position = newPosition;

            // 更新所有与此步骤相关的连接线
            UpdateConnectionsForElement(stepViewModel.Id);
        }
        else if (element is SFCTransitionViewModel transitionViewModel)
        {
            var oldPosition = transitionViewModel.Position;
            var newPosition = new Point(oldPosition.X + deltaX, oldPosition.Y + deltaY);
            transitionViewModel.Position = newPosition;

            // 更新所有与此转换相关的连接线
            UpdateConnectionsForElement(transitionViewModel.Id);
        }
        else if (element is SFCBranchViewModel branchViewModel)
        {
            var oldPosition = branchViewModel.Position;
            var newPosition = new Point(oldPosition.X + deltaX, oldPosition.Y + deltaY);
            branchViewModel.Position = newPosition;

            // 更新所有与此分支相关的连接线
            UpdateConnectionsForElement(branchViewModel.Id);
        }
        else if (element is SFCJumpViewModel jumpViewModel)
        {
            var oldPosition = jumpViewModel.Position;
            var newPosition = new Point(oldPosition.X + deltaX, oldPosition.Y + deltaY);
            jumpViewModel.Position = newPosition;

            // 注释掉重复的连接线更新调用，因为OnElementPositionChanged已经会处理
            // UpdateConnectionsForElement(jumpViewModel.Id);
        }
    }



    // 更新与指定元素相关的所有连接线
    public void UpdateConnectionsForElement(string elementId)
    {
        System.Diagnostics.Debug.WriteLine($"[UpdateConnectionsForElement] 开始更新元素 {elementId} 的连接线");



        if (Connections == null)
        {
            System.Diagnostics.Debug.WriteLine($"[UpdateConnectionsForElement] Connections为空，无法更新");
            return;
        }

        System.Diagnostics.Debug.WriteLine($"[UpdateConnectionsForElement] 总连接线数量: {Connections.Count}");

        // 使用ToList()创建一个副本，避免在遍历过程中修改集合
        foreach (var connection in Connections.ToList())
        {
            if (connection.SourceId == elementId || connection.TargetId == elementId)
            {
                System.Diagnostics.Debug.WriteLine($"[UpdateConnectionsForElement] 找到相关连接: {connection.Id} ({connection.SourceId} -> {connection.TargetId})");

                // 获取源和目标元素
                var sourceElement = GetElementById(connection.SourceId);
                var targetElement = GetElementById(connection.TargetId);

                System.Diagnostics.Debug.WriteLine($"[UpdateConnectionsForElement] 元素查找结果: 源元素={sourceElement?.GetType().Name ?? "null"}, 目标元素={targetElement?.GetType().Name ?? "null"}");

                if (sourceElement != null && targetElement != null)
                {
                    try
                    {
                        // 查找源连接点和目标连接点
                        var sourceConnectPoint = FindConnectPointInElement(sourceElement, connection.SourceType, connection.SourceConnectPointIndex, true);
                        var targetConnectPoint = FindConnectPointInElement(targetElement, connection.TargetType, connection.TargetConnectPointIndex, false);
                        
                        Point sourcePosition;
                        Point targetPosition;
                        
                        // 优先使用稳定的计算方法，避免拖拽过程中的布局更新问题
                        var viewModel = DataContext as EnhancedSFCViewModel;
                        if (viewModel != null)
                        {
                            var sourceViewModel = GetViewModelFromUIElement(sourceElement);
                            if (sourceViewModel != null)
                            {
                                var sourceElementPosition = GetElementPosition(sourceViewModel);
                                sourcePosition = viewModel.CalculateElementConnectPoint(sourceViewModel, sourceElementPosition, true, connection.SourceConnectPointIndex);
                                System.Diagnostics.Debug.WriteLine($"[连接点调试] 源连接点计算位置: {sourcePosition}");
                            }
                            else if (sourceConnectPoint != null)
                            {
                                sourcePosition = sourceConnectPoint.GetCenterPosition(this);
                                System.Diagnostics.Debug.WriteLine($"[连接点调试] 源连接点实际位置(回退): {sourcePosition}");
                            }
                            else
                            {
                                sourcePosition = GetConnectPointPosition(sourceElement, connection.SourceType, connection.SourceConnectPointIndex, true);
                            }
                        }
                        else if (sourceConnectPoint != null)
                        {
                            sourcePosition = sourceConnectPoint.GetCenterPosition(this);
                            System.Diagnostics.Debug.WriteLine($"[连接点调试] 源连接点实际位置(无ViewModel): {sourcePosition}");
                        }
                        else
                        {
                            sourcePosition = GetConnectPointPosition(sourceElement, connection.SourceType, connection.SourceConnectPointIndex, true);
                        }

                        // 对目标连接点也使用稳定的计算方法
                        if (viewModel != null)
                        {
                            var targetViewModel = GetViewModelFromUIElement(targetElement);
                            if (targetViewModel != null)
                            {
                                var targetElementPosition = GetElementPosition(targetViewModel);
                                targetPosition = viewModel.CalculateElementConnectPoint(targetViewModel, targetElementPosition, false, connection.TargetConnectPointIndex);
                                System.Diagnostics.Debug.WriteLine($"[连接点调试] 目标连接点计算位置: {targetPosition}");
                            }
                            else if (targetConnectPoint != null)
                            {
                                targetPosition = targetConnectPoint.GetCenterPosition(this);
                                System.Diagnostics.Debug.WriteLine($"[连接点调试] 目标连接点实际位置(回退): {targetPosition}");
                            }
                            else
                            {
                                targetPosition = GetConnectPointPosition(targetElement, connection.TargetType, connection.TargetConnectPointIndex, false);
                            }
                        }
                        else if (targetConnectPoint != null)
                        {
                            targetPosition = targetConnectPoint.GetCenterPosition(this);
                            System.Diagnostics.Debug.WriteLine($"[连接点调试] 目标连接点实际位置(无ViewModel): {targetPosition}");
                        }
                        else
                        {
                            targetPosition = GetConnectPointPosition(targetElement, connection.TargetType, connection.TargetConnectPointIndex, false);
                        }
                        
                        // 更新连接线的路径点
                        if (connection.PathPoints.Count >= 2)
                        {
                            // 更新起点和终点，保持中间点不变
                            connection.PathPoints[0] = sourcePosition;
                            connection.PathPoints[connection.PathPoints.Count - 1] = targetPosition;
                            System.Diagnostics.Debug.WriteLine($"[UpdateConnectionsForElement] ✅ 路径点已更新: 起点={sourcePosition}, 终点={targetPosition}");
                        }
                        else
                        {
                            // 如果没有足够的路径点，清除并添加新的
                            connection.PathPoints.Clear();
                            connection.PathPoints.Add(sourcePosition);
                            connection.PathPoints.Add(targetPosition);
                            System.Diagnostics.Debug.WriteLine($"[UpdateConnectionsForElement] ✅ 路径点已重建: 起点={sourcePosition}, 终点={targetPosition}");
                        }
                        
                        // 重新创建连接线
                        UpdateConnectionLine(connection);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"更新连接线错误: {ex.Message}");
                    }
                }
            }
        }
    }

    private void UpdateSelectedElementsPosition(double deltaX, double deltaY)
    {
        foreach (var element in _selectedElements)
        {
            UpdateElementPosition(element, deltaX, deltaY);
        }
    }

    private void UpdateSelectedElementsPositionWithAlignment(double deltaX, double deltaY)
    {
        foreach (var element in _selectedElements)
        {
            UpdateElementPositionWithAlignment(element, deltaX, deltaY);
        }
    }

    private void UpdateElementPositionWithAlignment(object element, double deltaX, double deltaY)
    {
        switch (element)
        {
            case SFCStepViewModel stepViewModel:
                var newStepPosition = new Point(stepViewModel.Position.X + deltaX, stepViewModel.Position.Y + deltaY);
                stepViewModel.Position = newStepPosition;
                break;
            case SFCTransitionViewModel transitionViewModel:
                var newTransitionPosition = new Point(transitionViewModel.Position.X + deltaX, transitionViewModel.Position.Y + deltaY);
                transitionViewModel.Position = newTransitionPosition;
                break;
            case SFCBranchViewModel branchViewModel:
                var newBranchPosition = new Point(branchViewModel.Position.X + deltaX, branchViewModel.Position.Y + deltaY);
                branchViewModel.Position = newBranchPosition;
                break;
            case SFCGraphNodeViewModel graphNode:
                var newGraphPosition = new Point(graphNode.Position.X + deltaX, graphNode.Position.Y + deltaY);
                graphNode.Position = newGraphPosition;
                break;
            case SFCJumpViewModel jumpViewModel:
                var newJumpPosition = new Point(jumpViewModel.Position.X + deltaX, jumpViewModel.Position.Y + deltaY);
                jumpViewModel.Position = newJumpPosition;
                break;
        }
    }

    /// <summary>
    /// 检查两个连接点是否重叠
    /// </summary>
    /// <param name="point1">连接点1的中心位置</param>
    /// <param name="point2">连接点2的中心位置</param>
    /// <returns>如果连接点重叠返回true，否则返回false</returns>
    private bool AreConnectPointsOverlapping(Point point1, Point point2)
    {
        const double connectPointRadius = 5.0; // 连接点半径（10x10像素圆形的半径）

        // 计算两个连接点中心之间的距离
        double distance = Math.Sqrt(Math.Pow(point2.X - point1.X, 2) + Math.Pow(point2.Y - point1.Y, 2));

        // 如果距离小于两个半径之和，则连接点重叠
        bool isOverlapping = distance < (connectPointRadius * 2); // 10px阈值

        // 调试信息：只在重叠状态时输出，避免过多日志
        if (isOverlapping)
        {
            System.Diagnostics.Debug.WriteLine($"[连接点重叠检测] 点1: ({point1.X:F1}, {point1.Y:F1}), 点2: ({point2.X:F1}, {point2.Y:F1}), 距离: {distance:F1}px, 重叠: {isOverlapping}");
        }

        return isOverlapping;
    }

    /// <summary>
    /// 从UI元素获取对应的ViewModel
    /// </summary>
    private object? GetViewModelFromUIElement(FrameworkElement uiElement)
    {
        return uiElement.DataContext;
    }

    /// <summary>
    /// 获取元素的当前位置
    /// </summary>
    private Point GetElementPosition(object element)
    {
        Point position;
        switch (element)
        {
            case SFCStepViewModel stepViewModel:
                position = stepViewModel.Position;
                break;
            case SFCTransitionViewModel transitionViewModel:
                position = transitionViewModel.Position;
                break;
            case SFCBranchViewModel branchViewModel:
                position = branchViewModel.Position;
                System.Diagnostics.Debug.WriteLine($"[GetElementPosition] 分支ViewModel位置: {branchViewModel.Id} -> {position}");
                break;
            case SFCGraphNodeViewModel graphNode:
                position = graphNode.Position;
                break;
            case SFCJumpViewModel jumpViewModel:
                position = jumpViewModel.Position;
                break;
            case SFCBranchModel branchModel:
                position = branchModel.Position;
                System.Diagnostics.Debug.WriteLine($"[GetElementPosition] 分支Model位置: {branchModel.Id} -> {position}");
                break;
            case SFCStepModel stepModel:
                position = stepModel.Position;
                break;
            case SFCTransitionModel transitionModel:
                position = transitionModel.Position;
                break;
            default:
                position = new Point(0, 0);
                break;
        }
        return position;
    }

    /// <summary>
    /// 将所有选中元素更新到对齐位置（实时对齐）
    /// </summary>
    private void UpdateSelectedElementsToAlignedPosition(double deltaX, double deltaY)
    {
        // 应用偏移到所有选中元素，保持相对位置关系
        foreach (var element in _selectedElements)
        {
            if (_originalElementPositions.TryGetValue(element, out var originalPosition))
            {
                // 计算该元素相对于第一个元素的偏移
                var relativeX = originalPosition.X - _originalElementPosition.X;
                var relativeY = originalPosition.Y - _originalElementPosition.Y;

                // 应用对齐后的偏移，保持相对位置
                var newPosition = new Point(
                    _originalElementPosition.X + deltaX + relativeX,
                    _originalElementPosition.Y + deltaY + relativeY);

                SetElementPosition(element, newPosition);
            }
        }
    }

    /// <summary>
    /// 设置元素位置
    /// </summary>
    private void SetElementPosition(object element, Point position)
    {
        switch (element)
        {
            case SFCStepViewModel stepViewModel:
                stepViewModel.Position = position;
                break;
            case SFCTransitionViewModel transitionViewModel:
                transitionViewModel.Position = position;
                break;
            case SFCBranchViewModel branchViewModel:
                branchViewModel.Position = position;
                break;
            case SFCGraphNodeViewModel graphNode:
                graphNode.Position = position;
                break;
        }
    }

    /// <summary>
    /// 执行最终对齐（拖拽结束时确保元素对齐到正确位置）
    /// </summary>
    private void PerformFinalAlignment(Point currentMousePosition)
    {
        var firstElement = _selectedElements.FirstOrDefault();
        if (firstElement == null) return;

        // 简化最终对齐，直接使用鼠标偏移量
        var deltaX = currentMousePosition.X - _dragStartPoint.X;
        var deltaY = currentMousePosition.Y - _dragStartPoint.Y;

        // 直接应用偏移量，不进行对齐计算
        ApplyFinalAlignmentToAllElements(deltaX, deltaY);
    }

    /// <summary>
    /// 应用最终对齐位置到所有选中元素
    /// </summary>
    private void ApplyFinalAlignmentToAllElements(double deltaX, double deltaY)
    {
        foreach (var element in _selectedElements)
        {
            if (_originalElementPositions.TryGetValue(element, out var originalPosition))
            {
                // 计算该元素相对于第一个元素的偏移
                var relativeX = originalPosition.X - _originalElementPosition.X;
                var relativeY = originalPosition.Y - _originalElementPosition.Y;

                // 应用最终对齐位置，保持相对位置
                var finalPosition = new Point(
                    _originalElementPosition.X + deltaX + relativeX,
                    _originalElementPosition.Y + deltaY + relativeY);

                SetElementPosition(element, finalPosition);
            }
        }
    }

    /// <summary>
    /// 更新选中元素的视觉位置（拖拽过程中的临时位置，不修改实际数据）
    /// </summary>
    private void UpdateSelectedElementsVisualPosition(double deltaX, double deltaY)
    {
        foreach (var element in _selectedElements)
        {
            if (_originalElementPositions.TryGetValue(element, out var originalPosition))
            {
                // 计算该元素相对于第一个元素的偏移
                var relativeX = originalPosition.X - _originalElementPosition.X;
                var relativeY = originalPosition.Y - _originalElementPosition.Y;

                // 计算临时视觉位置
                var visualPosition = new Point(
                    _originalElementPosition.X + deltaX + relativeX,
                    _originalElementPosition.Y + deltaY + relativeY);

                SetElementPosition(element, visualPosition);
            }
        }
    }

    private void UpdateCursor(MouseEventArgs e)
    {
        if (_isPanning || _isDragging || _isSelecting)
            return;

        if (Keyboard.IsKeyDown(Key.Space))
        {
            Cursor = Cursors.Hand;
        }
        else
        {
            var hitElement = e.OriginalSource as FrameworkElement;
            if (hitElement != null && FindSFCElement(hitElement) != null)
            {
                Cursor = Cursors.SizeAll;
            }
            else
            {
                Cursor = Cursors.Arrow;
            }
        }
    }

    #endregion

    #region 缩放功能

    /// <summary>
    /// 在指定位置进行缩放 - 修复无限画布缩放
    /// </summary>
    private void ZoomAt(Point position, double delta)
    {
        var newZoom = Math.Max(MinZoom, Math.Min(MaxZoom, _zoomFactor + delta));
        if (Math.Abs(newZoom - _zoomFactor) < 0.001)
            return;

        var zoomRatio = newZoom / _zoomFactor;

        // 计算缩放中心点
        var centerX = position.X - _translateTransform.X;
        var centerY = position.Y - _translateTransform.Y;

        // 更新缩放
        _scaleTransform.ScaleX = newZoom;
        _scaleTransform.ScaleY = newZoom;
        _zoomFactor = newZoom;

        // 调整平移以保持缩放中心点不变
        _translateTransform.X = position.X - centerX * zoomRatio;
        _translateTransform.Y = position.Y - centerY * zoomRatio;

        // 更新网格背景以适应新的缩放和平移
        UpdateGridBackground();

        // 更新状态显示
        UpdateStatusDisplay($"缩放: {_zoomFactor:P0} | Ctrl+滚轮:缩放 | Ctrl+0:重置 | 无限画布");
    }

    /// <summary>
    /// 放大
    /// </summary>
    private void ZoomIn()
    {
        var center = new Point(ActualWidth / 2, ActualHeight / 2);
        ZoomAt(center, ZoomStep);
    }

    /// <summary>
    /// 缩小
    /// </summary>
    private void ZoomOut()
    {
        var center = new Point(ActualWidth / 2, ActualHeight / 2);
        ZoomAt(center, -ZoomStep);
    }

    /// <summary>
    /// 重置缩放 - 修复无限画布重置
    /// </summary>
    private void ResetZoom()
    {
        _scaleTransform.ScaleX = 1.0;
        _scaleTransform.ScaleY = 1.0;
        _translateTransform.X = 0.0;
        _translateTransform.Y = 0.0;
        _zoomFactor = 1.0;

        // 重置网格背景
        UpdateGridBackground();

        UpdateStatusDisplay("缩放已重置 | Ctrl+滚轮:缩放 | 中键/Space+拖拽:平移 | 无限画布");
    }

    /// <summary>
    /// 适合内容大小
    /// </summary>
    private void FitToContent()
    {
        var contentBounds = GetContentBounds();
        if (contentBounds.IsEmpty)
        {
            ResetZoom();
            return;
        }

        // 添加边距
        var margin = 50;
        var viewWidth = ActualWidth - margin * 2;
        var viewHeight = ActualHeight - margin * 2;

        if (viewWidth <= 0 || viewHeight <= 0) return;

        // 计算适合的缩放比例
        var scaleX = viewWidth / contentBounds.Width;
        var scaleY = viewHeight / contentBounds.Height;
        var scale = Math.Min(scaleX, scaleY);

        // 限制缩放范围
        scale = Math.Max(MinZoom, Math.Min(MaxZoom, scale));

        // 应用缩放
        _scaleTransform.ScaleX = scale;
        _scaleTransform.ScaleY = scale;
        _zoomFactor = scale;

        // 更新网格背景
        UpdateGridBackground();

        // 居中内容
        var centerX = contentBounds.X + contentBounds.Width / 2;
        var centerY = contentBounds.Y + contentBounds.Height / 2;

        _translateTransform.X = ActualWidth / 2 - centerX * scale;
        _translateTransform.Y = ActualHeight / 2 - centerY * scale;

        UpdateStatusDisplay($"适合内容 | 缩放: {_zoomFactor:P0} | 双击空白区域重新适合");
    }

    /// <summary>
    /// 获取内容边界
    /// </summary>
    private Rect GetContentBounds()
    {
        var bounds = Rect.Empty;

        // 计算所有元素的边界
        if (Steps != null)
        {
            foreach (var step in Steps)
            {
                var elementBounds = new Rect(step.Position.X, step.Position.Y, 100, 60);
                bounds.Union(elementBounds);
            }
        }

        if (Transitions != null)
        {
            foreach (var transition in Transitions)
            {
                var elementBounds = new Rect(transition.Position.X, transition.Position.Y, 80, 20);
                bounds.Union(elementBounds);
            }
        }

        if (Branches != null)
        {
            foreach (var branch in Branches)
            {
                var elementBounds = new Rect(branch.Position.X, branch.Position.Y, 60, 40);
                bounds.Union(elementBounds);
            }
        }

        return bounds;
    }

    #endregion

    #region 平移功能

    /// <summary>
    /// 开始平移
    /// </summary>
    private void StartPanning(Point position)
    {
        _isPanning = true;
        _panStartPoint = position;
        _panStartOffset = new Point(_translateTransform.X, _translateTransform.Y);
        CaptureMouse();
        Cursor = Cursors.Hand;
        UpdateStatusDisplay("平移模式 | 拖拽移动画布 | 释放鼠标结束");
    }

    /// <summary>
    /// 更新平移 - 修复无限画布平移
    /// </summary>
    private void UpdatePanning(Point currentPosition)
    {
        if (!_isPanning) return;

        var deltaX = currentPosition.X - _panStartPoint.X;
        var deltaY = currentPosition.Y - _panStartPoint.Y;

        _translateTransform.X = _panStartOffset.X + deltaX;
        _translateTransform.Y = _panStartOffset.Y + deltaY;

        // 更新网格背景以实现无限画布效果
        UpdateGridBackground();
    }

    /// <summary>
    /// 停止平移
    /// </summary>
    private void StopPanning()
    {
        _isPanning = false;
        ReleaseMouseCapture();
        Cursor = Cursors.Arrow;
        UpdateStatusDisplay("就绪 | Ctrl+滚轮:缩放 | 中键/Space+拖拽:平移 | Ctrl+0:重置");
    }

    /// <summary>
    /// 强制更新元素的视觉状态
    /// </summary>
    private void UpdateElementVisualState(FrameworkElement element, bool isSelected)
    {
        try
        {
            // 强制触发数据绑定更新
            element.UpdateLayout();
            element.InvalidateVisual();

            // 如果是UserControl，也更新其内容
            if (element is UserControl userControl)
            {
                userControl.UpdateLayout();
                userControl.InvalidateVisual();
            }
        }
        catch (Exception ex)
        {
            UpdateStatusDisplay($"更新视觉状态失败: {ex.Message}");
        }
    }

    #endregion

    #region 选择功能

    /// <summary>
    /// 清除所有选择 - 修复选中状态管理
    /// </summary>
    private void ClearSelection()
    {
        try
        {
            // 添加日志
            Console.WriteLine("[DEBUG] SFCCanvas.ClearSelection - 清除所有选中元素");
            
            _selectedElements.Clear();

            // 清除ViewModel中的选中元素
            var enhancedViewModel = DataContext as EnhancedSFCViewModel;
            if (enhancedViewModel != null)
            {
                Console.WriteLine("[DEBUG] 清除EnhancedSFCViewModel中的选中元素");
                enhancedViewModel.SelectedElement = null;
            }

            if (Steps != null)
            {
                foreach (var step in Steps)
                {
                    step.IsSelected = false;
                    // 强制更新视图
                    if (_stepElements.TryGetValue(step.Id, out var stepView))
                    {
                        UpdateElementVisualState(stepView, false);
                    }
                }
            }
            if (Transitions != null)
            {
                foreach (var transition in Transitions)
                {
                    transition.IsSelected = false;
                    // 强制更新视图
                    if (_transitionElements.TryGetValue(transition.Id, out var transitionView))
                    {
                        UpdateElementVisualState(transitionView, false);
                    }
                }
            }
            if (Branches != null)
            {
                foreach (var branch in Branches)
                {
                    branch.IsSelected = false;
                    // 强制更新视图
                    if (_branchElements.TryGetValue(branch.Id, out var branchView))
                    {
                        UpdateElementVisualState(branchView, false);
                    }
                }
            }
            if (GraphNodes != null)
            {
                foreach (var graphNode in GraphNodes)
                {
                    graphNode.IsSelected = false;
                    // 强制更新视图
                    if (_graphNodeElements.TryGetValue(graphNode.Id, out var graphNodeView))
                    {
                        UpdateElementVisualState(graphNodeView, false);
                    }
                }
            }
            if (Jumps != null)
            {
                foreach (var jump in Jumps)
                {
                    jump.IsSelected = false;
                    // 强制更新视图
                    if (_jumpElements.TryGetValue(jump.Id, out var jumpView))
                    {
                        UpdateElementVisualState(jumpView, false);
                    }
                }
            }

            UpdateStatusDisplay("已清除选择 | 点击元素选择 | Ctrl+A全选");
        }
        catch (Exception ex)
        {
            UpdateStatusDisplay($"清除选择时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 选择元素 - 增强选中效果
    /// </summary>
    private void SelectElement(object element)
    {
        try
        {
            // 添加日志，记录选中元素的状态
            Console.WriteLine($"[DEBUG] SFCCanvas.SelectElement - 选中元素: {element}");
            Console.WriteLine($"[DEBUG] 元素类型: {element.GetType().Name}");
            
            if (!_selectedElements.Contains(element))
            {
                _selectedElements.Add(element);
            }

            // 将选中元素传递给ViewModel
            var enhancedViewModel = DataContext as EnhancedSFCViewModel;
            if (enhancedViewModel != null)
            {
                Console.WriteLine("[DEBUG] 将选中元素传递给EnhancedSFCViewModel");
                enhancedViewModel.SelectedElement = element;
            }

            switch (element)
            {
                case SFCStepViewModel step:
                    step.IsSelected = true;
                    UpdateStatusDisplay($"已选中步骤: {step.Name} | 橙色高亮效果");
                    // 强制更新视图
                    if (_stepElements.TryGetValue(step.Id, out var stepView))
                    {
                        UpdateElementVisualState(stepView, true);
                    }
                    break;
                case SFCTransitionViewModel transition:
                    transition.IsSelected = true;
                    UpdateStatusDisplay($"已选中转换: {transition.Name} | 红色高亮效果");
                    // 强制更新视图
                    if (_transitionElements.TryGetValue(transition.Id, out var transitionView))
                    {
                        UpdateElementVisualState(transitionView, true);
                    }
                    break;
                case SFCBranchViewModel branch:
                    branch.IsSelected = true;
                    UpdateStatusDisplay($"已选中分支: {branch.Name} | 红色高亮效果");
                    // 强制更新视图
                    if (_branchElements.TryGetValue(branch.Id, out var branchView))
                    {
                        UpdateElementVisualState(branchView, true);
                    }
                    break;
                case SFCGraphNodeViewModel graphNode:
                    graphNode.IsSelected = true;
                    UpdateStatusDisplay($"已选中节点: {graphNode.Name} | 蓝色高亮效果");
                    // 强制更新视图
                    if (_graphNodeElements.TryGetValue(graphNode.Id, out var graphNodeView))
                    {
                        UpdateElementVisualState(graphNodeView, true);
                    }
                    break;
                case SFCJumpViewModel jump:
                    jump.IsSelected = true;
                    UpdateStatusDisplay($"已选中跳转: {jump.Name} | 绿色高亮效果");
                    // 强制更新视图
                    if (_jumpElements.TryGetValue(jump.Id, out var jumpView))
                    {
                        UpdateElementVisualState(jumpView, true);
                    }
                    break;
            }

            // 强制刷新UI
            InvalidateVisual();
        }
        catch (Exception ex)
        {
            UpdateStatusDisplay($"选择元素时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 公开的选择元素方法，供外部调用
    /// </summary>
    public void SelectElementPublic(object element)
    {
        SelectElement(element);
    }

    /// <summary>
    /// 公开的清除选择方法，供外部调用
    /// </summary>
    public void ClearSelectionPublic()
    {
        ClearSelection();
    }

    private void SelectAllElements()
    {
        ClearSelection();

        if (Steps != null)
        {
            foreach (var step in Steps)
            {
                SelectElement(step);
            }
        }
        if (Transitions != null)
        {
            foreach (var transition in Transitions)
            {
                SelectElement(transition);
            }
        }
        if (Branches != null)
        {
            foreach (var branch in Branches)
            {
                SelectElement(branch);
            }
        }
        if (GraphNodes != null)
        {
            foreach (var graphNode in GraphNodes)
            {
                SelectElement(graphNode);
            }
        }
        if (Jumps != null)
        {
            foreach (var jump in Jumps)
            {
                SelectElement(jump);
            }
        }
    }

    /// <summary>
    /// 开始框选
    /// </summary>
    private void StartSelection(Point position)
    {
        _isSelecting = true;
        _selectionStartPoint = position;

        // 创建选择矩形
        _selectionRectangle = new Rectangle
        {
            Stroke = new SolidColorBrush(Color.FromRgb(0, 120, 215)),
            StrokeThickness = 1,
            Fill = new SolidColorBrush(Color.FromArgb(50, 0, 120, 215)),
            StrokeDashArray = new DoubleCollection { 5, 3 }
        };

        Canvas.SetLeft(_selectionRectangle, position.X);
        Canvas.SetTop(_selectionRectangle, position.Y);
        Children.Add(_selectionRectangle);
        CaptureMouse();
    }

    /// <summary>
    /// 更新框选
    /// </summary>
    private void UpdateSelection(Point currentPosition)
    {
        if (!_isSelecting || _selectionRectangle == null) return;

        var left = Math.Min(_selectionStartPoint.X, currentPosition.X);
        var top = Math.Min(_selectionStartPoint.Y, currentPosition.Y);
        var width = Math.Abs(currentPosition.X - _selectionStartPoint.X);
        var height = Math.Abs(currentPosition.Y - _selectionStartPoint.Y);

        Canvas.SetLeft(_selectionRectangle, left);
        Canvas.SetTop(_selectionRectangle, top);
        _selectionRectangle.Width = width;
        _selectionRectangle.Height = height;
    }

    /// <summary>
    /// 停止框选
    /// </summary>
    private void StopSelection()
    {
        if (!_isSelecting || _selectionRectangle == null) return;

        // 获取选择区域
        var selectionRect = new Rect(
            Canvas.GetLeft(_selectionRectangle),
            Canvas.GetTop(_selectionRectangle),
            _selectionRectangle.Width,
            _selectionRectangle.Height);

        // 选择区域内的元素
        SelectElementsInRect(selectionRect);

        // 清理
        Children.Remove(_selectionRectangle);
        _selectionRectangle = null;
        _isSelecting = false;
        ReleaseMouseCapture();
    }

    /// <summary>
    /// 选择矩形区域内的元素
    /// </summary>
    private void SelectElementsInRect(Rect selectionRect)
    {
        // 检查步骤
        if (Steps != null)
        {
            foreach (var step in Steps)
            {
                if (IsElementInRect(step.Position, selectionRect))
                {
                    SelectElement(step);
                }
            }
        }

        // 检查转换
        if (Transitions != null)
        {
            foreach (var transition in Transitions)
            {
                if (IsElementInRect(transition.Position, selectionRect))
                {
                    SelectElement(transition);
                }
            }
        }

        // 检查分支
        if (Branches != null)
        {
            foreach (var branch in Branches)
            {
                if (IsElementInRect(branch.Position, selectionRect))
                {
                    SelectElement(branch);
                }
            }
        }

        // 检查Graph节点
        if (GraphNodes != null)
        {
            foreach (var graphNode in GraphNodes)
            {
                if (IsElementInRect(graphNode.Position, selectionRect))
                {
                    SelectElement(graphNode);
                }
            }
        }

        // 检查跳转元素
        if (Jumps != null)
        {
            foreach (var jump in Jumps)
            {
                if (IsElementInRect(jump.Position, selectionRect))
                {
                    SelectElement(jump);
                }
            }
        }
    }

    /// <summary>
    /// 检查元素是否在矩形区域内
    /// </summary>
    private bool IsElementInRect(Point elementPosition, Rect selectionRect)
    {
        return selectionRect.Contains(elementPosition);
    }

    #endregion

    #region 其他功能

    private void DeleteSelectedElements()
    {
        // 获取ViewModel
        var viewModel = DataContext as EnhancedSFCViewModel;
        if (viewModel == null)
        {
            System.Diagnostics.Debug.WriteLine("[DeleteSelectedElements] ViewModel为null");
            return;
        }

        System.Diagnostics.Debug.WriteLine($"[DeleteSelectedElements] SFCCanvas._selectedElements.Count: {_selectedElements.Count}");
        System.Diagnostics.Debug.WriteLine($"[DeleteSelectedElements] ViewModel.SelectedElement: {viewModel.SelectedElement?.GetType().Name}");

        // 如果SFCCanvas中有选中元素，记录详细信息
        if (_selectedElements.Count > 0)
        {
            System.Diagnostics.Debug.WriteLine($"[DeleteSelectedElements] 使用SFCCanvas原有删除逻辑，选中元素类型: {_selectedElements.First()?.GetType().Name}");
        }

        // 如果SFCCanvas中没有选中的元素，但ViewModel中有选中元素，则删除ViewModel中的选中元素
        if (_selectedElements.Count == 0)
        {
            if (viewModel.SelectedElement != null)
            {
                System.Diagnostics.Debug.WriteLine("[DeleteSelectedElements] SFCCanvas中没有选中元素，但ViewModel中有选中元素，删除ViewModel中的选中元素");

                try
                {
                    // 直接调用ViewModel的DeleteSelectedCommand
                    viewModel.DeleteSelectedCommand?.Execute(null);
                    UpdateStatusDisplay("已删除选中的元素");
                }
                catch (Exception ex)
                {
                    UpdateStatusDisplay($"删除选中元素时出错: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"[DeleteSelectedElements] 删除ViewModel选中元素时出错: {ex}");
                }
            }
            else
            {
                UpdateStatusDisplay("没有选中的元素可删除");
                System.Diagnostics.Debug.WriteLine("[DeleteSelectedElements] SFCCanvas和ViewModel中都没有选中的元素");
            }
            return;
        }
        
        try
        {
            // 复制一个选中元素的列表，以避免在迭代过程中修改集合
            var elementsToDelete = new List<object>(_selectedElements);
            
            System.Diagnostics.Debug.WriteLine($"[DeleteSelectedElements] 准备删除 {elementsToDelete.Count} 个元素");
            
            // 成功删除的元素计数
            int deletedCount = 0;
            
            // 逐个删除选中的元素
            foreach (var element in elementsToDelete)
            {
                try 
                {
                    DeleteElement(element);
                    deletedCount++;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"删除元素时出错: {ex.Message}");
                }
            }
            
            // 清除选中状态
            _selectedElements.Clear();
            
            // 强制刷新整个画布 - 删除操作后不需要刷新连接，避免重新创建已删除的连接
            InvalidateVisual();
            RefreshSteps();
            RefreshTransitions();
            RefreshBranches();
            RefreshJumps();
            // RefreshConnections(); // 注释掉，删除操作后不需要刷新连接
            
            // 更新状态消息
            if (deletedCount > 0)
            {
                UpdateStatusDisplay($"已删除 {deletedCount} 个元素");
            }
            else
            {
                UpdateStatusDisplay("没有删除任何元素");
            }
        }
        catch (Exception ex)
        {
            UpdateStatusDisplay($"删除选中元素时出错: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"[DeleteSelectedElements] 错误: {ex}");
        }
    }

    private void CreateConnection(object? source, object? target)
    {
        if (source == null || target == null || source == target)
            return;

        var viewModel = DataContext as EnhancedSFCViewModel;
        if (viewModel == null)
            return;

        // 获取元素类型
        string sourceType = GetElementType(source);
        string targetType = GetElementType(target);

        // 检查连接是否有效
        bool isValid = false;
        
        // 基本连接规则
        switch (sourceType)
        {
            case "步骤":
                // 步骤只能连接到转换或分支
                isValid = targetType == "转换" || 
                          (targetType == "分支" && IsBranchValidForStepConnection(target));
                break;

            case "转换":
                // 转换只能连接到步骤或分支
                isValid = targetType == "步骤" || 
                          (targetType == "分支" && IsBranchValidForTransitionConnection(target));
                break;

            case "分支":
                // 分支连接规则取决于分支类型和是否为汇聚分支
                if (source is SFCBranchViewModel branchViewModel)
                {
                    if (branchViewModel.BranchType == SFCBranchType.Parallel)
                    {
                        if (branchViewModel.IsConvergence)
                        {
                            // 并行汇聚分支只能连接到转换
                            isValid = targetType == "转换";
                        }
                        else
                        {
                            // 并行分支只能连接到步骤
                            isValid = targetType == "步骤";
                        }
                    }
                    else if (branchViewModel.BranchType == SFCBranchType.Selection)
                    {
                        if (branchViewModel.IsConvergence)
                        {
                            // 选择汇聚分支只能连接到转换
                            isValid = targetType == "转换";
                        }
                        else
                        {
                            // 选择分支只能连接到转换
                            isValid = targetType == "转换";
                        }
                    }
                }
                break;
        }
        
        if (!isValid)
        {
            MessageBox.Show($"无效的连接: {sourceType} -> {targetType}", "连接错误", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // 创建连接
        viewModel.ConnectElementsCommand?.Execute(target);
    }
    
    /// <summary>
    /// 检查分支是否适合步骤连接
    /// </summary>
    private bool IsBranchValidForStepConnection(object target)
    {
        if (target is not SFCBranchViewModel branch)
            return false;
            
        // 步骤可以连接到：
        // 1. 选择分支（非汇聚）
        // 2. 并行分支（非汇聚）
        // 3. 任何类型的汇聚分支
        return branch.IsConvergence || 
               (branch.BranchType == SFCBranchType.Selection || 
                branch.BranchType == SFCBranchType.Parallel);
    }
    
    /// <summary>
    /// 检查分支是否适合转换连接
    /// </summary>
    private bool IsBranchValidForTransitionConnection(object target)
    {
        if (target is not SFCBranchViewModel branch)
            return false;
            
        // 转换可以连接到：
        // 1. 选择汇聚分支
        // 2. 并行汇聚分支
        return branch.IsConvergence;
    }

    private void ShowContextMenu(Point position)
    {
        // 保存右键位置供后续使用
        _lastRightClickPosition = position;

        // 检查右键位置的上下文
        var contextInfo = AnalyzeContextAtPosition(position);

        // 创建右键菜单
        var contextMenu = new ContextMenu
        {
            Background = new SolidColorBrush(Color.FromRgb(45, 45, 48)),
            Foreground = new SolidColorBrush(Color.FromRgb(176, 176, 176)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(90, 90, 90)),
            BorderThickness = new Thickness(1),
            FontSize = 12
        };

        // 根据上下文添加不同的菜单项
        if (contextInfo.IsOnElement)
        {
            AddElementContextMenuItems(contextMenu, contextInfo);
        }
        else
        {
            AddEmptyAreaContextMenuItems(contextMenu, position);
        }

        // 设置菜单位置并显示
        contextMenu.PlacementTarget = this;
        contextMenu.Placement = PlacementMode.MousePoint;
        contextMenu.IsOpen = true;
    }

    /// <summary>
    /// 添加空白区域的右键菜单项
    /// </summary>
    private void AddEmptyAreaContextMenuItems(ContextMenu contextMenu, Point position)
    {
        // === 画布操作 ===
        var canvasHeader = new MenuItem
        {
            Header = "🎨 画布操作",
            IsEnabled = false,
            FontWeight = FontWeights.Bold
        };
        contextMenu.Items.Add(canvasHeader);

        // 全选
        var selectAllItem = CreateMenuItem("🔘 全选", "选择所有元素", () => SelectAllElements());
        contextMenu.Items.Add(selectAllItem);

        // 清除选择
        var clearSelectionItem = CreateMenuItem("🚫 清除选择", "清除当前选择", () => ClearSelection());
        contextMenu.Items.Add(clearSelectionItem);

        // 分隔符
        contextMenu.Items.Add(new Separator());

        // 画布属性
        var canvasPropertiesItem = CreateMenuItem("⚙️ 画布属性", "设置画布属性", () => ShowCanvasProperties());
        contextMenu.Items.Add(canvasPropertiesItem);
    }

    /// <summary>
    /// 在指定位置创建元素 - 支持西门子Graph风格
    /// </summary>
    private void CreateElementAtPosition(string elementType, Point position)
    {
        // 获取ViewModel - 尝试两种可能的ViewModel类型
        var enhancedViewModel = DataContext as EnhancedSFCViewModel;
        var sfcViewModel = DataContext as SFCEditorViewModel;

        Point optimalPosition;

        // 根据元素类型创建
        if (enhancedViewModel != null)
        {
            // 西门子Graph风格：检查是否有选中元素
            if (enhancedViewModel.SelectedElement == null && enhancedViewModel.Steps.Count > 0)
            {
                // 没有选中元素且不是第一个步骤，显示提示
                MessageBox.Show("请先选择一个现有元素，然后添加新元素。\n新元素将自动添加在选中元素下方并建立连接。",
                    "西门子Graph风格提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 智能位置计算：如果有选中元素，使用连接点对齐算法
            if (enhancedViewModel.SelectedElement != null)
            {
                // 获取目标元素尺寸
                var targetElementSize = GetElementSizeByType(elementType);
                
                // 使用新的连接点精确对齐算法
                optimalPosition = CalculateConnectPointAlignedInsertPosition(
                    enhancedViewModel.SelectedElement, 
                    elementType, 
                    targetElementSize);
                
                System.Diagnostics.Debug.WriteLine($"[CreateElementAtPosition] 使用连接点对齐算法，位置: {optimalPosition}");
            }
            else
            {
                // 没有选中元素，使用原有的智能位置计算
                optimalPosition = CalculateOptimalPosition(position);
                System.Diagnostics.Debug.WriteLine($"[CreateElementAtPosition] 使用原有算法，位置: {optimalPosition}");
            }

            // 使用EnhancedSFCViewModel的命令（传递位置参数）
            switch (elementType)
            {
                case "Step":
                    enhancedViewModel.AddStep(optimalPosition);
                    break;
                case "Transition":
                    enhancedViewModel.AddTransition(optimalPosition);
                    break;
                case "Branch":
                case "ParallelBranch":
                case "SelectionBranch":
                    enhancedViewModel.AddBranch(optimalPosition);
                    break;
            }
        }
        else if (sfcViewModel != null)
        {
            // 对于SFCEditorViewModel，使用原有的智能位置计算
            optimalPosition = CalculateOptimalPosition(position);
            
            // 使用SFCEditorViewModel的命令
            switch (elementType)
            {
                case "Step":
                    sfcViewModel.AddStepCommand?.Execute(optimalPosition);
                    break;
                case "Transition":
                    sfcViewModel.AddTransitionCommand?.Execute(optimalPosition);
                    break;
                case "Branch":
                case "ParallelBranch":
                case "SelectionBranch":
                    sfcViewModel.AddBranchCommand?.Execute(optimalPosition);
                    break;
            }
        }
        else
        {
            // 没有有效的ViewModel，使用默认位置
            optimalPosition = CalculateOptimalPosition(position);
        }

        // 显示创建反馈
        ShowCreationFeedback(elementType, optimalPosition);
    }

    /// <summary>
    /// 计算最佳创建位置（避免重叠）
    /// </summary>
    private Point CalculateOptimalPosition(Point preferredPosition)
    {
        const double minDistance = 0; // 最小间距（连接点完全重叠）
        const double searchRadius = 200; // 搜索半径
        const double gridSize = 20; // 网格大小

        // 获取所有现有元素的位置
        var existingPositions = GetAllElementPositions();

        // 检查首选位置是否可用
        if (IsPositionAvailable(preferredPosition, existingPositions, minDistance))
        {
            return SnapToGrid(preferredPosition, gridSize);
        }

        // 在首选位置周围搜索可用位置
        for (double radius = minDistance; radius <= searchRadius; radius += gridSize)
        {
            for (double angle = 0; angle < 360; angle += 45)
            {
                var radians = angle * Math.PI / 180;
                var testPosition = new Point(
                    preferredPosition.X + radius * Math.Cos(radians),
                    preferredPosition.Y + radius * Math.Sin(radians));

                if (IsPositionInCanvas(testPosition) &&
                    IsPositionAvailable(testPosition, existingPositions, minDistance))
                {
                    return SnapToGrid(testPosition, gridSize);
                }
            }
        }

        // 如果找不到合适位置，返回首选位置（用户可以手动调整）
        return SnapToGrid(preferredPosition, gridSize);
    }

    /// <summary>
    /// 获取所有现有元素的位置
    /// </summary>
    private List<Point> GetAllElementPositions()
    {
        var positions = new List<Point>();

        // 添加步骤位置
        if (Steps != null)
        {
            foreach (var step in Steps)
            {
                positions.Add(step.Position);
            }
        }

        // 添加转换位置
        if (Transitions != null)
        {
            foreach (var transition in Transitions)
            {
                positions.Add(transition.Position);
            }
        }

        // 添加分支位置
        if (Branches != null)
        {
            foreach (var branch in Branches)
            {
                positions.Add(branch.Position);
            }
        }

        // 添加Graph节点位置
        if (GraphNodes != null)
        {
            foreach (var graphNode in GraphNodes)
            {
                positions.Add(graphNode.Position);
            }
        }

        // 添加跳转元素位置
        if (Jumps != null)
        {
            foreach (var jump in Jumps)
            {
                positions.Add(jump.Position);
            }
        }

        return positions;
    }

    /// <summary>
    /// 检查位置是否可用（不与现有元素重叠）
    /// </summary>
    private bool IsPositionAvailable(Point position, List<Point> existingPositions, double minDistance)
    {
        foreach (var existingPos in existingPositions)
        {
            var distance = Math.Sqrt(Math.Pow(position.X - existingPos.X, 2) +
                                   Math.Pow(position.Y - existingPos.Y, 2));
            if (distance < minDistance)
            {
                return false;
            }
        }
        return true;
    }

    /// <summary>
    /// 检查位置是否在画布范围内
    /// </summary>
    private bool IsPositionInCanvas(Point position)
    {
        return position.X >= 0 && position.Y >= 0 &&
               position.X <= Width && position.Y <= Height;
    }

    /// <summary>
    /// 对齐到网格
    /// </summary>
    private Point SnapToGrid(Point position, double gridSize)
    {
        return new Point(
            Math.Round(position.X / gridSize) * gridSize,
            Math.Round(position.Y / gridSize) * gridSize);
    }

    /// <summary>
    /// 显示创建反馈
    /// </summary>
    private void ShowCreationFeedback(string elementName, Point position)
    {
        // 创建临时高亮圆圈
        var highlight = new Ellipse
        {
            Width = 60,
            Height = 60,
            Fill = new SolidColorBrush(Color.FromArgb(100, 0, 188, 242)), // 半透明蓝色
            Stroke = new SolidColorBrush(Color.FromRgb(0, 188, 242)),
            StrokeThickness = 2
        };

        // 设置位置
        Canvas.SetLeft(highlight, position.X - 30);
        Canvas.SetTop(highlight, position.Y - 30);

        // 添加到画布
        Children.Add(highlight);

        // 创建淡出动画
        var fadeOut = new DoubleAnimation
        {
            From = 1.0,
            To = 0.0,
            Duration = TimeSpan.FromMilliseconds(800),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
        };

        // 动画完成后移除元素
        fadeOut.Completed += (s, e) => Children.Remove(highlight);

        // 开始动画
        highlight.BeginAnimation(UIElement.OpacityProperty, fadeOut);
    }

    #endregion

    #region 右键菜单增强功能

    /// <summary>
    /// 上下文信息类
    /// </summary>
    private class ContextInfo
    {
        public bool IsOnElement { get; set; }
        public object? Element { get; set; }
        public string ElementType { get; set; } = string.Empty;
        public bool HasSelection { get; set; }
        public int SelectionCount { get; set; }
    }

    /// <summary>
    /// 分析指定位置的上下文信息
    /// </summary>
    private ContextInfo AnalyzeContextAtPosition(Point position)
    {
        var contextInfo = new ContextInfo();

        // 检查是否点击在元素上
        var hitElement = GetElementAtPosition(position);
        if (hitElement != null)
        {
            contextInfo.IsOnElement = true;
            contextInfo.Element = hitElement;
            contextInfo.ElementType = GetElementType(hitElement);
        }

        // 检查选择状态
        contextInfo.HasSelection = HasSelectedElements();
        contextInfo.SelectionCount = GetSelectedElementsCount();

        return contextInfo;
    }

    /// <summary>
    /// 获取指定位置的元素
    /// </summary>
    private object? GetElementAtPosition(Point position)
    {
        // 使用HitTest查找位置上的元素
        var hitResult = VisualTreeHelper.HitTest(this, position);
        if (hitResult?.VisualHit != null)
        {
            // 向上遍历可视化树查找SFC元素
            var element = hitResult.VisualHit;
            while (element != null && element != this)
            {
                // 检查是否为SFC元素的DataContext
                if (element.GetValue(DataContextProperty) is SFCStepViewModel ||
                    element.GetValue(DataContextProperty) is SFCTransitionViewModel ||
                    element.GetValue(DataContextProperty) is SFCBranchViewModel ||
                    element.GetValue(DataContextProperty) is SFCJumpViewModel)
                {
                    return element.GetValue(DataContextProperty);
                }

                // 检查是否为SFC元素控件本身
                if (element is SFCStepView || element is SFCTransitionView ||
                    element is SFCBranchView || element is SFCJumpView ||
                    element is SFCTerminatorView)
                {
                    return element.GetValue(DataContextProperty);
                }

                element = VisualTreeHelper.GetParent(element);
            }
        }
        return null;
    }

    /// <summary>
    /// 获取元素类型
    /// </summary>
    private string GetElementType(object element)
    {
        return element switch
        {
            SFCStepViewModel stepVM => IsTerminatorElement(stepVM) ? "顺控器终止" : "步骤",
            SFCTransitionViewModel => "转换",
            SFCBranchViewModel => "分支",
            SFCJumpViewModel => "跳转",
            _ => "Unknown"
        };
    }

    /// <summary>
    /// 检查是否为终止元素
    /// </summary>
    private bool IsTerminatorElement(SFCStepViewModel stepViewModel)
    {
        return stepViewModel.Description == "TERMINATOR_ELEMENT" || stepViewModel.Name == "顺控器终止";
    }

    /// <summary>
    /// 检查是否有选中的元素
    /// </summary>
    private bool HasSelectedElements()
    {
        // 这里需要根据实际的选择管理逻辑来实现
        return false; // 临时返回false
    }

    /// <summary>
    /// 获取选中元素的数量
    /// </summary>
    private int GetSelectedElementsCount()
    {
        // 这里需要根据实际的选择管理逻辑来实现
        return 0; // 临时返回0
    }

    /// <summary>
    /// 创建菜单项的辅助方法
    /// </summary>
    private MenuItem CreateMenuItem(string header, string tooltip, Action action)
    {
        var menuItem = new MenuItem
        {
            Header = header,
            ToolTip = tooltip,
            Foreground = new SolidColorBrush(Color.FromRgb(176, 176, 176))
        };
        menuItem.Click += (s, e) => action();
        return menuItem;
    }

    /// <summary>
    /// 添加元素上下文菜单项
    /// </summary>
    private void AddElementContextMenuItems(ContextMenu contextMenu, ContextInfo contextInfo)
    {
        // 元素信息
        var elementHeader = new MenuItem
        {
            Header = $"📍 {contextInfo.ElementType}",
            IsEnabled = false,
            FontWeight = FontWeights.Bold
        };
        contextMenu.Items.Add(elementHeader);

        // 编辑属性
        var editPropertiesItem = CreateMenuItem("⚙️ 属性", "编辑元素属性", () => EditElementProperties(contextInfo.Element));
        contextMenu.Items.Add(editPropertiesItem);

        // 复制
        var copyItem = CreateMenuItem("📋 复制", "复制元素", () => CopyElement(contextInfo.Element));
        contextMenu.Items.Add(copyItem);

        // 删除
        var deleteItem = CreateMenuItem("🗑️ 删除", "删除元素", () => DeleteElement(contextInfo.Element));
        contextMenu.Items.Add(deleteItem);
    }

    /// <summary>
    /// 创建特殊元素 - 支持西门子Graph风格
    /// </summary>
    private void CreateSpecialElementAtPosition(string elementType, Point position)
    {
        // 获取ViewModel
        var enhancedViewModel = DataContext as EnhancedSFCViewModel;
        var sfcViewModel = DataContext as SFCEditorViewModel;

        // 使用智能位置计算
        var optimalPosition = CalculateOptimalPosition(position);

        if (enhancedViewModel != null)
        {
            // 西门子Graph风格：检查是否有选中元素
            if (enhancedViewModel.SelectedElement == null)
            {
                // 没有选中元素，显示提示
                MessageBox.Show("请先选择一个现有元素，然后添加新元素。\n新元素将自动添加在选中元素下方并建立连接。",
                    "西门子Graph风格提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 使用EnhancedSFCViewModel创建特殊元素
            switch (elementType)
            {
                case "WaitCondition":
                    CreateWaitConditionElement(enhancedViewModel, optimalPosition);
                    break;
                case "Delay":
                    CreateDelayElement(enhancedViewModel, optimalPosition);
                    break;
                case "EndFlow":
                    CreateEndFlowElement(enhancedViewModel, optimalPosition);
                    break;
                case "SubFlow":
                    CreateSubFlowElement(enhancedViewModel, optimalPosition);
                    break;
            }
        }

        // 显示创建反馈
        ShowCreationFeedback(elementType, optimalPosition);
    }

    /// <summary>
    /// 创建等待条件元素（简化为普通步骤）
    /// </summary>
    private void CreateWaitConditionElement(EnhancedSFCViewModel viewModel, Point position)
    {
        viewModel.AddStep(position);
    }

    /// <summary>
    /// 创建延时元素（简化为普通步骤）
    /// </summary>
    private void CreateDelayElement(EnhancedSFCViewModel viewModel, Point position)
    {
        viewModel.AddStep(position);
    }

    /// <summary>
    /// 创建流程结束元素（简化为普通步骤）
    /// </summary>
    private void CreateEndFlowElement(EnhancedSFCViewModel viewModel, Point position)
    {
        viewModel.AddStep(position);
    }

    /// <summary>
    /// 创建子流程元素（简化为普通步骤）
    /// </summary>
    private void CreateSubFlowElement(EnhancedSFCViewModel viewModel, Point position)
    {
        viewModel.AddStep(position);
    }

    /// <summary>
    /// 检查剪贴板是否有内容
    /// </summary>
    private bool HasClipboardContent()
    {
        return false; // 临时返回false
    }

    /// <summary>
    /// 在指定位置粘贴
    /// </summary>
    private void PasteAtPosition(Point position)
    {
        // 实现粘贴逻辑
    }



    /// <summary>
    /// 显示画布属性
    /// </summary>
    private void ShowCanvasProperties()
    {
        // 实现画布属性对话框
    }

    /// <summary>
    /// 编辑元素属性
    /// </summary>
    private void EditElementProperties(object? element)
    {
        if (element == null) return;
        
        if (element is SFCStepViewModel stepViewModel)
        {
            // 查找关联的EnhancedSFCViewModel
            var parentViewModel = DataContext as EnhancedSFCViewModel;
            parentViewModel?.EditStep(parentViewModel.Steps.FirstOrDefault(s => s.Id == stepViewModel.Id));
        }
        else if (element is SFCTransitionViewModel transitionViewModel)
        {
            var parentViewModel = DataContext as EnhancedSFCViewModel;
            parentViewModel?.EditTransition(parentViewModel.Transitions.FirstOrDefault(t => t.Id == transitionViewModel.Id));
        }
        else if (element is SFCBranchViewModel branchViewModel)
        {
            var parentViewModel = DataContext as EnhancedSFCViewModel;
            parentViewModel?.EditBranch(branchViewModel);
        }
        else if (element is SFCGraphNodeViewModel graphNodeViewModel)
        {
            // 实现图形节点的编辑
        }
    }

    /// <summary>
    /// 复制元素
    /// </summary>
    private void CopyElement(object? element)
    {
        // 实现复制逻辑
    }

    /// <summary>
    /// 删除元素
    /// </summary>
    private void DeleteElement(object? element)
    {
        if (element == null) return;
        
        // 获取ViewModel
        var viewModel = DataContext as EnhancedSFCViewModel;
        if (viewModel == null) return;
        
        try
        {
            // 直接将元素传递给ViewModel的DeleteElementCommand
            // 现在ViewModel已经能够处理视图模型对象
            viewModel.DeleteElementCommand.Execute(element);
            
            // 更新状态消息
            string elementType = GetElementType(element);
            string elementName = GetElementName(element);
            UpdateStatusDisplay($"已删除{elementType}: {elementName}");
            
            // 刷新画布 - 删除操作后不需要刷新连接，避免重新创建已删除的连接
            RefreshSteps();
            RefreshTransitions();
            RefreshBranches();
            RefreshJumps();
            // RefreshConnections(); // 注释掉，删除操作后不需要刷新连接
            
            // 强制刷新整个画布
            InvalidateVisual();
            
            // 记录调试信息
            System.Diagnostics.Debug.WriteLine($"[DeleteElement] 已删除元素: {elementType} {elementName}");
        }
        catch (Exception ex)
        {
            UpdateStatusDisplay($"删除元素时出错: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"[DeleteElement] 错误: {ex}");
        }
    }


    
    /// <summary>
    /// 获取元素名称
    /// </summary>
    private string GetElementName(object element)
    {
        if (element is SFCStepViewModel step)
            return step.Name;
        else if (element is SFCTransitionViewModel transition)
            return transition.Name;
        else if (element is SFCBranchViewModel branch)
            return branch.Name;
        else if (element is SFCGraphNodeViewModel graphNode)
            return graphNode.Name;
        
        return "未知元素";
    }

    #endregion

    #region 拖拽放置功能

    private void OnDragEnter(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent("ToolboxNode"))
        {
            e.Effects = DragDropEffects.Copy;
            
            // 存储当前拖拽的数据，用于连接点对齐计算
            _currentDragData = e.Data.GetData("ToolboxNode");
            
            ShowDragFeedback(true);
            CreateDropZoneIndicator();

            // 改变鼠标光标
            Mouse.OverrideCursor = Cursors.Hand;
        }
        else
        {
            e.Effects = DragDropEffects.None;
            Mouse.OverrideCursor = Cursors.No;
        }
        e.Handled = true;
    }

    private void OnDragOver(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent("ToolboxNode"))
        {
            e.Effects = DragDropEffects.Copy;

            // 更新拖拽预览位置
            var position = e.GetPosition(this);

            // 获取节点定义以确定元素大小
            var nodeDefinition = e.Data.GetData("ToolboxNode") as NodeDefinition;
            var elementSize = GetElementSizeFromNodeDefinition(nodeDefinition);

            // 计算智能对齐位置（使用工具栏拖拽专用方法）
            var alignedPosition = CalculateAlignedPositionForToolbox(position, elementSize);

            // 更新所有视觉反馈
            UpdateDragPreview(alignedPosition);
            UpdatePositionPreview(alignedPosition);
            UpdateAlignmentAssistForToolbox(position, alignedPosition, elementSize);
            UpdatePositionTooltip(alignedPosition);
        }
        else
        {
            e.Effects = DragDropEffects.None;
        }
        e.Handled = true;
    }

    private void OnDragLeave(object sender, DragEventArgs e)
    {
        ShowDragFeedback(false);
        HideDragPreview();
        ClearAllDragVisuals();

        // 清理拖拽数据
        _currentDragData = null;

        // 恢复鼠标光标
        Mouse.OverrideCursor = null;

        e.Handled = true;
    }

    private void OnDrop(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent("ToolboxNode"))
        {
            var nodeDefinition = e.Data.GetData("ToolboxNode") as NodeDefinition;
            if (nodeDefinition != null)
            {
                var dropPosition = e.GetPosition(this);

                // 使用对齐后的位置
                var alignedPosition = CalculateOptimalPosition(dropPosition);
                CreateElementFromDrop(nodeDefinition, alignedPosition);

                // 显示成功反馈
                ShowDropSuccessFeedback(alignedPosition, nodeDefinition.Name);

                // 播放放置动画
                PlayDropAnimation(alignedPosition);
            }
        }

        ShowDragFeedback(false);
        HideDragPreview();
        ClearAllDragVisuals();

        // 恢复鼠标光标
        Mouse.OverrideCursor = null;

        e.Handled = true;
    }

    /// <summary>
    /// 显示拖拽反馈
    /// </summary>
    private void ShowDragFeedback(bool show)
    {
        if (show)
        {
            // 改变画布背景颜色表示可以放置
            Background = new RadialGradientBrush
            {
                Center = new Point(0.5, 0.5),
                RadiusX = 0.8,
                RadiusY = 0.8,
                GradientStops = new GradientStopCollection
                {
                    new GradientStop(Color.FromArgb(30, 0, 120, 215), 0),
                    new GradientStop(Color.FromArgb(10, 0, 120, 215), 0.7),
                    new GradientStop(Color.FromRgb(45, 45, 48), 1)
                }
            };
        }
            else
    {
        // 恢复为蓝色渐变背景
        Background = new RadialGradientBrush
        {
            Center = new Point(0.5, 0.5),
            RadiusX = 0.8,
            RadiusY = 0.8,
            GradientStops = new GradientStopCollection
            {
                new GradientStop(Color.FromArgb(30, 0, 120, 215), 0),
                new GradientStop(Color.FromArgb(10, 0, 120, 215), 0.7),
                new GradientStop(Color.FromRgb(45, 45, 48), 1)
            }
        };
    }
    }

    /// <summary>
    /// 创建放置区域指示器
    /// </summary>
    private void CreateDropZoneIndicator()
    {
        if (_dropZoneIndicator == null)
        {
            _dropZoneIndicator = new Rectangle
            {
                Fill = new SolidColorBrush(Color.FromArgb(30, 0, 120, 215)),
                Stroke = new SolidColorBrush(Color.FromArgb(150, 0, 120, 215)),
                StrokeDashArray = new DoubleCollection { 5, 5 },
                StrokeThickness = 2,
                Width = ActualWidth,
                Height = ActualHeight,
                IsHitTestVisible = false
            };

            Canvas.SetLeft(_dropZoneIndicator, 0);
            Canvas.SetTop(_dropZoneIndicator, 0);
            Children.Add(_dropZoneIndicator);
        }
    }

    /// <summary>
    /// 更新位置预览
    /// </summary>
    private void UpdatePositionPreview(Point position)
    {
        var optimalPosition = CalculateOptimalPosition(position);

        if (_positionPreview == null)
        {
            _positionPreview = new Ellipse
            {
                Width = 20,
                Height = 20,
                Fill = new RadialGradientBrush
                {
                    GradientStops = new GradientStopCollection
                    {
                        new GradientStop(Color.FromArgb(200, 0, 255, 0), 0),
                        new GradientStop(Color.FromArgb(100, 0, 200, 0), 0.7),
                        new GradientStop(Color.FromArgb(50, 0, 150, 0), 1)
                    }
                },
                Stroke = new SolidColorBrush(Color.FromArgb(255, 0, 200, 0)),
                StrokeThickness = 2,
                IsHitTestVisible = false
            };
            Children.Add(_positionPreview);
        }

        Canvas.SetLeft(_positionPreview, optimalPosition.X - 10);
        Canvas.SetTop(_positionPreview, optimalPosition.Y - 10);

        // 添加脉冲动画
        var pulseAnimation = new DoubleAnimation
        {
            From = 0.5,
            To = 1.0,
            Duration = TimeSpan.FromMilliseconds(800),
            AutoReverse = true,
            RepeatBehavior = RepeatBehavior.Forever
        };
        _positionPreview.BeginAnimation(UIElement.OpacityProperty, pulseAnimation);
    }

    /// <summary>
    /// 更新网格对齐线
    /// </summary>
    private void UpdateGridAlignment(Point position)
    {
        var optimalPosition = CalculateOptimalPosition(position);

        // 创建垂直对齐线
        if (_gridLineX == null)
        {
            _gridLineX = new Line
            {
                Stroke = new SolidColorBrush(Color.FromArgb(120, 255, 165, 0)),
                StrokeThickness = 1,
                StrokeDashArray = new DoubleCollection { 3, 3 },
                IsHitTestVisible = false
            };
            Children.Add(_gridLineX);
        }

        // 创建水平对齐线
        if (_gridLineY == null)
        {
            _gridLineY = new Line
            {
                Stroke = new SolidColorBrush(Color.FromArgb(120, 255, 165, 0)),
                StrokeThickness = 1,
                StrokeDashArray = new DoubleCollection { 3, 3 },
                IsHitTestVisible = false
            };
            Children.Add(_gridLineY);
        }

        // 更新对齐线位置
        _gridLineX.X1 = optimalPosition.X;
        _gridLineX.Y1 = 0;
        _gridLineX.X2 = optimalPosition.X;
        _gridLineX.Y2 = ActualHeight;

        _gridLineY.X1 = 0;
        _gridLineY.Y1 = optimalPosition.Y;
        _gridLineY.X2 = ActualWidth;
        _gridLineY.Y2 = optimalPosition.Y;
    }

    /// <summary>
    /// 更新位置提示
    /// </summary>
    private void UpdatePositionTooltip(Point position)
    {
        if (_positionTooltip == null)
        {
            _positionTooltip = new TextBlock
            {
                Background = new SolidColorBrush(Color.FromArgb(220, 45, 45, 48)),
                Foreground = new SolidColorBrush(Color.FromRgb(255, 255, 255)),
                Padding = new Thickness(8, 4, 8, 4),
                FontSize = 11,
                FontFamily = new FontFamily("Consolas"),
                IsHitTestVisible = false,
                Effect = new DropShadowEffect
                {
                    Color = Colors.Black,
                    Direction = 315,
                    ShadowDepth = 2,
                    BlurRadius = 4,
                    Opacity = 0.5
                }
            };
            Children.Add(_positionTooltip);
        }

        // 构建提示文本
        var tooltipText = $"位置: ({position.X:F0}, {position.Y:F0})";

        if (_isSnapped)
        {
            tooltipText += "\n🧲 已吸附";

            // 检查对齐类型
            var gridPosition = SnapToGrid(position);
            if (CalculateDistance(position, gridPosition) < 1.0)
            {
                tooltipText += " - 网格";
            }
            else
            {
                tooltipText += " - 元素";
            }
        }

        _positionTooltip.Text = tooltipText;
        Canvas.SetLeft(_positionTooltip, position.X + 20);
        Canvas.SetTop(_positionTooltip, position.Y - 35);
    }

    /// <summary>
    /// 更新对齐辅助
    /// </summary>
    private void UpdateAlignmentAssist(Point originalPosition, Point alignedPosition)
    {
        // 清除之前的对齐视觉元素
        ClearAlignmentVisuals();

        // 如果位置发生了吸附，显示对齐辅助
        if (_isSnapped)
        {
            ShowAlignmentLines(originalPosition, alignedPosition);
            ShowAlignmentHighlights(alignedPosition);
        }

        // 更新网格对齐线
        UpdateGridAlignment(alignedPosition);
    }

    /// <summary>
    /// 显示对齐辅助线
    /// </summary>
    private void ShowAlignmentLines(Point originalPosition, Point alignedPosition)
    {
        // 获取现有元素（排除被拖拽的元素）
        var excludeElements = new HashSet<object>(_selectedElements);
        var existingElements = GetExistingElements(excludeElements);

        // 获取被拖拽元素的大小
        var draggedElementSize = GetDraggedElementSize();

        foreach (var element in existingElements)
        {
            // 检查所有可能的对齐并显示对齐线
            ShowAlignmentLinesForElement(originalPosition, element, draggedElementSize);
        }

        // 如果是网格对齐，显示网格线
        var gridPosition = SnapToGrid(originalPosition);
        if (CalculateDistance(alignedPosition, gridPosition) < 1.0)
        {
            CreateGridAlignmentLines(alignedPosition);
        }
    }

    /// <summary>
    /// 为特定元素显示对齐线
    /// </summary>
    private void ShowAlignmentLinesForElement(Point position, ElementInfo element, Size draggedSize)
    {
        // 计算被拖拽元素的边界
        var draggedLeft = position.X;
        var draggedRight = position.X + draggedSize.Width;
        var draggedTop = position.Y;
        var draggedBottom = position.Y + draggedSize.Height;
        var draggedCenterX = position.X + draggedSize.Width / 2;
        var draggedCenterY = position.Y + draggedSize.Height / 2;

        // 计算目标元素的边界
        var elementLeft = element.Position.X;
        var elementRight = element.Position.X + element.Size.Width;
        var elementTop = element.Position.Y;
        var elementBottom = element.Position.Y + element.Size.Height;
        var elementCenterX = element.Position.X + element.Size.Width / 2;
        var elementCenterY = element.Position.Y + element.Size.Height / 2;

        var tolerance = ELEMENT_SNAP_DISTANCE;

        // 垂直对齐线
        // 左边缘对齐
        if (Math.Abs(draggedLeft - elementLeft) <= tolerance)
        {
            CreateVerticalLine(elementLeft, Color.FromArgb(180, 0, 255, 255), 2); // 青色
        }

        // 右边缘对齐
        if (Math.Abs(draggedRight - elementRight) <= tolerance)
        {
            CreateVerticalLine(elementRight, Color.FromArgb(180, 0, 255, 255), 2); // 青色
        }

        // 中心对齐
        if (Math.Abs(draggedCenterX - elementCenterX) <= tolerance)
        {
            CreateVerticalLine(elementCenterX, Color.FromArgb(180, 255, 0, 255), 2); // 洋红色
        }

        // 相邻对齐（左边贴右边）
        if (Math.Abs(draggedLeft - elementRight) <= tolerance)
        {
            CreateVerticalLine(elementRight, Color.FromArgb(150, 255, 165, 0), 1.5); // 橙色
        }

        // 相邻对齐（右边贴左边）
        if (Math.Abs(draggedRight - elementLeft) <= tolerance)
        {
            CreateVerticalLine(elementLeft, Color.FromArgb(150, 255, 165, 0), 1.5); // 橙色
        }

        // 水平对齐线
        // 顶部对齐
        if (Math.Abs(draggedTop - elementTop) <= tolerance)
        {
            CreateHorizontalLine(elementTop, Color.FromArgb(180, 0, 255, 255), 2); // 青色
        }

        // 底部对齐
        if (Math.Abs(draggedBottom - elementBottom) <= tolerance)
        {
            CreateHorizontalLine(elementBottom, Color.FromArgb(180, 0, 255, 255), 2); // 青色
        }

        // 中心对齐
        if (Math.Abs(draggedCenterY - elementCenterY) <= tolerance)
        {
            CreateHorizontalLine(elementCenterY, Color.FromArgb(180, 255, 0, 255), 2); // 洋红色
        }

        // 相邻对齐（顶部贴底部）
        if (Math.Abs(draggedTop - elementBottom) <= tolerance)
        {
            CreateHorizontalLine(elementBottom, Color.FromArgb(150, 255, 165, 0), 1.5); // 橙色
        }

        // 相邻对齐（底部贴顶部）
        if (Math.Abs(draggedBottom - elementTop) <= tolerance)
        {
            CreateHorizontalLine(elementTop, Color.FromArgb(150, 255, 165, 0), 1.5); // 橙色
        }
    }

    /// <summary>
    /// 获取被拖拽元素的大小
    /// </summary>
    private Size GetDraggedElementSize()
    {
        if (_selectedElements.Count > 0)
        {
            var firstElement = _selectedElements.First();
            switch (firstElement)
            {
                case SFCStepViewModel:
                    return new Size(100, 60); // 步骤大小
                case SFCTransitionViewModel:
                    return new Size(80, 20); // 转换大小
                case SFCGraphNodeViewModel graphNode:
                    return graphNode.Size;
                default:
                    return new Size(100, 60); // 默认大小
            }
        }
        return new Size(100, 60); // 默认大小
    }



    /// <summary>
    /// 显示对齐高亮
    /// </summary>
    private void ShowAlignmentHighlights(Point alignedPosition)
    {
        // 获取现有元素（排除被拖拽的元素）
        var excludeElements = new HashSet<object>(_selectedElements);
        var existingElements = GetExistingElements(excludeElements);
        var draggedSize = GetDraggedElementSize();

        foreach (var element in existingElements)
        {
            // 检查是否与此元素对齐
            var alignmentResults = CheckAllAlignments(alignedPosition, element, draggedSize);
            if (alignmentResults.Any(r => r.Distance <= ELEMENT_SNAP_DISTANCE))
            {
                CreateAlignmentHighlight(element);
            }
        }
    }





    /// <summary>
    /// 创建垂直线
    /// </summary>
    private void CreateVerticalLine(double x, Color color, double thickness)
    {
        var line = new Line
        {
            X1 = x,
            Y1 = 0,
            X2 = x,
            Y2 = ActualHeight,
            Stroke = new SolidColorBrush(color),
            StrokeThickness = thickness,
            StrokeDashArray = new DoubleCollection { 8, 4 },
            IsHitTestVisible = false
        };
        Children.Add(line);
        _alignmentLines.Add(line);
    }

    /// <summary>
    /// 创建水平线
    /// </summary>
    private void CreateHorizontalLine(double y, Color color, double thickness)
    {
        var line = new Line
        {
            X1 = 0,
            Y1 = y,
            X2 = ActualWidth,
            Y2 = y,
            Stroke = new SolidColorBrush(color),
            StrokeThickness = thickness,
            StrokeDashArray = new DoubleCollection { 8, 4 },
            IsHitTestVisible = false
        };
        Children.Add(line);
        _alignmentLines.Add(line);
    }



    /// <summary>
    /// 创建网格对齐线
    /// </summary>
    private void CreateGridAlignmentLines(Point position)
    {
        // 垂直网格线
        var verticalLine = new Line
        {
            X1 = position.X,
            Y1 = 0,
            X2 = position.X,
            Y2 = ActualHeight,
            Stroke = new SolidColorBrush(Color.FromArgb(120, 255, 165, 0)), // 橙色
            StrokeThickness = 1,
            StrokeDashArray = new DoubleCollection { 4, 4 },
            IsHitTestVisible = false
        };
        Children.Add(verticalLine);
        _alignmentLines.Add(verticalLine);

        // 水平网格线
        var horizontalLine = new Line
        {
            X1 = 0,
            Y1 = position.Y,
            X2 = ActualWidth,
            Y2 = position.Y,
            Stroke = new SolidColorBrush(Color.FromArgb(120, 255, 165, 0)), // 橙色
            StrokeThickness = 1,
            StrokeDashArray = new DoubleCollection { 4, 4 },
            IsHitTestVisible = false
        };
        Children.Add(horizontalLine);
        _alignmentLines.Add(horizontalLine);
    }

    /// <summary>
    /// 创建对齐高亮
    /// </summary>
    private void CreateAlignmentHighlight(ElementInfo element)
    {
        var highlight = new Rectangle
        {
            Width = element.Size.Width + 4,
            Height = element.Size.Height + 4,
            Fill = new SolidColorBrush(Color.FromArgb(30, 255, 0, 255)), // 半透明洋红色
            Stroke = new SolidColorBrush(Color.FromArgb(150, 255, 0, 255)), // 洋红色边框
            StrokeThickness = 2,
            StrokeDashArray = new DoubleCollection { 5, 5 },
            IsHitTestVisible = false
        };

        Canvas.SetLeft(highlight, element.Position.X - 2);
        Canvas.SetTop(highlight, element.Position.Y - 2);
        Children.Add(highlight);
        _alignmentHighlights.Add(highlight);
    }

    /// <summary>
    /// 更新拖拽预览
    /// </summary>
    private void UpdateDragPreview(Point position)
    {
        // 更新所有视觉元素的位置
        UpdatePositionPreview(position);
        UpdatePositionTooltip(position);
    }

    /// <summary>
    /// 隐藏拖拽预览
    /// </summary>
    private void HideDragPreview()
    {
        ClearAllDragVisuals();
    }

    /// <summary>
    /// 计算智能对齐位置
    /// </summary>
    private Point CalculateAlignedPosition(Point originalPosition)
    {
        var alignedPosition = originalPosition;
        _isSnapped = false;

        // 1. 网格对齐
        var gridAlignedPosition = SnapToGrid(originalPosition);
        var gridDistance = CalculateDistance(originalPosition, gridAlignedPosition);

        // 2. 元素对齐
        var elementAlignedPosition = SnapToElements(originalPosition);
        var elementDistance = CalculateDistance(originalPosition, elementAlignedPosition);

        // 3. 边界对齐
        var boundaryAlignedPosition = SnapToBoundaries(originalPosition);
        var boundaryDistance = boundaryAlignedPosition == originalPosition ?
            double.MaxValue : CalculateDistance(originalPosition, boundaryAlignedPosition);

        // 选择最近的对齐位置
        var minDistance = Math.Min(Math.Min(gridDistance, elementDistance), boundaryDistance);

        if (minDistance <= SNAP_DISTANCE)
        {
            _isSnapped = true;

            if (minDistance == gridDistance)
            {
                alignedPosition = gridAlignedPosition;
            }
            else if (minDistance == elementDistance)
            {
                alignedPosition = elementAlignedPosition;
            }
            else
            {
                alignedPosition = boundaryAlignedPosition;
            }
        }

        _snapPosition = alignedPosition;
        return alignedPosition;
    }

    /// <summary>
    /// 网格对齐
    /// </summary>
    private Point SnapToGrid(Point position)
    {
        var x = Math.Round(position.X / GRID_SIZE) * GRID_SIZE;
        var y = Math.Round(position.Y / GRID_SIZE) * GRID_SIZE;
        return new Point(x, y);
    }

    /// <summary>
    /// 元素对齐
    /// </summary>
    private Point SnapToElements(Point position)
    {
        var bestPosition = position;
        var minDistance = double.MaxValue;

        // 获取所有现有元素（排除被拖拽的元素）
        var excludeElements = new HashSet<object>(_selectedElements);
        var existingElements = GetExistingElements(excludeElements);

        // 获取被拖拽元素的大小
        var draggedSize = GetDraggedElementSize();

        foreach (var element in existingElements)
        {
            // 检查所有可能的对齐方式
            var alignmentResults = CheckAllAlignments(position, element, draggedSize);

            foreach (var result in alignmentResults)
            {
                if (result.Distance <= ELEMENT_SNAP_DISTANCE && result.Distance < minDistance)
                {
                    minDistance = result.Distance;
                    bestPosition = result.Position;
                }
            }
        }

        return bestPosition;
    }

    /// <summary>
    /// 检查所有对齐方式
    /// </summary>
    private List<AlignmentResult> CheckAllAlignments(Point position, ElementInfo element, Size draggedSize)
    {
        var results = new List<AlignmentResult>();

        // 计算被拖拽元素的边界
        var draggedLeft = position.X;
        var draggedRight = position.X + draggedSize.Width;
        var draggedTop = position.Y;
        var draggedBottom = position.Y + draggedSize.Height;
        var draggedCenterX = position.X + draggedSize.Width / 2;
        var draggedCenterY = position.Y + draggedSize.Height / 2;

        // 计算目标元素的边界
        var elementLeft = element.Position.X;
        var elementRight = element.Position.X + element.Size.Width;
        var elementTop = element.Position.Y;
        var elementBottom = element.Position.Y + element.Size.Height;
        var elementCenterX = element.Position.X + element.Size.Width / 2;
        var elementCenterY = element.Position.Y + element.Size.Height / 2;

        // 垂直对齐检查
        // 左边缘对齐
        var leftDistance = Math.Abs(draggedLeft - elementLeft);
        if (leftDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(elementLeft, position.Y),
                Distance = leftDistance,
                Type = "LeftAlign"
            });
        }

        // 右边缘对齐
        var rightDistance = Math.Abs(draggedRight - elementRight);
        if (rightDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(elementRight - draggedSize.Width, position.Y),
                Distance = rightDistance,
                Type = "RightAlign"
            });
        }

        // 中心对齐
        var centerXDistance = Math.Abs(draggedCenterX - elementCenterX);
        if (centerXDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(elementCenterX - draggedSize.Width / 2, position.Y),
                Distance = centerXDistance,
                Type = "CenterXAlign"
            });
        }

        // 水平对齐检查
        // 顶部对齐
        var topDistance = Math.Abs(draggedTop - elementTop);
        if (topDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(position.X, elementTop),
                Distance = topDistance,
                Type = "TopAlign"
            });
        }

        // 底部对齐
        var bottomDistance = Math.Abs(draggedBottom - elementBottom);
        if (bottomDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(position.X, elementBottom - draggedSize.Height),
                Distance = bottomDistance,
                Type = "BottomAlign"
            });
        }

        // 中心对齐
        var centerYDistance = Math.Abs(draggedCenterY - elementCenterY);
        if (centerYDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(position.X, elementCenterY - draggedSize.Height / 2),
                Distance = centerYDistance,
                Type = "CenterYAlign"
            });
        }

        // 相邻对齐检查
        // 左边贴右边
        var leftToRightDistance = Math.Abs(draggedLeft - elementRight);
        if (leftToRightDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(elementRight, position.Y),
                Distance = leftToRightDistance,
                Type = "LeftToRight"
            });
        }

        // 右边贴左边
        var rightToLeftDistance = Math.Abs(draggedRight - elementLeft);
        if (rightToLeftDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(elementLeft - draggedSize.Width, position.Y),
                Distance = rightToLeftDistance,
                Type = "RightToLeft"
            });
        }

        // 顶部贴底部
        var topToBottomDistance = Math.Abs(draggedTop - elementBottom);
        if (topToBottomDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(position.X, elementBottom),
                Distance = topToBottomDistance,
                Type = "TopToBottom"
            });
        }

        // 底部贴顶部
        var bottomToTopDistance = Math.Abs(draggedBottom - elementTop);
        if (bottomToTopDistance <= ELEMENT_SNAP_DISTANCE)
        {
            results.Add(new AlignmentResult
            {
                Position = new Point(position.X, elementTop - draggedSize.Height),
                Distance = bottomToTopDistance,
                Type = "BottomToTop"
            });
        }

        return results;
    }

    /// <summary>
    /// 对齐结果类
    /// </summary>
    private class AlignmentResult
    {
        public Point Position { get; set; }
        public double Distance { get; set; }
        public string Type { get; set; } = string.Empty;
    }

    /// <summary>
    /// 边界对齐
    /// </summary>
    private Point SnapToBoundaries(Point position)
    {
        var x = position.X;
        var y = position.Y;
        var snapped = false;

        // 左边界对齐
        if (Math.Abs(position.X) <= SNAP_DISTANCE)
        {
            x = 0;
            snapped = true;
        }

        // 上边界对齐
        if (Math.Abs(position.Y) <= SNAP_DISTANCE)
        {
            y = 0;
            snapped = true;
        }

        // 右边界对齐
        if (Math.Abs(position.X - ActualWidth) <= SNAP_DISTANCE)
        {
            x = ActualWidth;
            snapped = true;
        }

        // 下边界对齐
        if (Math.Abs(position.Y - ActualHeight) <= SNAP_DISTANCE)
        {
            y = ActualHeight;
            snapped = true;
        }

        // 如果没有发生边界吸附，返回原始位置
        // 这样边界距离就不会是0，不会干扰其他对齐方式
        return snapped ? new Point(x, y) : position;
    }

    /// <summary>
    /// 计算两点间距离
    /// </summary>
    private double CalculateDistance(Point p1, Point p2)
    {
        return Math.Sqrt(Math.Pow(p1.X - p2.X, 2) + Math.Pow(p1.Y - p2.Y, 2));
    }

    /// <summary>
    /// 根据节点定义获取元素大小
    /// </summary>
    private Size GetElementSizeFromNodeDefinition(NodeDefinition? nodeDefinition)
    {
        if (nodeDefinition == null) return new Size(100, 60);

        switch (nodeDefinition.Name)
        {
            case "插入步":
            case "SFC步骤":
            case "等待条件":
            case "延时执行":
            case "流程结束":
            case "子流程调用":
                return new Size(100, 60); // 步骤大小
            case "SFC跳转":
                return new Size(60, 50); // 跳转元素大小
            case "转移条件":
            case "SFC转换":
                return new Size(80, 20); // 转换大小
            case "并行分支":
            case "选择分支":
                return new Size(120, 40); // 分支大小
            default:
                return new Size(100, 60); // 默认大小
        }
    }

    /// <summary>
    /// 工具栏拖拽专用的对齐位置计算
    /// </summary>
    private Point CalculateAlignedPositionForToolbox(Point originalPosition, Size elementSize)
    {
        var alignedPosition = originalPosition;
        _isSnapped = false;

        // 获取ViewModel检查是否有选中元素
        var enhancedViewModel = DataContext as EnhancedSFCViewModel;
        
        // 优先级1：连接点对齐（如果有选中元素）
        if (enhancedViewModel?.SelectedElement != null)
        {
            try
            {
                // 根据拖拽的节点类型确定元素类型
                var nodeDefinition = _currentDragData as NodeDefinition;
                if (nodeDefinition != null)
                {
                    var elementType = GetElementTypeFromNodeDefinition(nodeDefinition);
                    
                    // 计算连接点对齐位置
                    var connectPointAlignedPosition = CalculateConnectPointAlignedInsertPosition(
                        enhancedViewModel.SelectedElement, 
                        elementType, 
                        elementSize);
                    
                    var connectPointDistance = CalculateDistance(originalPosition, connectPointAlignedPosition);
                    
                    // 如果连接点对齐位置在合理范围内，优先使用
                    if (connectPointDistance <= SNAP_DISTANCE * 3) // 扩大连接点对齐的吸附范围
                    {
                        _isSnapped = true;
                        _snapPosition = connectPointAlignedPosition;
                        
                        System.Diagnostics.Debug.WriteLine($"[CalculateAlignedPositionForToolbox] 使用连接点对齐: {connectPointAlignedPosition}");
                        return connectPointAlignedPosition;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CalculateAlignedPositionForToolbox] 连接点对齐失败: {ex.Message}");
            }
        }

        // 优先级2：网格对齐
        var gridAlignedPosition = SnapToGrid(originalPosition);
        var gridDistance = CalculateDistance(originalPosition, gridAlignedPosition);

        // 优先级3：元素对齐（使用工具栏专用方法）
        var elementAlignedPosition = SnapToElementsForToolbox(originalPosition, elementSize);
        var elementDistance = CalculateDistance(originalPosition, elementAlignedPosition);

        // 优先级4：边界对齐
        var boundaryAlignedPosition = SnapToBoundaries(originalPosition);
        var boundaryDistance = boundaryAlignedPosition == originalPosition ?
            double.MaxValue : CalculateDistance(originalPosition, boundaryAlignedPosition);

        // 选择最近的对齐位置
        var minDistance = Math.Min(Math.Min(gridDistance, elementDistance), boundaryDistance);

        if (minDistance <= SNAP_DISTANCE)
        {
            _isSnapped = true;

            if (minDistance == gridDistance)
            {
                alignedPosition = gridAlignedPosition;
            }
            else if (minDistance == elementDistance)
            {
                alignedPosition = elementAlignedPosition;
            }
            else
            {
                alignedPosition = boundaryAlignedPosition;
            }
        }

        _snapPosition = alignedPosition;
        return alignedPosition;
    }

    /// <summary>
    /// 工具栏拖拽专用的元素对齐
    /// </summary>
    private Point SnapToElementsForToolbox(Point position, Size draggedSize)
    {
        var bestPosition = position;
        var minDistance = double.MaxValue;

        // 获取所有现有元素（工具栏拖拽时不需要排除元素）
        var existingElements = GetExistingElements(null);

        foreach (var element in existingElements)
        {
            // 检查所有可能的对齐方式
            var alignmentResults = CheckAllAlignments(position, element, draggedSize);

            foreach (var result in alignmentResults)
            {
                if (result.Distance <= ELEMENT_SNAP_DISTANCE && result.Distance < minDistance)
                {
                    minDistance = result.Distance;
                    bestPosition = result.Position;
                }
            }
        }

        return bestPosition;
    }

    /// <summary>
    /// 工具栏拖拽专用的对齐辅助
    /// </summary>
    private void UpdateAlignmentAssistForToolbox(Point originalPosition, Point alignedPosition, Size elementSize)
    {
        // 清除之前的对齐视觉元素
        ClearAlignmentVisuals();

        // 如果位置发生了吸附，显示对齐辅助
        if (_isSnapped)
        {
            ShowAlignmentLinesForToolbox(originalPosition, alignedPosition, elementSize);
            ShowAlignmentHighlightsForToolbox(alignedPosition, elementSize);
        }

        // 更新网格对齐线
        UpdateGridAlignment(alignedPosition);
    }

    /// <summary>
    /// 工具栏拖拽专用的对齐线显示
    /// </summary>
    private void ShowAlignmentLinesForToolbox(Point originalPosition, Point alignedPosition, Size draggedSize)
    {
        // 获取所有现有元素
        var existingElements = GetExistingElements(null);

        foreach (var element in existingElements)
        {
            // 检查所有可能的对齐并显示对齐线
            ShowAlignmentLinesForElement(originalPosition, element, draggedSize);
        }

        // 如果是网格对齐，显示网格线
        var gridPosition = SnapToGrid(originalPosition);
        if (CalculateDistance(alignedPosition, gridPosition) < 1.0)
        {
            CreateGridAlignmentLines(alignedPosition);
        }
    }

    /// <summary>
    /// 工具栏拖拽专用的对齐高亮
    /// </summary>
    private void ShowAlignmentHighlightsForToolbox(Point alignedPosition, Size draggedSize)
    {
        // 获取所有现有元素
        var existingElements = GetExistingElements(null);

        foreach (var element in existingElements)
        {
            // 检查是否与此元素对齐
            var alignmentResults = CheckAllAlignments(alignedPosition, element, draggedSize);
            if (alignmentResults.Any(r => r.Distance <= ELEMENT_SNAP_DISTANCE))
            {
                CreateAlignmentHighlight(element);
            }
        }
    }

    /// <summary>
    /// 清除所有拖拽视觉元素
    /// </summary>
    private void ClearAllDragVisuals()
    {
        if (_dropZoneIndicator != null)
        {
            Children.Remove(_dropZoneIndicator);
            _dropZoneIndicator = null;
        }

        if (_positionPreview != null)
        {
            Children.Remove(_positionPreview);
            _positionPreview = null;
        }

        if (_gridLineX != null)
        {
            Children.Remove(_gridLineX);
            _gridLineX = null;
        }

        if (_gridLineY != null)
        {
            Children.Remove(_gridLineY);
            _gridLineY = null;
        }

        if (_positionTooltip != null)
        {
            Children.Remove(_positionTooltip);
            _positionTooltip = null;
        }

        // 清除对齐辅助线
        ClearAlignmentVisuals();
    }

    /// <summary>
    /// 清除对齐视觉元素
    /// </summary>
    private void ClearAlignmentVisuals()
    {
        foreach (var line in _alignmentLines)
        {
            Children.Remove(line);
        }
        _alignmentLines.Clear();

        foreach (var highlight in _alignmentHighlights)
        {
            Children.Remove(highlight);
        }
        _alignmentHighlights.Clear();
    }

    /// <summary>
    /// 从拖拽创建元素
    /// </summary>
    private void CreateElementFromDrop(NodeDefinition nodeDefinition, Point dropPosition)
    {
        // 获取ViewModel
        var enhancedViewModel = DataContext as EnhancedSFCViewModel;
        var sfcViewModel = DataContext as SFCEditorViewModel;

        Point finalPosition;

        if (enhancedViewModel != null)
        {
            // 智能位置计算：如果有选中元素，使用连接点对齐算法
            if (enhancedViewModel.SelectedElement != null)
            {
                // 根据节点定义确定元素类型
                string elementType = GetElementTypeFromNodeDefinition(nodeDefinition);
                
                // 获取目标元素尺寸
                var targetElementSize = GetElementSizeByType(elementType);
                
                // 使用新的连接点精确对齐算法
                finalPosition = CalculateConnectPointAlignedInsertPosition(
                    enhancedViewModel.SelectedElement, 
                    elementType, 
                    targetElementSize);
                
                System.Diagnostics.Debug.WriteLine($"[CreateElementFromDrop] 使用连接点对齐算法，位置: {finalPosition}");
            }
            else
            {
                // 没有选中元素，使用拖拽位置
                finalPosition = dropPosition;
                System.Diagnostics.Debug.WriteLine($"[CreateElementFromDrop] 使用拖拽位置: {finalPosition}");
            }

            // 根据节点定义的名称创建对应的元素
            switch (nodeDefinition.Name)
            {
                case "插入步":
                case "SFC步骤":
                    enhancedViewModel.AddStep(finalPosition);
                    break;
                case "转移条件":
                case "SFC转换":
                    enhancedViewModel.AddTransition(finalPosition);
                    break;
                case "并行分支":
                case "选择分支":
                    enhancedViewModel.AddBranch(finalPosition);
                    break;
                case "SFC跳转":
                    enhancedViewModel.InsertJumpAfterSelected();
                    break;
                case "等待条件":
                case "延时执行":
                case "流程结束":
                case "子流程调用":
                    // 这些特殊步骤类型已被简化，统一创建普通步骤
                    enhancedViewModel.AddStep(finalPosition);
                    break;
                default:
                    // 默认创建步骤
                    enhancedViewModel.AddStep(finalPosition);
                    break;
            }
        }
        else if (sfcViewModel != null)
        {
            // 对于SFCEditorViewModel，使用原有逻辑
            finalPosition = dropPosition;
            
            // 使用SFCEditorViewModel创建元素
            switch (nodeDefinition.Name)
            {
                case "插入步":
                case "SFC步骤":
                    CreateElementAtPosition("Step", finalPosition);
                    break;
                case "转移条件":
                case "SFC转换":
                    CreateElementAtPosition("Transition", finalPosition);
                    break;
                case "并行分支":
                case "选择分支":
                    CreateElementAtPosition("Branch", finalPosition);
                    break;
                case "等待条件":
                    CreateSpecialElementAtPosition("WaitCondition", finalPosition);
                    break;
                case "延时执行":
                    CreateSpecialElementAtPosition("Delay", finalPosition);
                    break;
                case "流程结束":
                    CreateSpecialElementAtPosition("EndFlow", finalPosition);
                    break;
                case "子流程调用":
                    CreateSpecialElementAtPosition("SubFlow", finalPosition);
                    break;
                default:
                    CreateElementAtPosition("Step", finalPosition);
                    break;
            }
        }
    }

    /// <summary>
    /// 显示放置成功反馈
    /// </summary>
    private void ShowDropSuccessFeedback(Point position, string elementName)
    {
        // 创建成功反馈动画
        var feedbackText = new TextBlock
        {
            Text = $"✓ 已创建 {elementName}",
            Foreground = new SolidColorBrush(Color.FromRgb(0, 200, 0)),
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Background = new SolidColorBrush(Color.FromArgb(200, 45, 45, 48)),
            Padding = new Thickness(8, 4, 8, 4),
            Opacity = 0
        };

        Canvas.SetLeft(feedbackText, position.X + 10);
        Canvas.SetTop(feedbackText, position.Y - 30);
        Children.Add(feedbackText);

        // 淡入动画
        var fadeIn = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(200));
        var fadeOut = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(300))
        {
            BeginTime = TimeSpan.FromMilliseconds(1500)
        };

        fadeOut.Completed += (s, e) => Children.Remove(feedbackText);

        feedbackText.BeginAnimation(UIElement.OpacityProperty, fadeIn);
        feedbackText.BeginAnimation(UIElement.OpacityProperty, fadeOut);
    }

    /// <summary>
    /// 播放放置动画
    /// </summary>
    private void PlayDropAnimation(Point position)
    {
        // 创建放置波纹效果
        var ripple = new Ellipse
        {
            Width = 10,
            Height = 10,
            Stroke = new SolidColorBrush(Color.FromArgb(255, 0, 255, 0)),
            StrokeThickness = 3,
            Fill = new SolidColorBrush(Color.FromArgb(50, 0, 255, 0)),
            IsHitTestVisible = false
        };

        Canvas.SetLeft(ripple, position.X - 5);
        Canvas.SetTop(ripple, position.Y - 5);
        Children.Add(ripple);

        // 波纹扩散动画
        var expandAnimation = new DoubleAnimation
        {
            From = 10,
            To = 80,
            Duration = TimeSpan.FromMilliseconds(600),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
        };

        var fadeAnimation = new DoubleAnimation
        {
            From = 1,
            To = 0,
            Duration = TimeSpan.FromMilliseconds(600),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
        };

        // 动画完成后移除元素
        fadeAnimation.Completed += (s, e) => Children.Remove(ripple);

        ripple.BeginAnimation(WidthProperty, expandAnimation);
        ripple.BeginAnimation(HeightProperty, expandAnimation);
        ripple.BeginAnimation(OpacityProperty, fadeAnimation);

        // 更新位置以保持居中
        var centerAnimation = new DoubleAnimation
        {
            From = position.X - 5,
            To = position.X - 40,
            Duration = TimeSpan.FromMilliseconds(600),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
        };

        var centerAnimationY = new DoubleAnimation
        {
            From = position.Y - 5,
            To = position.Y - 40,
            Duration = TimeSpan.FromMilliseconds(600),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
        };

        ripple.BeginAnimation(Canvas.LeftProperty, centerAnimation);
        ripple.BeginAnimation(Canvas.TopProperty, centerAnimationY);
    }

    /// <summary>
    /// 获取现有元素信息
    /// </summary>
    private List<ElementInfo> GetExistingElements()
    {
        return GetExistingElements(null);
    }

    /// <summary>
    /// 获取现有元素信息（排除指定元素）
    /// </summary>
    private List<ElementInfo> GetExistingElements(HashSet<object>? excludeElements)
    {
        var elements = new List<ElementInfo>();

        // 获取SFC步骤
        var enhancedViewModel = DataContext as EnhancedSFCViewModel;
        var sfcViewModel = DataContext as SFCEditorViewModel;

        if (enhancedViewModel != null)
        {
            foreach (var step in enhancedViewModel.StepViewModels)
            {
                // 排除被拖拽的元素
                if (excludeElements != null && excludeElements.Contains(step))
                    continue;

                elements.Add(new ElementInfo
                {
                    Position = step.Position,
                    Size = new Size(100, 60), // 默认步骤大小
                    Type = "Step",
                    ViewModel = step
                });
            }

            foreach (var transition in enhancedViewModel.TransitionViewModels)
            {
                // 排除被拖拽的元素
                if (excludeElements != null && excludeElements.Contains(transition))
                    continue;

                elements.Add(new ElementInfo
                {
                    Position = transition.Position,
                    Size = new Size(80, 20), // 默认转换大小
                    Type = "Transition",
                    ViewModel = transition
                });
            }
        }
        else if (sfcViewModel != null)
        {
            if (sfcViewModel.GraphNodes != null)
            {
                foreach (var node in sfcViewModel.GraphNodes)
                {
                    // 排除被拖拽的元素
                    if (excludeElements != null && excludeElements.Contains(node))
                        continue;

                    elements.Add(new ElementInfo
                    {
                        Position = node.Position,
                        Size = node.Size,
                        Type = "GraphNode",
                        ViewModel = node
                    });
                }
            }
        }

        return elements;
    }



    /// <summary>
    /// 元素信息类
    /// </summary>
    private class ElementInfo
    {
        public Point Position { get; set; }
        public Size Size { get; set; }
        public string Type { get; set; } = string.Empty;
        public object? ViewModel { get; set; }
    }

    #endregion

    // 在OnMouseLeftButtonDown方法中添加拖拽连接的处理
    private void StartConnectionDrag(FrameworkElement sourceElement, Point startPoint)
    {
        if (sourceElement == null) return;
        
        // 查找SFC元素
        var sfcElement = FindSFCElement(sourceElement);
        if (sfcElement == null || sfcElement.DataContext == null) return;
        
        // 获取元素的类型
        var elementType = GetElementType(sfcElement.DataContext);
        if (elementType == "Unknown") return;
        
        _isDraggingConnection = true;
        _connectionDragSource = sfcElement.DataContext;
        
        // 获取元素中心点作为连接起点
        _connectionDragStartPoint = GetElementCenter(sfcElement);
        
        // 创建临时连接线
        CreateTemporaryConnectionLine(_connectionDragStartPoint, startPoint);
        CaptureMouse();
        UpdateStatusDisplay($"正在从{elementType}创建连接 | 拖拽到目标元素并释放鼠标完成连接");
    }
    
    // 在OnMouseMove方法中添加拖拽连接的处理
    private void UpdateConnectionDrag(Point currentPoint)
    {
        if (_temporaryConnectionPath != null && _connectionDragSource != null)
        {
            UpdateTemporaryConnectionLine(_connectionDragStartPoint, currentPoint);
            
            // 查找鼠标下的目标元素
            var hitTestResult = VisualTreeHelper.HitTest(this, currentPoint);
            if (hitTestResult?.VisualHit is FrameworkElement targetElement)
            {
                UpdateCursorForConnectionTarget(targetElement);
            }
        }
    }
    
    // 在OnMouseLeftButtonUp方法中添加拖拽连接的处理
    private void EndConnectionDrag(Point endPoint)
    {
        RemoveTemporaryConnectionLine();

        var hitTestResult = VisualTreeHelper.HitTest(this, endPoint);
        if (hitTestResult?.VisualHit is FrameworkElement targetElement)
        {
            var finalTargetElement = FindSFCElement(targetElement);
            if (finalTargetElement != null)
            {
                CreateConnection(_connectionDragSource, finalTargetElement.DataContext);
            }
        }

        _connectionDragSource = null;
        Cursor = Cursors.Arrow;
    }
    
    // 创建临时连接线
    private void CreateTemporaryConnectionLine(Point startPoint, Point endPoint)
    {
        // 移除之前的临时连接线
        RemoveTemporaryConnectionLine();
        
        // 创建新的临时连接线
        _temporaryConnectionPath = new Path
        {
            Stroke = new SolidColorBrush(Color.FromRgb(0, 122, 204)),
            StrokeThickness = 2,
            StrokeDashArray = new DoubleCollection { 4, 2 },
            Opacity = 0.7
        };
        
        // 创建贝塞尔曲线几何图形
        var geometry = new PathGeometry();
        var figure = new PathFigure();
        figure.StartPoint = startPoint;
        
        // 计算控制点
        double controlPointDistance = Math.Max(80, Math.Abs(endPoint.Y - startPoint.Y) * 0.5);
        var sourceControlPoint = new Point(startPoint.X, startPoint.Y + controlPointDistance);
        var targetControlPoint = new Point(endPoint.X, endPoint.Y - controlPointDistance);
        
        // 如果是水平方向的连接，则使用水平控制点
        if (Math.Abs(endPoint.Y - startPoint.Y) < 50)
        {
            double horizontalDistance = Math.Abs(endPoint.X - startPoint.X) * 0.5;
            sourceControlPoint = new Point(startPoint.X + horizontalDistance, startPoint.Y);
            targetControlPoint = new Point(endPoint.X - horizontalDistance, endPoint.Y);
        }
        
        // 创建贝塞尔曲线段
        var bezierSegment = new BezierSegment(
            sourceControlPoint,
            targetControlPoint,
            endPoint,
            true);
        
        figure.Segments.Add(bezierSegment);
        geometry.Figures.Add(figure);
        _temporaryConnectionPath.Data = geometry;
        
        // 箭头
        _temporaryConnectionArrow = new Polygon
        {
            Fill = new SolidColorBrush(Color.FromRgb(0, 122, 204)),
            Points = new PointCollection
            {
                new Point(0, 0),
                new Point(-8, -4),
                new Point(-8, 4)
            },
            Opacity = 0.7,
            RenderTransform = new RotateTransform()
        };
        
        // 计算箭头位置和旋转角度
        double angle = Math.Atan2(endPoint.Y - startPoint.Y, endPoint.X - startPoint.X) * 180 / Math.PI;
        _temporaryConnectionArrow.RenderTransform = new RotateTransform(angle);
        SetLeft(_temporaryConnectionArrow, endPoint.X);
        SetTop(_temporaryConnectionArrow, endPoint.Y);
        
        // 添加到画布
        Children.Add(_temporaryConnectionPath);
        Children.Add(_temporaryConnectionArrow);
    }
    
    // 更新临时连接线
    private void UpdateTemporaryConnectionLine(Point startPoint, Point endPoint)
    {
        if (_temporaryConnectionPath == null || _temporaryConnectionArrow == null)
        {
            CreateTemporaryConnectionLine(startPoint, endPoint);
            return;
        }
        
        // 更新贝塞尔曲线
        var geometry = new PathGeometry();
        var figure = new PathFigure();
        figure.StartPoint = startPoint;
        
        // 计算控制点
        double controlPointDistance = Math.Max(80, Math.Abs(endPoint.Y - startPoint.Y) * 0.5);
        var sourceControlPoint = new Point(startPoint.X, startPoint.Y + controlPointDistance);
        var targetControlPoint = new Point(endPoint.X, endPoint.Y - controlPointDistance);
        
        // 如果是水平方向的连接，则使用水平控制点
        if (Math.Abs(endPoint.Y - startPoint.Y) < 50)
        {
            double horizontalDistance = Math.Abs(endPoint.X - startPoint.X) * 0.5;
            sourceControlPoint = new Point(startPoint.X + horizontalDistance, startPoint.Y);
            targetControlPoint = new Point(endPoint.X - horizontalDistance, endPoint.Y);
        }
        
        // 创建贝塞尔曲线段
        var bezierSegment = new BezierSegment(
            sourceControlPoint,
            targetControlPoint,
            endPoint,
            true);
        
        figure.Segments.Add(bezierSegment);
        geometry.Figures.Add(figure);
        _temporaryConnectionPath.Data = geometry;
        
        // 更新箭头位置和旋转角度
        double angle = Math.Atan2(endPoint.Y - startPoint.Y, endPoint.X - startPoint.X) * 180 / Math.PI;
        _temporaryConnectionArrow.RenderTransform = new RotateTransform(angle);
        SetLeft(_temporaryConnectionArrow, endPoint.X);
        SetTop(_temporaryConnectionArrow, endPoint.Y);
    }
    
    // 移除临时连接线
    private void RemoveTemporaryConnectionLine()
    {
        if (_temporaryConnectionPath != null)
        {
            Children.Remove(_temporaryConnectionPath);
            _temporaryConnectionPath = null;
        }
        
        if (_temporaryConnectionArrow != null)
        {
            Children.Remove(_temporaryConnectionArrow);
            _temporaryConnectionArrow = null;
        }
    }
    
    // 更新光标显示
    private void UpdateCursorForConnectionTarget(FrameworkElement targetElement)
    {
        if (targetElement != null && _connectionDragSource != null)
        {
            var sourceDataContext = _connectionDragSource;
            var targetDataContext = targetElement.DataContext;
            
            // 获取源和目标类型
            string sourceType = GetElementType(sourceDataContext);
            string targetType = GetElementType(targetDataContext);
            
            // 判断连接是否有效
            bool isValid = false;
            
            switch (sourceType)
            {
                case "步骤":
                    // 步骤只能连接到转换或分支
                    isValid = targetType == "转换" || 
                              (targetType == "分支" && IsBranchValidForStepConnection(targetDataContext));
                    break;
                case "转换":
                    // 转换只能连接到步骤或分支
                    isValid = targetType == "步骤" || 
                              (targetType == "分支" && IsBranchValidForTransitionConnection(targetDataContext));
                    break;
                case "分支":
                    // 分支连接规则取决于分支类型和是否为汇聚分支
                    if (sourceDataContext is SFCBranchViewModel branchViewModel)
                    {
                        if (branchViewModel.BranchType == SFCBranchType.Parallel)
                        {
                            if (branchViewModel.IsConvergence)
                            {
                                // 并行汇聚分支只能连接到转换
                                isValid = targetType == "转换";
                            }
                            else
                            {
                                // 并行分支只能连接到步骤
                                isValid = targetType == "步骤";
                            }
                        }
                        else if (branchViewModel.BranchType == SFCBranchType.Selection)
                        {
                            if (branchViewModel.IsConvergence)
                            {
                                // 选择汇聚分支只能连接到转换
                                isValid = targetType == "转换";
                            }
                            else
                            {
                                // 选择分支只能连接到转换
                                isValid = targetType == "转换";
                            }
                        }
                    }
                    break;
            }
            
            // 更新光标
            Cursor = isValid ? Cursors.Arrow : Cursors.No;
        }
        else
        {
            Cursor = Cursors.Cross;
        }
    }

    // 连接点拖拽功能已移除，只保留自动连接



    private SFCConnectPoint? FindConnectPointAtPosition(Point position)
    {
        // 使用扩大的检测范围
        const double DETECTION_RADIUS = 20.0;
        
        // 将位置转换为屏幕坐标
        Point point = position;
        
        // 查找点下的视觉元素
        HitTestResult result = VisualTreeHelper.HitTest(this, point);
        if (result?.VisualHit != null)
        {
            // 查找连接点元素
            DependencyObject current = result.VisualHit;
            while (current != null && !(current is SFCConnectPoint))
            {
                current = VisualTreeHelper.GetParent(current);
            }
            
            // 返回连接点
            return current as SFCConnectPoint;
        }
        
        // 如果直接点击没有找到连接点，尝试在周围区域查找
        var connectPoints = new List<SFCConnectPoint>();
        
        // 收集画布上的所有连接点
        foreach (var child in Children)
        {
            if (child is SFCConnectPoint connectPoint)
            {
                connectPoints.Add(connectPoint);
            }
            else if (child is FrameworkElement element)
            {
                // 在元素内部查找连接点
                FindConnectPointsInElement(element, connectPoints);
            }
        }
        
        // 查找最近的连接点
        SFCConnectPoint? nearestPoint = null;
        double minDistance = DETECTION_RADIUS;
        
        foreach (var connectPoint in connectPoints)
        {
            // 获取连接点在画布中的位置
            var centerPoint = connectPoint.GetCenterPosition(this);
            var distance = CalculateDistance(centerPoint, position);
            
            if (distance < minDistance)
            {
                minDistance = distance;
                nearestPoint = connectPoint;
            }
        }
        
        return nearestPoint;
    }

    private IEnumerable<SFCConnectPoint> GetAllConnectPoints()
    {
        var allElements = _stepElements.Values
            .Concat(_transitionElements.Values)
            .Concat(_branchElements.Values)
            .Concat(_graphNodeElements.Values);

        foreach (var element in allElements)
        {
            var connectPoints = new List<SFCConnectPoint>();
            FindConnectPointsInElement(element, connectPoints);
            foreach (var cp in connectPoints)
            {
                yield return cp;
            }
        }
    }

    private void FindConnectPointsInElement(FrameworkElement element, List<SFCConnectPoint> connectPoints)
    {
        for (int i = 0; i < VisualTreeHelper.GetChildrenCount(element); i++)
        {
            var child = VisualTreeHelper.GetChild(element, i);
            
            if (child is SFCConnectPoint connectPoint)
            {
                connectPoints.Add(connectPoint);
            }
            else if (child is FrameworkElement frameworkElement)
            {
                FindConnectPointsInElement(frameworkElement, connectPoints);
            }
        }
    }



    private bool IsValidConnectPointConnection(ConnectPointEventArgs source, SFCConnectPoint target)
    {
        // 基本规则：输出点只能连接到输入点
        if (source.PointType != ConnectPointType.Output || target.PointType != ConnectPointType.Input)
        {
            return false;
        }
        
        // 不允许自连接
        if (source.ElementId == target.ElementId)
        {
            return false;
        }
        
        // 检查元素类型的连接规则
        return IsValidConnectionByElementType(source.ElementType, target.ElementType);
    }

    private void CreateConnectionFromConnectPoints(ConnectPointEventArgs source, SFCConnectPoint target)
    {
        var viewModel = DataContext as EnhancedSFCViewModel;
        if (viewModel == null) return;
        
        var sourceId = source.ElementId;
        var targetId = target.ElementId;
        
        // 确保sourceId和targetId有效
        if (string.IsNullOrEmpty(sourceId) || string.IsNullOrEmpty(targetId))
        {
            MessageBox.Show("连接失败：源或目标元素的ID无效。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        // 使用 AddConnection 方法创建连接
        viewModel.AddConnection(sourceId, targetId, source.Index, target.Index);
    }

    private bool IsBranchConvergence(string branchId)
    {
        // 查找分支模型
        var parentViewModel = DataContext as EnhancedSFCViewModel;
        if (parentViewModel?.Branches == null) return false;
        
        var branch = parentViewModel.Branches.FirstOrDefault(b => b.Id == branchId);
        return branch?.IsConvergence ?? false;
    }

    private SFCBranchType GetBranchType(string branchId)
    {
        // 查找分支模型
        var parentViewModel = DataContext as EnhancedSFCViewModel;
        if (parentViewModel?.Branches == null) return SFCBranchType.Selection;
        
        var branch = parentViewModel.Branches.FirstOrDefault(b => b.Id == branchId);
        return branch?.BranchType ?? SFCBranchType.Selection;
    }

    private string GetElementTypeName(SFCElementType elementType)
    {
        switch (elementType)
        {
            case SFCElementType.Step:
                return "步骤";
            case SFCElementType.Transition:
                return "转换";
            case SFCElementType.Branch:
                return "分支";
            case SFCElementType.Jump:
                return "跳转";
            default:
                return "未知";
        }
    }

    private Point GetConnectPointPosition(FrameworkElement element, SFCElementType elementType, int connectPointIndex, bool isSource)
    {
        // 使用精确的连接点位置计算
        var viewModel = DataContext as EnhancedSFCViewModel;
        if (viewModel != null)
        {
            // 获取元素在画布中的位置
            var elementPosition = new Point(Canvas.GetLeft(element), Canvas.GetTop(element));

            // 根据元素类型创建对应的对象来调用精确计算方法
            object elementObject = null;

            switch (elementType)
            {
                case SFCElementType.Step:
                    // 检查是否为顺控器终止元素
                    var tempStep = new SFCStepViewModel();
                    if (element is FrameworkElement stepFrameworkElement && stepFrameworkElement.DataContext is SFCStepViewModel actualStep)
                    {
                        // 复制关键属性以便正确识别顺控器终止元素
                        tempStep.Description = actualStep.Description;
                        tempStep.Name = actualStep.Name;
                    }
                    elementObject = tempStep;
                    break;
                case SFCElementType.Transition:
                    elementObject = new SFCTransitionViewModel(); // 临时对象，仅用于类型判断
                    break;
                case SFCElementType.Branch:
                    // 创建临时分支对象，需要根据实际分支类型设置
                    var tempBranch = new SFCBranchViewModel();
                    // 尝试从实际元素获取分支类型
                    if (element is FrameworkElement frameworkElement && frameworkElement.DataContext is SFCBranchViewModel actualBranch)
                    {
                        tempBranch.BranchType = actualBranch.BranchType;
                    }
                    else
                    {
                        tempBranch.BranchType = SFCBranchType.Selection; // 默认为选择分支
                    }
                    elementObject = tempBranch;
                    break;
                case SFCElementType.Jump:
                    // 创建临时跳转对象
                    elementObject = new SFCJumpViewModel();
                    break;
            }

            if (elementObject != null)
            {
                // 使用精确的连接点计算方法，传递连接点索引
                return viewModel.CalculateElementConnectPoint(elementObject, elementPosition, isSource, connectPointIndex);
            }
        }

        // 回退到默认计算方法
        Point defaultPosition = GetElementCenter(element);

        try
        {
            switch (elementType)
            {
                case SFCElementType.Step:
                    // 步骤的连接位置：基于XAML定义的精确位置
                    if (isSource) // 输出连接点 - 下连接点
                    {
                        var stepPos = new Point(Canvas.GetLeft(element), Canvas.GetTop(element));
                        return new Point(stepPos.X + 45 + 5, stepPos.Y + 117 + 5);
                    }
                    else // 输入连接点 - 上连接点
                    {
                        var stepPos = new Point(Canvas.GetLeft(element), Canvas.GetTop(element));
                        return new Point(stepPos.X + 45 + 5, stepPos.Y + 1 + 5);
                    }

                case SFCElementType.Transition:
                    // 转换条件的连接位置：基于XAML定义的精确位置
                    if (isSource) // 输出连接点 - 下连接点
                    {
                        var transPos = new Point(Canvas.GetLeft(element), Canvas.GetTop(element));
                        return new Point(transPos.X + 55.3 + 5, transPos.Y + 25 + 5);
                    }
                    else // 输入连接点 - 上连接点
                    {
                        var transPos = new Point(Canvas.GetLeft(element), Canvas.GetTop(element));
                        return new Point(transPos.X + 55.3 + 5, transPos.Y - 5 + 5);
                    }

                case SFCElementType.Branch:
                    // 选择分支的精确连接位置：基于XAML定义的实际连接点位置
                    var branchPos = new Point(Canvas.GetLeft(element), Canvas.GetTop(element));
                    if (isSource) // 输出连接点
                    {
                        // 选择分支的输出连接点（右侧下端）：Canvas.Left="162" Canvas.Top="61"
                        // 连接点控件尺寸10x10，中心偏移+5
                        return new Point(branchPos.X + 162 + 5, branchPos.Y + 61 + 5);
                    }
                    else // 输入连接点
                    {
                        // 扩展选择分支的连接线应该连接到横线左端点，而不是隐藏的连接点
                        // 横线位置：Canvas.Left="107" + X1="29" = 136, Canvas.Top="36" + Y1="15" = 51
                        return new Point(branchPos.X + 136, branchPos.Y + 51);
                    }

                case SFCElementType.Jump:
                    // 跳转元素的连接位置：基于SFCJumpView.xaml定义的精确位置
                    var jumpPos = new Point(Canvas.GetLeft(element), Canvas.GetTop(element));
                    if (isSource) // 输出连接点
                    {
                        // 跳转元素没有输出连接点，返回底部中心位置作为参考
                        return new Point(jumpPos.X + 30, jumpPos.Y + 50);
                    }
                    else // 输入连接点
                    {
                        // 跳转元素的输入连接点：Canvas.Left="25" Canvas.Top="6"
                        // 连接点控件尺寸10x10，中心偏移+5
                        return new Point(jumpPos.X + 25 + 5, jumpPos.Y + 6 + 5);
                    }
            }
        }
        catch (Exception ex)
        {
            // 发生异常时记录日志并使用默认位置
            System.Diagnostics.Debug.WriteLine($"获取连接位置失败: {ex.Message}");
        }

        // 如果没有找到连接点或发生异常，返回元素中心点
        return defaultPosition;
    }
    
    private SFCConnectPoint? FindConnectPointInElement(FrameworkElement element, SFCElementType elementType, int connectPointIndex, bool isSource)
    {
        // 查找元素内的连接点
        var connectPoints = new List<SFCConnectPoint>();
        FindConnectPointsInElement(element, connectPoints);

        // 简化日志：只在首次查找或出现问题时输出详细信息
        bool foundMatch = false;

        // 根据索引和类型查找对应的连接点
        foreach (var connectPoint in connectPoints)
        {
            if (connectPoint.Index == connectPointIndex)
            {
                // 检查连接点类型是否匹配
                bool typeMatches = (isSource && connectPoint.PointType == ConnectPointType.Output) ||
                                  (!isSource && connectPoint.PointType == ConnectPointType.Input);

                if (typeMatches)
                {
                    foundMatch = true;
                    return connectPoint;
                }
            }
        }

        // 重新启用详细调试信息来诊断连接点查找问题
        System.Diagnostics.Debug.WriteLine($"[FindConnectPointInElement] 查找连接点: {elementType}[{connectPointIndex}], 期望类型={(isSource ? "Output" : "Input")}, 找到{connectPoints.Count}个连接点");

        foreach (var connectPoint in connectPoints)
        {
            System.Diagnostics.Debug.WriteLine($"[FindConnectPointInElement] 检查连接点: 索引={connectPoint.Index}, 类型={connectPoint.PointType}, 元素ID='{connectPoint.ElementId}'");
        }

        if (!foundMatch)
        {
            System.Diagnostics.Debug.WriteLine($"[FindConnectPointInElement] ❌ 未找到匹配连接点");
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"[FindConnectPointInElement] ✅ 找到匹配连接点");
        }
        return null;
    }

    private bool IsValidConnectionByElementType(Models.SFCElementType sourceType, Models.SFCElementType targetType)
    {
        // 应用IEC 61131-3和西门子博图标准规则进行验证
        switch (sourceType)
        {
            case Models.SFCElementType.Step:
                // 步骤输出只能连接到转换的输入
                return targetType == Models.SFCElementType.Transition;
                
            case Models.SFCElementType.Transition:
                // 转换输出可以连接到步骤或分支的输入
                return targetType == Models.SFCElementType.Step || targetType == Models.SFCElementType.Branch;
                
            case Models.SFCElementType.Branch:
                // 分支输出根据分支类型连接到不同元素
                if (targetType == Models.SFCElementType.Step || targetType == Models.SFCElementType.Transition)
                    return true;
                return false;
                
            default:
                return false;
        }
    }

    /// <summary>
    /// 检查元素插入是否符合西门子Graph规则
    /// </summary>
    private bool ValidateElementInsertion(SFCElementType sourceType, SFCElementType targetType, out string message)
    {
        message = "";
        
        // 检查是否符合西门子Graph元素插入规则
        // 规则1：步骤只能连接到转换条件或选择分支
        if (sourceType == SFCElementType.Step)
        {
            if (targetType != SFCElementType.Transition && targetType != SFCElementType.Branch)
            {
                message = "步骤只能连接到转换条件或选择分支";
                return false;
            }
        }
        
        // 规则2：转换条件只能连接到步骤、结束流程、跳转或并行分支
        else if (sourceType == SFCElementType.Transition)
        {
            if (targetType != SFCElementType.Step && targetType != SFCElementType.Branch)
            {
                message = "转换条件只能连接到步骤、结束流程、跳转或并行分支";
                return false;
            }
        }
        
        // 规则3：分支特殊规则（由分支类型决定）
        else if (sourceType == SFCElementType.Branch)
        {
            // 分支规则在ValidateBranchInsertion方法中检查
            string branchMessage;
            bool isValidBranch = ValidateBranchInsertion(sourceType, targetType, out branchMessage);
            message = branchMessage;
            return isValidBranch;
        }
        
        // 默认允许连接
        return true;
    }

    /// <summary>
    /// 检查分支元素插入是否符合西门子Graph规则
    /// </summary>
    private bool ValidateBranchInsertion(SFCElementType sourceType, SFCElementType targetType, out string message)
    {
        // 这里需要访问分支类型信息，但SFCElementType中没有这些信息
        // 因此只能做一般性检查，具体分支类型检查会在ViewModel层进行
        
        message = "";
        // 默认允许分支连接
        return true;
    }

    /// <summary>
    /// SFC模型
    /// </summary>
    public SFCModel? SFCModel
    {
        get { return (SFCModel?)GetValue(SFCModelProperty); }
        set { SetValue(SFCModelProperty, value); }
    }

    /// <summary>
    /// SFC模型变化处理
    /// </summary>
    private static void OnSFCModelChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is SFCCanvas canvas)
        {
            var oldModel = e.OldValue as SFCModel;
            var newModel = e.NewValue as SFCModel;
            
            // 取消旧模型的事件订阅
            if (oldModel != null)
            {
                // 取消事件订阅
            }
            
            // 订阅新模型的事件
            if (newModel != null)
            {
                // 初始化连接交互管理器
                canvas.InitializeConnectionUIManager();
                
                // 更新验证状态
                canvas.UpdateValidationStatus();
            }
        }
    }
}
