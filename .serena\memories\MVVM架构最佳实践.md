# PC_Control2 MVVM架构最佳实践

## 📋 概述
本文档总结了PC_Control2项目中MVVM架构的最佳实践，包括ViewModel设计、数据绑定、命令模式、依赖注入等核心概念的标准实现方法。

---

## 🏗️ MVVM架构基础

### 架构层次关系
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      View       │◄──►│   ViewModel     │◄──►│     Model       │
│   (XAML + CS)   │    │  (业务逻辑)      │    │   (数据模型)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
    UI交互逻辑              数据绑定+命令            业务数据+服务
```

### 职责分离原则
- **Model**: 纯数据模型，不包含UI逻辑
- **ViewModel**: 业务逻辑，数据转换，命令处理
- **View**: 纯UI展示，最少的代码后置

---

## 🎯 ViewModel设计模式

### 1. 基础ViewModel实现

#### ViewModelBase基类
```csharp
public abstract class ViewModelBase : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}
```

#### 标准属性实现
```csharp
public class SFCStepViewModel : ViewModelBase
{
    private string _name;
    private bool _isSelected;
    private Point _position;

    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            if (SetProperty(ref _isSelected, value))
            {
                // 属性变化时的额外逻辑
                OnSelectionChanged();
            }
        }
    }

    public Point Position
    {
        get => _position;
        set => SetProperty(ref _position, value);
    }

    private void OnSelectionChanged()
    {
        // 选择状态变化的处理逻辑
        SelectionChanged?.Invoke(this, EventArgs.Empty);
    }

    public event EventHandler SelectionChanged;
}
```

### 2. 集合属性管理

#### ObservableCollection使用
```csharp
public class EnhancedSFCViewModel : ViewModelBase
{
    public ObservableCollection<SFCStepViewModel> Steps { get; }
    public ObservableCollection<SFCTransitionViewModel> Transitions { get; }
    public ObservableCollection<SFCConnectionViewModel> Connections { get; }

    public EnhancedSFCViewModel()
    {
        Steps = new ObservableCollection<SFCStepViewModel>();
        Transitions = new ObservableCollection<SFCTransitionViewModel>();
        Connections = new ObservableCollection<SFCConnectionViewModel>();

        // 监听集合变化
        Steps.CollectionChanged += OnStepsCollectionChanged;
    }

    private void OnStepsCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
    {
        switch (e.Action)
        {
            case NotifyCollectionChangedAction.Add:
                foreach (SFCStepViewModel step in e.NewItems)
                {
                    step.SelectionChanged += OnStepSelectionChanged;
                }
                break;

            case NotifyCollectionChangedAction.Remove:
                foreach (SFCStepViewModel step in e.OldItems)
                {
                    step.SelectionChanged -= OnStepSelectionChanged;
                }
                break;
        }
    }
}
```

### 3. 计算属性实现

```csharp
public class SFCBranchViewModel : ViewModelBase
{
    private SFCBranchType _branchType;
    private bool _isExpanded;

    public SFCBranchType BranchType
    {
        get => _branchType;
        set
        {
            if (SetProperty(ref _branchType, value))
            {
                // 通知相关计算属性更新
                OnPropertyChanged(nameof(IsSelectionBranch));
                OnPropertyChanged(nameof(IsParallelBranch));
                OnPropertyChanged(nameof(DisplayName));
            }
        }
    }

    // 计算属性 - 不需要后备字段
    public bool IsSelectionBranch => BranchType == SFCBranchType.Selection;
    public bool IsParallelBranch => BranchType == SFCBranchType.Parallel;
    
    public string DisplayName => BranchType switch
    {
        SFCBranchType.Selection => "选择分支",
        SFCBranchType.Parallel => "并行分支",
        _ => "未知分支"
    };
}
```

---

## 🎮 命令模式实现

### 1. RelayCommand实现

```csharp
public class RelayCommand : ICommand
{
    private readonly Action<object> _execute;
    private readonly Func<object, bool> _canExecute;

    public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public event EventHandler CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }

    public bool CanExecute(object parameter) => _canExecute?.Invoke(parameter) ?? true;

    public void Execute(object parameter) => _execute(parameter);
}

// 泛型版本
public class RelayCommand<T> : ICommand
{
    private readonly Action<T> _execute;
    private readonly Func<T, bool> _canExecute;

    public RelayCommand(Action<T> execute, Func<T, bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public event EventHandler CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }

    public bool CanExecute(object parameter)
    {
        if (parameter is T typedParameter)
            return _canExecute?.Invoke(typedParameter) ?? true;
        return false;
    }

    public void Execute(object parameter)
    {
        if (parameter is T typedParameter)
            _execute(typedParameter);
    }
}
```

### 2. 命令属性定义

```csharp
public class MainWindowViewModel : ViewModelBase
{
    public ICommand InsertStepCommand { get; }
    public ICommand InsertTransitionCommand { get; }
    public ICommand DeleteElementCommand { get; }
    public ICommand SaveProjectCommand { get; }

    public MainWindowViewModel()
    {
        // 简单命令
        InsertStepCommand = new RelayCommand(
            execute: _ => InsertStep(),
            canExecute: _ => CanInsertStep());

        // 带参数的命令
        DeleteElementCommand = new RelayCommand<SFCElementViewModel>(
            execute: element => DeleteElement(element),
            canExecute: element => element != null && CanDeleteElement(element));

        // 异步命令
        SaveProjectCommand = new RelayCommand(
            execute: async _ => await SaveProjectAsync(),
            canExecute: _ => HasUnsavedChanges);
    }

    private void InsertStep()
    {
        var newStep = new SFCStepViewModel
        {
            Name = $"步骤{Steps.Count + 1}",
            Position = CalculateNewStepPosition()
        };
        
        Steps.Add(newStep);
        SelectedElement = newStep;
    }

    private bool CanInsertStep()
    {
        return SelectedElement != null && 
               (SelectedElement is SFCStepViewModel || SelectedElement is SFCTransitionViewModel);
    }

    private async Task SaveProjectAsync()
    {
        try
        {
            IsLoading = true;
            await _projectService.SaveProjectAsync(CurrentProject);
            HasUnsavedChanges = false;
        }
        catch (Exception ex)
        {
            // 错误处理
            ShowErrorMessage($"保存失败: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }
}
```

### 3. 命令参数传递

```xml
<!-- 在XAML中使用命令 -->
<Button Content="插入步骤" Command="{Binding InsertStepCommand}" />

<Button Content="删除元素" 
        Command="{Binding DeleteElementCommand}" 
        CommandParameter="{Binding SelectedElement}" />

<!-- 在列表项中使用命令 -->
<ListBox ItemsSource="{Binding Steps}">
    <ListBox.ItemTemplate>
        <DataTemplate>
            <StackPanel>
                <TextBlock Text="{Binding Name}" />
                <Button Content="删除" 
                        Command="{Binding DataContext.DeleteElementCommand, 
                                 RelativeSource={RelativeSource AncestorType=ListBox}}"
                        CommandParameter="{Binding}" />
            </StackPanel>
        </DataTemplate>
    </ListBox.ItemTemplate>
</ListBox>
```

---

## 🔗 数据绑定最佳实践

### 1. 绑定模式选择

```xml
<!-- OneWay: 只从源到目标，性能最好 -->
<TextBlock Text="{Binding Name, Mode=OneWay}" />

<!-- TwoWay: 双向绑定，用于输入控件 -->
<TextBox Text="{Binding Name, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

<!-- OneTime: 一次性绑定，用于静态数据 -->
<TextBlock Text="{Binding Version, Mode=OneTime}" />

<!-- OneWayToSource: 只从目标到源，少见 -->
<Slider Value="{Binding CurrentValue, Mode=OneWayToSource}" />
```

### 2. 值转换器使用

```csharp
// 布尔到可见性转换器
public class BoolToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            bool invert = parameter?.ToString() == "Invert";
            bool result = invert ? !boolValue : boolValue;
            return result ? Visibility.Visible : Visibility.Collapsed;
        }
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Visibility visibility)
        {
            bool invert = parameter?.ToString() == "Invert";
            bool result = visibility == Visibility.Visible;
            return invert ? !result : result;
        }
        return false;
    }
}

// 枚举到字符串转换器
public class EnumToStringConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Enum enumValue)
        {
            return enumValue.ToString();
        }
        return string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string stringValue && targetType.IsEnum)
        {
            return Enum.Parse(targetType, stringValue);
        }
        return Binding.DoNothing;
    }
}
```

### 3. 多重绑定

```xml
<TextBlock>
    <TextBlock.Text>
        <MultiBinding StringFormat="{}{0} - {1}">
            <Binding Path="Name" />
            <Binding Path="Status" />
        </MultiBinding>
    </TextBlock.Text>
</TextBlock>

<!-- 使用多值转换器 -->
<Button IsEnabled="{Binding}">
    <Button.IsEnabled>
        <MultiBinding Converter="{StaticResource AndConverter}">
            <Binding Path="IsConnected" />
            <Binding Path="IsValid" />
            <Binding Path="HasPermission" />
        </MultiBinding>
    </Button.IsEnabled>
</Button>
```

### 4. 绑定调试

```xml
<!-- 启用绑定跟踪 -->
<TextBlock Text="{Binding Name, diag:PresentationTraceSources.TraceLevel=High}" />

<!-- 绑定回退值 -->
<TextBlock Text="{Binding Name, FallbackValue='未命名'}" />

<!-- 绑定目标为空时的值 -->
<TextBlock Text="{Binding Name, TargetNullValue='空值'}" />
```

---

## 💉 依赖注入最佳实践

### 1. 服务注册

```csharp
// App.xaml.cs
public partial class App : Application
{
    private ServiceProvider _serviceProvider;

    protected override void OnStartup(StartupEventArgs e)
    {
        var services = new ServiceCollection();
        
        // 注册服务
        services.AddSingleton<IProjectService, ProjectService>();
        services.AddSingleton<IBlueprintService, BlueprintService>();
        services.AddSingleton<ISFCValidator, SFCValidator>();
        
        // 注册ViewModels
        services.AddTransient<MainWindowViewModel>();
        services.AddTransient<EnhancedSFCViewModel>();
        services.AddTransient<ProjectTreeViewModel>();
        
        // 注册Views
        services.AddTransient<MainWindow>();
        
        _serviceProvider = services.BuildServiceProvider();
        
        // 启动主窗口
        var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();
        
        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _serviceProvider?.Dispose();
        base.OnExit(e);
    }
}
```

### 2. ViewModel依赖注入

```csharp
public class MainWindowViewModel : ViewModelBase
{
    private readonly IProjectService _projectService;
    private readonly IBlueprintService _blueprintService;
    private readonly ISFCValidator _sfcValidator;

    public MainWindowViewModel(
        IProjectService projectService,
        IBlueprintService blueprintService,
        ISFCValidator sfcValidator)
    {
        _projectService = projectService ?? throw new ArgumentNullException(nameof(projectService));
        _blueprintService = blueprintService ?? throw new ArgumentNullException(nameof(blueprintService));
        _sfcValidator = sfcValidator ?? throw new ArgumentNullException(nameof(sfcValidator));

        InitializeCommands();
        LoadProject();
    }

    private async void LoadProject()
    {
        try
        {
            CurrentProject = await _projectService.LoadDefaultProjectAsync();
        }
        catch (Exception ex)
        {
            ShowErrorMessage($"加载项目失败: {ex.Message}");
        }
    }
}
```

### 3. View与ViewModel关联

```csharp
// MainWindow.xaml.cs
public partial class MainWindow : Window
{
    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
    }
}

// 或者使用ViewModelLocator模式
public class ViewModelLocator
{
    private static readonly ServiceProvider ServiceProvider;

    static ViewModelLocator()
    {
        var services = new ServiceCollection();
        // 配置服务...
        ServiceProvider = services.BuildServiceProvider();
    }

    public MainWindowViewModel MainWindowViewModel => 
        ServiceProvider.GetRequiredService<MainWindowViewModel>();

    public EnhancedSFCViewModel EnhancedSFCViewModel => 
        ServiceProvider.GetRequiredService<EnhancedSFCViewModel>();
}
```

---

## 🔄 事件处理和通信

### 1. 事件聚合器模式

```csharp
public interface IEventAggregator
{
    void Subscribe<T>(Action<T> handler);
    void Unsubscribe<T>(Action<T> handler);
    void Publish<T>(T eventData);
}

public class EventAggregator : IEventAggregator
{
    private readonly Dictionary<Type, List<Delegate>> _handlers = new();

    public void Subscribe<T>(Action<T> handler)
    {
        if (!_handlers.ContainsKey(typeof(T)))
            _handlers[typeof(T)] = new List<Delegate>();
        
        _handlers[typeof(T)].Add(handler);
    }

    public void Unsubscribe<T>(Action<T> handler)
    {
        if (_handlers.ContainsKey(typeof(T)))
        {
            _handlers[typeof(T)].Remove(handler);
        }
    }

    public void Publish<T>(T eventData)
    {
        if (_handlers.ContainsKey(typeof(T)))
        {
            foreach (var handler in _handlers[typeof(T)].Cast<Action<T>>())
            {
                handler(eventData);
            }
        }
    }
}

// 事件定义
public class ElementSelectedEvent
{
    public SFCElementViewModel SelectedElement { get; set; }
}

// 使用示例
public class ProjectTreeViewModel : ViewModelBase
{
    private readonly IEventAggregator _eventAggregator;

    public ProjectTreeViewModel(IEventAggregator eventAggregator)
    {
        _eventAggregator = eventAggregator;
    }

    private void OnNodeSelected(SFCElementViewModel element)
    {
        _eventAggregator.Publish(new ElementSelectedEvent { SelectedElement = element });
    }
}
```

### 2. 弱事件模式

```csharp
public class WeakEventManager<TEventArgs> where TEventArgs : EventArgs
{
    private readonly List<WeakReference> _handlers = new();

    public void AddHandler(EventHandler<TEventArgs> handler)
    {
        _handlers.Add(new WeakReference(handler));
    }

    public void RemoveHandler(EventHandler<TEventArgs> handler)
    {
        for (int i = _handlers.Count - 1; i >= 0; i--)
        {
            if (_handlers[i].Target == handler)
            {
                _handlers.RemoveAt(i);
            }
        }
    }

    public void RaiseEvent(object sender, TEventArgs e)
    {
        for (int i = _handlers.Count - 1; i >= 0; i--)
        {
            if (_handlers[i].Target is EventHandler<TEventArgs> handler)
            {
                handler(sender, e);
            }
            else
            {
                _handlers.RemoveAt(i); // 清理已回收的引用
            }
        }
    }
}
```

---

## 📋 MVVM检查清单

### ViewModel设计
- [ ] 继承自ViewModelBase或实现INotifyPropertyChanged
- [ ] 使用SetProperty方法设置属性
- [ ] 计算属性正确通知依赖属性
- [ ] 集合使用ObservableCollection
- [ ] 正确处理事件订阅和取消订阅

### 命令实现
- [ ] 使用ICommand接口
- [ ] 实现CanExecute逻辑
- [ ] 异步命令正确处理异常
- [ ] 命令参数类型安全

### 数据绑定
- [ ] 选择合适的绑定模式
- [ ] 使用值转换器处理类型转换
- [ ] 设置FallbackValue和TargetNullValue
- [ ] 启用绑定调试（开发阶段）

### 依赖注入
- [ ] 服务接口定义清晰
- [ ] 生命周期管理正确
- [ ] 构造函数参数验证
- [ ] 资源正确释放

### 性能优化
- [ ] 避免频繁的属性通知
- [ ] 使用OneWay绑定替代TwoWay（适当时）
- [ ] 实现IDisposable接口
- [ ] 使用弱事件模式（必要时）

---

## 🎯 总结

MVVM架构的核心是**分离关注点**和**可测试性**。通过遵循这些最佳实践，可以构建出：

- **可维护的代码**：清晰的职责分离
- **可测试的逻辑**：业务逻辑与UI分离
- **可扩展的架构**：依赖注入和接口设计
- **高性能的应用**：合理的绑定和通知机制

记住：**简单、一致、可预测**是优秀MVVM实现的三个关键特征。