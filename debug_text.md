[连接点调试] 源连接点计算位置: 250,322
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 263.09090909090855,374.50000000000006
[连接点调试] 目标连接点计算位置: 263.09090909090855,374.50000000000006
[UpdateConnectionsForElement] ✅ 路径点已更新: 起点=250,322, 终点=263.09090909090855,374.50000000000006
线程 13120 已退出，返回值为 0 (0x0)。
[InsertSiblingBranch] 横线端点连接位置计算:
  当前分支位置: X=221, Y=319.5
  当前分支ViewType: Initial
  前一个分支横线右端: 361
  连接间隙: 0
  计算公式: 361 + 0 - 40 - (-46)
  新分支位置: X=367, Y=319.5
  验证：新分支横线最左端 = 361
  常量值: RIGHT_PART_LEFT=40, LINE_LEFT_OFFSET=-46, LINE_RIGHT_OFFSET=100, GAP=0
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引1适配器类型: Input (修复为Input)
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[SFCCanvas] 位置变化: SFCBranchViewModel.NextBranchId
[SFCCanvas] 为新创建的分支添加位置变化监听: 23c70eb5-f6ad-4197-86b3-0797fd01107e
[CreateBranchChainConnection] 选择分支扩展：使用左侧下端连接点（索引1）
[CreateBranchChainConnection] 分支链连接: Selection 源索引2 -> Selection 目标索引1
[CreateBranchChainConnection] 创建分支链连接: 260c6cee-dcf4-4a30-b6d6-d13d12fd2d3e -> 23c70eb5-f6ad-4197-86b3-0797fd01107e
[AddConnection] 开始执行: 260c6cee-dcf4-4a30-b6d6-d13d12fd2d3e -> 23c70eb5-f6ad-4197-86b3-0797fd01107e
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=234.09090909090855,367.50000000000006, 源Model位置=221,319.5
[AddConnection] 位置获取: 目标ViewModel位置=367,319.5, 目标Model位置=367,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=234.09090909090855,367.50000000000006, 输出点=True
[CalculateElementConnectPoint] ✅ 选择分支右上连接点(索引2): 401.09090909090855,402.50000000000006
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=367,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 417,325.5
[CalculateElementConnectPoint] ✅ 选择分支左下连接点(索引1): 396,354.5
[AddConnection] 创建连接: 260c6cee-dcf4-4a30-b6d6-d13d12fd2d3e -> 23c70eb5-f6ad-4197-86b3-0797fd01107e
[AddConnection] 源位置: 234.09090909090855,367.50000000000006, 目标位置: 367,319.5
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 401.09090909090855,402.50000000000006, 目标连接点: 396,354.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 e008e41a-8fee-4dd4-8426-77ad797e0eeb 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 401.09090909090855,402.50000000000006, 终点: 396,354.5
[AddConnection] 🔄 开始更新连接点状态: 260c6cee-dcf4-4a30-b6d6-d13d12fd2d3e -> 23c70eb5-f6ad-4197-86b3-0797fd01107e
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 2
  是否输入: False
  连接ID: e008e41a-8fee-4dd4-8426-77ad797e0eeb
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 260c6cee-dcf4-4a30-b6d6-d13d12fd2d3e, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Output, Index=2, AdapterIndex=2, ConnectionId: e008e41a-8fee-4dd4-8426-77ad797e0eeb
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 1
  是否输入: True
  连接ID: e008e41a-8fee-4dd4-8426-77ad797e0eeb
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 23c70eb5-f6ad-4197-86b3-0797fd01107e, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=1, AdapterIndex=1, ConnectionId: e008e41a-8fee-4dd4-8426-77ad797e0eeb
[UpdateConnectPointStates] 连接点状态更新完成: 260c6cee-dcf4-4a30-b6d6-d13d12fd2d3e[2] -> 23c70eb5-f6ad-4197-86b3-0797fd01107e[1]
[ValidateAndAdjustBranchChainLayout] 调整分支链布局，共2个分支
[ValidateAndAdjustBranchChainLayout] 分支链布局调整完成
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=19516075)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: 23c70eb5-f6ad-4197-86b3-0797fd01107e
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[SFCSelectionBranchView] 开始设置连接点ElementId: 23c70eb5-f6ad-4197-86b3-0797fd01107e
[SFCSelectionBranchView] ✅ LeftTop连接点ElementId设置: 23c70eb5-f6ad-4197-86b3-0797fd01107e
[SFCSelectionBranchView] ✅ LeftBottom连接点ElementId设置: 23c70eb5-f6ad-4197-86b3-0797fd01107e
[SFCSelectionBranchView] ✅ RightTop连接点ElementId设置: 23c70eb5-f6ad-4197-86b3-0797fd01107e
[SFCSelectionBranchView] ✅ RightBottom连接点ElementId设置: 23c70eb5-f6ad-4197-86b3-0797fd01107e
[InitializeConnectPoints] LeftTop连接点ElementId: '23c70eb5-f6ad-4197-86b3-0797fd01107e', 期望: '23c70eb5-f6ad-4197-86b3-0797fd01107e'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: '23c70eb5-f6ad-4197-86b3-0797fd01107e'
[SFCSelectionBranchView] 适配器检查: Count=4, 期望>=4
[SFCSelectionBranchView] ✅ LeftTop适配器绑定: Direction=Input, Index=0
[SFCSelectionBranchView] ✅ LeftBottom适配器绑定: Direction=Input, Index=1
[SFCSelectionBranchView] ✅ RightTop适配器绑定: Direction=Output, Index=2
[SFCSelectionBranchView] ✅ RightBottom适配器绑定: Direction=Output, Index=3
[SFCSelectionBranchView] 开始同步现有连接状态: 23c70eb5-f6ad-4197-86b3-0797fd01107e
[SFCSelectionBranchView] 找到 1 个相关连接
[SFCSelectionBranchView] ✅ 同步输入连接: e008e41a-8fee-4dd4-8426-77ad797e0eeb, 索引: 1
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=19516075)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=19516075); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=19516075)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=19516075); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=19516075)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=19516075); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=19516075)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=19516075); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=19516075)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=19516075); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=19516075)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=19516075); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=19516075)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=19516075); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: 23c70eb5-f6ad-4197-86b3-0797fd01107e
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[CreateConnectionPath] 连接 e008e41a-8fee-4dd4-8426-77ad797e0eeb 连接点不重叠，创建连接线
[CreateConnectionPath] 起点: (401.1, 402.5), 终点: (396.0, 354.5), 距离: 48.3px
[贝塞尔曲线创建] 起点: (401.1, 402.5), 终点: (396.0, 354.5)
[贝塞尔曲线创建] Y差异: 48.0px, X差异: 5.1px, 控制点偏移: 19.2px
[贝塞尔曲线创建] PathPoints[0]: (401.1, 402.5), PathPoints[1]: (396.0, 354.5)
添加连接线: e008e41a-8fee-4dd4-8426-77ad797e0eeb
[AddConnection] 延迟更新连接线 e008e41a-8fee-4dd4-8426-77ad797e0eeb 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel右上连接点(索引2): 409.09090909090855,402.50000000000006
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左下连接点(索引1): 396,354.5
[PathPoints集合变化] 连接 e008e41a-8fee-4dd4-8426-77ad797e0eeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 e008e41a-8fee-4dd4-8426-77ad797e0eeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 e008e41a-8fee-4dd4-8426-77ad797e0eeb 的PathPoints已更新，重新评估连接线
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 234.09090909090855,367.50000000000006, 目标位置: 367,319.5
[AddConnection] 延迟更新后的路径点: 409.09090909090855,402.50000000000006 -> 396,354.5
