[SFCBranchView] 选择分支，只处理选中逻辑
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=11400497)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InsertSiblingBranch] 横线端点连接位置计算:
  当前分支位置: X=221, Y=319.5
  当前分支ViewType: Initial
  前一个分支横线右端: 361
  连接间隙: 0
  计算公式: 361 + 0 - 40 - (-46)
  新分支位置: X=367, Y=319.5
  验证：新分支横线最左端 = 361
  常量值: RIGHT_PART_LEFT=40, LINE_LEFT_OFFSET=-46, LINE_RIGHT_OFFSET=100, GAP=0
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引1适配器类型: Input (修复为Input)
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[SFCCanvas] 位置变化: SFCBranchViewModel.NextBranchId
[SFCCanvas] 为新创建的分支添加位置变化监听: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[CreateBranchChainConnection] 选择分支扩展：使用左侧下端连接点（索引1）
[CreateBranchChainConnection] 分支链连接: Selection 源索引2 -> Selection 目标索引1
[CreateBranchChainConnection] 创建分支链连接: 10a23b93-5970-45b5-abbf-23bfa8218732 -> 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[AddConnection] 开始执行: 10a23b93-5970-45b5-abbf-23bfa8218732 -> 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=242.81818181818204,350.9181818181819, 源Model位置=221,319.5
[AddConnection] 位置获取: 目标ViewModel位置=367,319.5, 目标Model位置=367,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=242.81818181818204,350.9181818181819, 输出点=True
[CalculateElementConnectPoint] ✅ 选择分支右上连接点(索引2): 409.81818181818204,385.9181818181819
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=367,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 417,325.5
[CalculateElementConnectPoint] ✅ 选择分支左下连接点(索引1): 396,354.5
[AddConnection] 创建连接: 10a23b93-5970-45b5-abbf-23bfa8218732 -> 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[AddConnection] 源位置: 242.81818181818204,350.9181818181819, 目标位置: 367,319.5
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 409.81818181818204,385.9181818181819, 目标连接点: 396,354.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 bd174df1-eb87-4d23-b987-41c2ca0cfd25 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 409.81818181818204,385.9181818181819, 终点: 396,354.5
[AddConnection] 🔄 开始更新连接点状态: 10a23b93-5970-45b5-abbf-23bfa8218732 -> 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 2
  是否输入: False
  连接ID: bd174df1-eb87-4d23-b987-41c2ca0cfd25
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 10a23b93-5970-45b5-abbf-23bfa8218732, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Output, Index=2, AdapterIndex=2, ConnectionId: bd174df1-eb87-4d23-b987-41c2ca0cfd25
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 1
  是否输入: True
  连接ID: bd174df1-eb87-4d23-b987-41c2ca0cfd25
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=1, AdapterIndex=1, ConnectionId: bd174df1-eb87-4d23-b987-41c2ca0cfd25
[UpdateConnectPointStates] 连接点状态更新完成: 10a23b93-5970-45b5-abbf-23bfa8218732[2] -> 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c[1]
[ValidateAndAdjustBranchChainLayout] 调整分支链布局，共2个分支
[ValidateAndAdjustBranchChainLayout] 分支链布局调整完成
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=44212955)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[SFCSelectionBranchView] 开始设置连接点ElementId: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[SFCSelectionBranchView] ✅ LeftTop连接点ElementId设置: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[SFCSelectionBranchView] ✅ LeftBottom连接点ElementId设置: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[SFCSelectionBranchView] ✅ RightTop连接点ElementId设置: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[SFCSelectionBranchView] ✅ RightBottom连接点ElementId设置: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[InitializeConnectPoints] LeftTop连接点ElementId: '4d5d790c-bab9-4fb5-837d-ecc56b59eb8c', 期望: '4d5d790c-bab9-4fb5-837d-ecc56b59eb8c'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: '4d5d790c-bab9-4fb5-837d-ecc56b59eb8c'
[SFCSelectionBranchView] 适配器检查: Count=4, 期望>=4
[SFCSelectionBranchView] ✅ LeftTop适配器绑定: Direction=Input, Index=0
[SFCSelectionBranchView] ✅ LeftBottom适配器绑定: Direction=Input, Index=1
[SFCSelectionBranchView] ✅ RightTop适配器绑定: Direction=Output, Index=2
[SFCSelectionBranchView] ✅ RightBottom适配器绑定: Direction=Output, Index=3
[SFCSelectionBranchView] 开始同步现有连接状态: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
[SFCSelectionBranchView] 找到 1 个相关连接
[SFCSelectionBranchView] ✅ 同步输入连接: bd174df1-eb87-4d23-b987-41c2ca0cfd25, 索引: 1
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=44212955)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=44212955); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=44212955)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=44212955); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=44212955)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=44212955); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=44212955)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=44212955); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=44212955)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=44212955); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=44212955)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=44212955); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=44212955)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=44212955); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: 4d5d790c-bab9-4fb5-837d-ecc56b59eb8c
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[CreateConnectionPath] 连接 bd174df1-eb87-4d23-b987-41c2ca0cfd25 连接点不重叠，创建连接线
[CreateConnectionPath] 起点: (409.8, 385.9), 终点: (396.0, 354.5), 距离: 34.3px
[贝塞尔曲线创建] 起点: (409.8, 385.9), 终点: (396.0, 354.5)
[贝塞尔曲线创建] Y差异: 31.4px, X差异: 13.8px, 控制点偏移: 12.6px
[贝塞尔曲线创建] PathPoints[0]: (409.8, 385.9), PathPoints[1]: (396.0, 354.5)
添加连接线: bd174df1-eb87-4d23-b987-41c2ca0cfd25
[AddConnection] 延迟更新连接线 bd174df1-eb87-4d23-b987-41c2ca0cfd25 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel右上连接点(索引2): 417.81818181818204,385.9181818181819
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左下连接点(索引1): 396,354.5
[PathPoints集合变化] 连接 bd174df1-eb87-4d23-b987-41c2ca0cfd25 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 bd174df1-eb87-4d23-b987-41c2ca0cfd25 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 bd174df1-eb87-4d23-b987-41c2ca0cfd25 的PathPoints已更新，重新评估连接线
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 242.81818181818204,350.9181818181819, 目标位置: 367,319.5
[AddConnection] 延迟更新后的路径点: 417.81818181818204,385.9181818181819 -> 396,354.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
