# PC_Control2 常见问题排查指南

## 📋 概述
本指南汇总了PC_Control2项目开发过程中的常见问题及其解决方案，帮助开发者快速定位和解决技术障碍。

---

## 🔧 编译错误解决方案

### 1. NuGet包依赖问题

#### 问题现象
```
错误 CS0234: 命名空间"Microsoft.Extensions"中不存在类型或命名空间名"DependencyInjection"
```

#### 解决方案
```bash
# 检查并安装缺失的NuGet包
dotnet add package Microsoft.Extensions.DependencyInjection
dotnet add package Microsoft.Extensions.Hosting
dotnet restore
```

#### 常见缺失包
- `Microsoft.Extensions.DependencyInjection` - 依赖注入
- `Microsoft.Extensions.Hosting` - 主机服务
- `System.Text.Json` - JSON序列化
- `Microsoft.Xaml.Behaviors.Wpf` - WPF行为

### 2. 项目引用问题

#### 问题现象
```
错误 CS0246: 找不到类型或命名空间名"SFCModel"
```

#### 解决方案
1. **检查using语句**：
```csharp
using PC_Control2.Models;
using PC_Control2.ViewModels;
using PC_Control2.Services;
```

2. **检查项目文件**：
```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
  </PropertyGroup>
</Project>
```

### 3. XAML编译错误

#### 问题现象
```
错误 MC3000: 找不到类型"SFCStepView"
```

#### 解决方案
1. **检查命名空间映射**：
```xml
<UserControl xmlns:controls="clr-namespace:PC_Control2.Controls">
    <controls:SFCStepView />
</UserControl>
```

2. **检查Build Action**：
- XAML文件：`Page` 或 `UserControl`
- 代码文件：`Compile`

---

## ⚠️ 运行时异常处理

### 1. 空引用异常 (NullReferenceException)

#### 常见场景
```csharp
// 问题代码
var viewModel = DataContext as SFCStepViewModel;
viewModel.Name = "新步骤"; // 可能为null

// 解决方案
if (DataContext is SFCStepViewModel viewModel)
{
    viewModel.Name = "新步骤";
}
```

#### 防护模式
```csharp
// 使用null条件运算符
viewModel?.UpdateConnectPointStates();

// 使用null合并运算符
var name = viewModel?.Name ?? "默认名称";
```

### 2. 类型转换异常 (InvalidCastException)

#### 问题现象
```csharp
// 危险的强制转换
var step = (SFCStepViewModel)selectedElement;
```

#### 解决方案
```csharp
// 安全的类型检查
if (selectedElement is SFCStepViewModel step)
{
    // 安全使用step
}

// 或使用as操作符
var step = selectedElement as SFCStepViewModel;
if (step != null)
{
    // 安全使用step
}
```

### 3. 数据绑定异常

#### 问题现象
```
System.Windows.Data Error: 40 : BindingExpression path error
```

#### 调试方法
1. **启用绑定调试**：
```xml
<TextBlock Text="{Binding Name, diag:PresentationTraceSources.TraceLevel=High}" />
```

2. **检查属性通知**：
```csharp
public string Name
{
    get => _name;
    set
    {
        if (_name != value)
        {
            _name = value;
            OnPropertyChanged(); // 确保触发通知
        }
    }
}
```

---

## 🎨 UI显示问题

### 1. 控件不显示

#### 检查清单
- [ ] **Visibility属性**：确保不是`Collapsed`或`Hidden`
- [ ] **尺寸设置**：Width/Height不为0
- [ ] **父容器**：父容器有足够空间
- [ ] **Z-Index**：没有被其他控件遮挡
- [ ] **数据上下文**：DataContext正确设置

#### 调试技巧
```xml
<!-- 添加背景色调试布局 -->
<Grid Background="LightBlue">
    <TextBlock Text="{Binding Name}" Background="Yellow" />
</Grid>
```

### 2. 样式失效

#### 常见原因
1. **资源字典未加载**：
```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="Styles/SFCStyles.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

2. **样式Key冲突**：
```xml
<!-- 避免重复的Key -->
<Style x:Key="SFCStepStyle" TargetType="UserControl">
```

### 3. 布局错乱

#### Canvas布局问题
```csharp
// 确保Canvas位置正确设置
Canvas.SetLeft(element, position.X);
Canvas.SetTop(element, position.Y);

// 检查Canvas尺寸
canvas.Width = 2000;
canvas.Height = 1500;
```

#### 数据模板问题
```xml
<DataTemplate DataType="{x:Type vm:SFCStepViewModel}">
    <controls:SFCStepView />
</DataTemplate>
```

---

## 🚀 性能问题诊断

### 1. UI卡顿

#### 常见原因
- **频繁的属性通知**：避免在循环中触发PropertyChanged
- **复杂的数据绑定**：使用OneWay绑定替代TwoWay
- **大量UI元素**：考虑虚拟化

#### 优化方案
```csharp
// 批量更新时暂停通知
public void BatchUpdate(Action updateAction)
{
    _suppressNotifications = true;
    try
    {
        updateAction();
    }
    finally
    {
        _suppressNotifications = false;
        OnPropertyChanged(string.Empty); // 通知所有属性更新
    }
}
```

### 2. 内存泄漏

#### 检查要点
- **事件订阅**：确保正确取消订阅
- **静态引用**：避免静态集合持有对象引用
- **定时器**：确保定时器被正确释放

#### 防护代码
```csharp
public class SFCViewModel : IDisposable
{
    public void Dispose()
    {
        // 取消事件订阅
        SomeService.SomeEvent -= OnSomeEvent;
        
        // 释放资源
        _timer?.Dispose();
        
        // 清理集合
        Elements.Clear();
    }
}
```

---

## 🔍 调试技巧

### 1. 输出窗口调试

```csharp
// 使用Debug.WriteLine输出调试信息
Debug.WriteLine($"连接点状态更新: {connectPoint.Id} -> {isConnected}");

// 使用条件编译
#if DEBUG
    Console.WriteLine($"调试信息: {message}");
#endif
```

### 2. 断点调试技巧

#### 条件断点
```csharp
// 在断点上右键 -> 条件
// 条件: element.Id == "特定ID"
```

#### 数据断点
- 监视特定变量的值变化
- 在变量上右键 -> "中断时值更改"

### 3. XAML调试

#### 实时可视化树
- 调试时：调试 -> 窗口 -> 实时可视化树
- 查看控件层次结构和属性值

#### 绑定调试
```xml
<!-- 在XAML中启用绑定跟踪 -->
<TextBlock Text="{Binding Name, diag:PresentationTraceSources.TraceLevel=High}" />
```

---

## 📝 快速检查清单

### 编译问题
- [ ] NuGet包是否完整安装
- [ ] using语句是否正确
- [ ] 命名空间是否匹配
- [ ] 项目引用是否正确

### 运行时问题
- [ ] 空引用检查
- [ ] 类型转换安全性
- [ ] 事件订阅/取消订阅
- [ ] 资源释放

### UI问题
- [ ] DataContext设置
- [ ] 属性通知实现
- [ ] 样式资源加载
- [ ] 布局容器配置

### 性能问题
- [ ] 绑定模式优化
- [ ] 事件处理频率
- [ ] 内存泄漏检查
- [ ] UI虚拟化考虑

---

## 🆘 紧急救援命令

```bash
# 清理并重建项目
dotnet clean
dotnet restore
dotnet build

# 重置NuGet缓存
dotnet nuget locals all --clear

# 检查项目依赖
dotnet list package

# 运行项目
dotnet run
```

---

## 📞 获取帮助

当遇到无法解决的问题时：
1. **查看输出窗口**：编译错误和警告信息
2. **检查调试输出**：运行时异常详情
3. **使用诊断工具**：内存和CPU使用情况
4. **参考官方文档**：Microsoft WPF文档
5. **搜索相关问题**：Stack Overflow、GitHub Issues

记住：大多数问题都有标准的解决方案，保持耐心和系统性的排查方法是关键。