# SFC-蓝图统一运行时引擎设计指导书 v2.0

## 📋 设计背景与目标

### 原始诉求
传统PLC在设计软件中进行工程设计开发，开发完成后可以把工程文件下载到PLC中运行。当前项目专注于设计功能（SFC设计、蓝图设计），但作为最终产品，还需要运行时引擎功能。我们的平台软件设计初衷是包含设计功能和运行时引擎功能的完整解决方案。

### 核心设计理念：SFC-蓝图深度融合
基于最新的SFC-蓝图深度融合架构，运行时引擎采用以下设计原则：
- **职责分离**：SFC专注流程控制，蓝图专注逻辑实现
- **深度融合**：运行时统一执行，无缝协作
- **多模式支持**：解释器（调试）+ 编译器（性能）+ JIT（平衡）
- **工业级质量**：安全监控、硬件抽象、标准符合

### 技术策略整合
结合开源项目复用和自主创新：
- **复用Beremiz**：执行逻辑设计、硬件抽象、安全监控机制
- **自主C#编译器**：最适合PC环境的高性能执行
- **MatIEC验证**：确保IEC 61131-3标准符合性
- **现有架构集成**：与WPF/MVVM架构无缝融合

## 🏗️ 总体架构设计

### 三层混合执行模式

#### 第一层：开发调试模式（SFC-蓝图统一解释器）
```
用途：设计器中实时调试和可视化
技术特点：
├── 直接解释SFCModel和BlueprintModel数据结构
├── SFC解释器直接调用蓝图执行器，实现无缝执行
├── 支持热更新，修改设计后立即生效
├── 完美的"能流"可视化，实时状态反馈
└── 支持SFC和蓝图的联合调试和断点设置
```

#### 第二层：优化执行模式（SFC-蓝图统一编译器）
```
用途：生产环境高性能执行
技术特点：
├── 将SFC流程和关联蓝图编译为统一的C#程序集
├── SFC生成主控制逻辑，蓝图生成方法调用
├── 内联简单蓝图，缓存复杂蓝图的编译结果
├── 编译后的程序具有原生C#的执行性能
└── 支持增量编译和编译结果缓存
```

#### 第三层：智能优化模式（JIT性能优化）
```
用途：热点代码自动优化
技术特点：
├── 建立热点代码识别机制
├── 使用Roslyn实现动态编译优化
├── 智能执行模式切换（解释器/编译器/JIT）
├── 平衡性能和灵活性
└── 添加性能监控和分析工具
```

### 核心组件架构

#### 1. SFC-蓝图统一执行引擎（UnifiedExecutionEngine）
```csharp
public class UnifiedSFCBlueprintExecutionEngine : IExecutionEngine
{
    // 核心执行器
    private readonly UnifiedSFCBlueprintInterpreter _interpreter;
    private readonly UnifiedSFCBlueprintCompiler _compiler;
    private readonly JITOptimizer _jitOptimizer;
    
    // 共享执行上下文
    private readonly SharedExecutionContext _sharedContext;
    
    // 硬件和安全
    private readonly IHardwareManager _hardwareManager;
    private readonly SafetyMonitor _safetyMonitor;
    
    // 调试和监控
    private readonly IRuntimeDebugger _debugger;
    private readonly PerformanceMonitor _performanceMonitor;
}
```

#### 2. 共享执行上下文（SharedExecutionContext）
```csharp
public class SharedExecutionContext
{
    // SFC状态管理
    public Dictionary<string, object> SFCVariables { get; set; }
    public HashSet<string> ActiveSteps { get; set; }
    public SFCExecutionState CurrentState { get; set; }
    
    // 蓝图状态管理
    public Dictionary<string, object> BlueprintVariables { get; set; }
    public Stack<BlueprintCallFrame> CallStack { get; set; }
    
    // 硬件接口
    public IHardwareManager HardwareManager { get; set; }
    
    // 系统状态
    public DateTime ExecutionStartTime { get; set; }
    public TimeSpan ElapsedTime => DateTime.UtcNow - ExecutionStartTime;
    
    // 变量同步机制
    public void SyncSFCToBlueprintVariables(BlueprintCallInfo callInfo);
    public void SyncBlueprintToSFCVariables(BlueprintCallInfo callInfo);
}
```

#### 3. SFC-蓝图统一解释器（UnifiedInterpreter）
```csharp
public class UnifiedSFCBlueprintInterpreter
{
    public async Task<ExecutionResult> ExecuteAsync(SFCModel sfcModel)
    {
        // 初始化执行上下文
        InitializeExecutionContext(sfcModel);
        
        // 主执行循环
        while (_stateMachine.HasActiveSteps())
        {
            var activeSteps = _stateMachine.GetActiveSteps();
            
            // 并行执行所有活动步骤
            var stepTasks = activeSteps.Select(ExecuteStepAsync);
            await Task.WhenAll(stepTasks);
            
            // 检查转换条件（调用蓝图）
            await CheckTransitionsAsync(activeSteps);
            
            // 更新执行状态
            UpdateExecutionState();
        }
        
        return new ExecutionResult { Success = true, Context = _sharedContext };
    }
    
    private async Task<bool> EvaluateTransitionCondition(SFCTransitionModel transition)
    {
        // 深度融合：直接调用蓝图执行器
        var result = await _blueprintExecutor.ExecuteAsync(
            transition.ConditionBlueprintId, 
            _sharedContext);
            
        return result.GetValue<bool>();
    }
}
```

#### 4. SFC-蓝图统一编译器（UnifiedCompiler）
```csharp
public class UnifiedSFCBlueprintCompiler
{
    public async Task<CompilationResult> CompileAsync(SFCModel sfcModel)
    {
        var compilationUnit = new CompilationUnit();
        
        // 1. 收集所有关联的蓝图
        var referencedBlueprints = await CollectReferencedBlueprintsAsync(sfcModel);
        
        // 2. 编译蓝图为C#方法
        foreach (var blueprint in referencedBlueprints)
        {
            var method = await CompileBlueprintToMethodAsync(blueprint);
            compilationUnit.AddMethod(method);
        }
        
        // 3. 生成SFC主执行逻辑
        var mainMethod = await CompileSFCToMainMethodAsync(sfcModel);
        compilationUnit.AddMethod(mainMethod);
        
        // 4. 生成统一的类
        var classCode = GenerateUnifiedClass(compilationUnit);
        
        // 5. 编译为程序集
        return await CompileToAssemblyAsync(classCode);
    }
}
```

## 🔄 详细执行流程

### 智能启动流程

#### 第一阶段：项目加载与验证
1. **SFC-蓝图模型加载**：读取SFCModel、BlueprintModel及其关联关系
2. **融合完整性验证**：检查SFC-蓝图关联的完整性和一致性
3. **安全性检查**：验证安全级别、权限设置、资源限制
4. **性能预分析**：识别热点路径、预编译候选、内存需求估算

#### 第二阶段：运行时环境初始化
1. **共享执行上下文创建**：初始化SFC-蓝图统一变量表和状态机
2. **硬件连接建立**：基于Beremiz硬件抽象设计，扩展现有IHardwareService
3. **安全监控启动**：启动Beremiz安全监控机制，看门狗、异常监控
4. **调试器准备**：初始化SFC-蓝图联合调试器，支持断点管理

#### 第三阶段：执行引擎启动
1. **模式选择**：根据用户设置选择解释器/编译器/JIT模式
2. **扫描周期启动**：启动PLC扫描周期模型（读取输入→执行逻辑→写入输出）
3. **事件系统激活**：注册SFC-蓝图状态变化事件、UI更新事件
4. **首次扫描执行**：执行第一个完整的SFC-蓝图融合扫描周期

### 增强扫描周期（SFC-蓝图融合）

#### 高优先级周期（1ms - 安全关键）
```
1. 安全监控检查
   - 检查系统心跳和看门狗
   - 验证SFC-蓝图执行状态
   - 监控关键变量和执行时间

2. 紧急处理
   - 紧急停机信号检测
   - 安全级别验证
   - 异常恢复机制
```

#### 中优先级周期（10ms - 控制逻辑）
```
1. SFC状态机执行
   - 检查活动步骤状态
   - 评估转换条件（调用蓝图）
   - 执行状态转换

2. 蓝图逻辑执行
   - 执行步骤动作蓝图
   - 处理蓝图间的调用关系
   - 同步变量和状态

3. 硬件I/O处理
   - 读取输入变量
   - 写入输出变量
   - 处理通信协议
```

#### 低优先级周期（100ms - 监控诊断）
```
1. 性能监控
   - 执行时间统计
   - 内存使用监控
   - 热点代码识别

2. 调试支持
   - 断点检查
   - 变量监控更新
   - 执行历史记录

3. UI更新
   - 能流可视化更新
   - 状态显示刷新
   - 调试信息显示
```

## 🛡️ 安全监控系统

### 移植Beremiz安全监控机制
基于Beremiz的工业级安全设计，实现多层安全保护：

#### L1-执行安全
- **看门狗定时器**：1ms级别的系统心跳检测
- **执行超时检测**：SFC步骤和蓝图执行的超时保护
- **内存保护**：防止内存泄漏和越界访问
- **资源限制**：CPU、内存、文件句柄的使用限制

#### L2-逻辑安全
- **死锁检测**：检测SFC-蓝图执行中的潜在死锁
- **循环检测**：防止无限循环和递归调用
- **状态一致性**：确保SFC状态和蓝图状态的一致性
- **变量完整性**：检查变量类型和值的合法性

#### L3-系统安全
- **权限检查**：验证操作权限和安全级别
- **沙箱执行**：隔离执行环境，防止恶意代码
- **审计日志**：记录所有关键操作和状态变化
- **加密保护**：敏感数据的加密存储和传输

#### L4-业务安全
- **安全级别验证**：符合工业安全标准的级别管理
- **紧急停机**：多种紧急停机机制和故障恢复
- **故障隔离**：故障的快速定位和影响隔离
- **恢复机制**：自动恢复和人工干预的结合

## 🔧 硬件抽象层设计

### 移植Beremiz硬件抽象设计
学习Beremiz的硬件抽象层设计，扩展现有IHardwareService：

#### 核心接口扩展
```csharp
public interface IHardwareManager : IHardwareService
{
    // 设备生命周期管理
    Task<bool> ConnectDeviceAsync(DeviceConfig config);
    Task<bool> DisconnectDeviceAsync(string deviceId);
    Task<DeviceStatus> GetDeviceStatusAsync(string deviceId);
    
    // 多协议支持
    Task<bool> ConfigureProtocolAsync(string deviceId, ProtocolConfig config);
    Task<CommunicationResult> ExecuteCommandAsync(string deviceId, Command command);
    
    // 异步I/O处理
    Task<ReadResult> ReadVariablesAsync(List<VariableAddress> addresses);
    Task<WriteResult> WriteVariablesAsync(List<VariableWrite> writes);
    
    // 实时通信
    event EventHandler<VariableChangedEventArgs> VariableChanged;
    event EventHandler<DeviceStatusChangedEventArgs> DeviceStatusChanged;
    
    // 性能监控
    CommunicationStatistics GetCommunicationStatistics(string deviceId);
    Task<DiagnosticInfo> GetDiagnosticInfoAsync(string deviceId);
}
```

#### 支持的工业协议
- **Modbus RTU/TCP**：工业现场总线标准协议
- **OPC UA**：现代工业通信标准
- **Ethernet/IP**：以太网工业协议
- **串口通信**：RS232/RS485串口设备
- **自定义协议**：支持用户自定义通信协议

## 🐛 专业调试系统

### SFC-蓝图联合调试器
参考Beremiz调试界面设计，实现专业级调试功能：

#### 核心调试功能
```csharp
public interface ISFCBlueprintDebugger
{
    // 断点管理
    Task SetBreakpointAsync(string elementId, BreakpointType type);
    Task RemoveBreakpointAsync(string breakpointId);
    Task<List<Breakpoint>> GetBreakpointsAsync();
    
    // 执行控制
    Task StartDebuggingAsync(SFCModel model);
    Task StopDebuggingAsync();
    Task StepOverAsync();
    Task StepIntoAsync();
    Task ContinueAsync();
    
    // 状态监控
    Task<List<VariableInfo>> GetVariablesAsync();
    Task<ExecutionStack> GetExecutionStackAsync();
    Task<List<ActiveElement>> GetActiveElementsAsync();
    
    // 历史回放
    Task<ExecutionHistory> GetExecutionHistoryAsync();
    Task ReplayToPointAsync(DateTime timestamp);
}
```

#### 可视化集成
- **能流显示**：实时高亮活动的SFC步骤和蓝图节点
- **数据流追踪**：变量值变化的可视化显示
- **调用栈可视化**：SFC-蓝图嵌套调用的图形化显示
- **性能热力图**：执行频率和耗时的热力图显示

## 📊 性能优化策略

### JIT性能优化
保留第一版设计的JIT优化机制，结合SFC-蓝图融合：

#### 热点识别机制
```csharp
public class HotspotAnalyzer
{
    public void AnalyzeExecution(ExecutionTrace trace)
    {
        // 识别频繁执行的SFC路径
        var hotSFCPaths = IdentifyHotSFCPaths(trace);
        
        // 识别频繁调用的蓝图
        var hotBlueprints = IdentifyHotBlueprints(trace);
        
        // 建议JIT编译候选
        SuggestJITCandidates(hotSFCPaths, hotBlueprints);
    }
}
```

#### 智能模式切换
- **解释器模式**：开发调试时使用，灵活性最高
- **JIT模式**：热点代码自动编译，平衡性能和灵活性
- **编译器模式**：生产环境使用，性能最高

## 🎯 实施路线图

### Week10-12：基础执行引擎
- 开发SFC-蓝图统一解释器
- 开发SFC-蓝图统一编译器
- 实现基础SFC执行逻辑
- 实现能流可视化系统

### Week13：功能完善
- 完善C#编译器和性能优化
- 移植Beremiz硬件抽象设计
- 移植Beremiz安全监控机制
- 实现高级运行时功能

### Week14-15：高级功能
- 利用C#原生调试能力实现专业调试
- 移植Beremiz调试界面设计思路
- 实现C#编译的蓝图集成
- 实现JIT性能优化

## 💡 关键技术实现细节

### SFC-蓝图变量映射机制
```csharp
public class VariableMappingManager
{
    // SFC变量到蓝图输入的映射
    public void MapSFCToBlueprintInputs(SFCTransitionModel transition, SharedExecutionContext context)
    {
        var callInfo = transition.CallInfo;
        foreach (var mapping in callInfo.InputMappings)
        {
            if (context.SFCVariables.TryGetValue(mapping.Value, out var value))
            {
                // 类型转换和验证
                var convertedValue = ConvertAndValidateType(value, mapping.TargetType);
                context.BlueprintVariables[mapping.Key] = convertedValue;
            }
        }
    }

    // 蓝图输出到SFC变量的映射
    public void MapBlueprintToSFCOutputs(SFCTransitionModel transition, SharedExecutionContext context)
    {
        var callInfo = transition.CallInfo;
        foreach (var mapping in callInfo.OutputMappings)
        {
            if (context.BlueprintVariables.TryGetValue(mapping.Key, out var value))
            {
                context.SFCVariables[mapping.Value] = value;
            }
        }
    }
}
```

### 编译代码生成示例
```csharp
public class SFCCodeGenerator
{
    public string GenerateSFCMainMethod(SFCModel sfcModel)
    {
        var code = new StringBuilder();
        code.AppendLine("public async Task ExecuteSFC()");
        code.AppendLine("{");
        code.AppendLine("    var currentSteps = new HashSet<string> { \"" + sfcModel.InitialStep.Id + "\" };");
        code.AppendLine("    ");
        code.AppendLine("    while (currentSteps.Count > 0)");
        code.AppendLine("    {");

        // 为每个步骤生成执行代码
        foreach (var step in sfcModel.Steps)
        {
            code.AppendLine($"        if (currentSteps.Contains(\"{step.Id}\"))");
            code.AppendLine("        {");

            // 执行步骤动作蓝图
            foreach (var actionBlueprintId in step.ActionBlueprintIds)
            {
                var blueprint = GetBlueprint(actionBlueprintId);
                code.AppendLine($"            await {GetMethodName(blueprint)}();");
            }

            // 检查转换条件蓝图
            foreach (var transition in step.GetOutgoingTransitions())
            {
                var conditionBlueprint = GetBlueprint(transition.ConditionBlueprintId);
                code.AppendLine($"            if (await {GetMethodName(conditionBlueprint)}())");
                code.AppendLine("            {");
                code.AppendLine($"                currentSteps.Remove(\"{step.Id}\");");
                code.AppendLine($"                currentSteps.Add(\"{transition.TargetStep.Id}\");");
                code.AppendLine("            }");
            }

            code.AppendLine("        }");
        }

        code.AppendLine("    }");
        code.AppendLine("}");

        return code.ToString();
    }
}
```

### 异常处理和恢复机制
```csharp
public class SFCBlueprintExceptionHandler
{
    public async Task<bool> HandleBlueprintExceptionAsync(
        string blueprintId,
        Exception exception,
        SharedExecutionContext context)
    {
        var errorInfo = new BlueprintExecutionError
        {
            BlueprintId = blueprintId,
            Exception = exception,
            Context = context.Clone(),
            Timestamp = DateTime.UtcNow
        };

        // 记录错误
        LogError(errorInfo);

        // 尝试恢复策略
        var recoveryStrategy = DetermineRecoveryStrategy(errorInfo);

        switch (recoveryStrategy)
        {
            case RecoveryStrategy.Retry:
                return await RetryBlueprintExecution(blueprintId, context);

            case RecoveryStrategy.UseDefaultValue:
                SetDefaultReturnValue(blueprintId, context);
                return true;

            case RecoveryStrategy.SkipTransition:
                // 跳过此转换，继续执行
                return false;

            case RecoveryStrategy.StopExecution:
                // 停止整个SFC执行
                throw new SFCExecutionException("蓝图执行失败，停止SFC执行", exception);

            default:
                return false;
        }
    }
}
```

## 🔍 性能监控和分析

### 执行性能分析器
```csharp
public class SFCBlueprintPerformanceAnalyzer
{
    private readonly Dictionary<string, PerformanceMetrics> _blueprintMetrics;
    private readonly Dictionary<string, PerformanceMetrics> _sfcStepMetrics;

    public void StartProfiling(string elementId, ElementType type)
    {
        var metrics = GetOrCreateMetrics(elementId, type);
        metrics.StartTiming();
    }

    public void EndProfiling(string elementId, ElementType type)
    {
        var metrics = GetOrCreateMetrics(elementId, type);
        metrics.EndTiming();

        // 检查是否需要优化
        if (metrics.AverageExecutionTime > GetThreshold(type))
        {
            SuggestOptimization(elementId, type, metrics);
        }
    }

    private void SuggestOptimization(string elementId, ElementType type, PerformanceMetrics metrics)
    {
        var suggestions = new List<string>();

        if (type == ElementType.Blueprint &&
            metrics.ExecutionCount > 1000 &&
            metrics.AverageExecutionTime > TimeSpan.FromMilliseconds(10))
        {
            suggestions.Add("考虑将此蓝图编译为内联代码");
        }

        if (metrics.MemoryUsage > 10 * 1024 * 1024) // 10MB
        {
            suggestions.Add("检查是否存在内存泄漏");
        }

        // 通知开发者
        OnOptimizationSuggested?.Invoke(elementId, suggestions);
    }
}
```

### 内存管理优化
```csharp
public class ExecutionMemoryManager
{
    private readonly ObjectPool<ExecutionContext> _contextPool;
    private readonly ObjectPool<VariableTable> _variablePool;

    public ExecutionContext GetExecutionContext()
    {
        var context = _contextPool.Get();
        context.Reset();
        return context;
    }

    public void ReturnExecutionContext(ExecutionContext context)
    {
        context.Clear();
        _contextPool.Return(context);
    }

    // 内存使用监控
    public MemoryUsageInfo GetMemoryUsage()
    {
        return new MemoryUsageInfo
        {
            TotalMemory = GC.GetTotalMemory(false),
            Gen0Collections = GC.CollectionCount(0),
            Gen1Collections = GC.CollectionCount(1),
            Gen2Collections = GC.CollectionCount(2),
            PooledContexts = _contextPool.Count,
            PooledVariables = _variablePool.Count
        };
    }
}
```



```
## 🎯 成功指标

### 技术指标
- **编译性能**：SFC-蓝图联合编译时间 < 5秒（1000个元素）
- **运行性能**：转换条件评估时间 < 1ms
- **内存使用**：运行时内存占用 < 200MB
- **稳定性**：连续运行24小时无异常

### 用户体验指标
- **学习时间**：新用户掌握基本操作 < 2小时
- **开发效率**：复杂逻辑开发时间减少 50%
- **错误率**：逻辑错误发生率 < 5%
- **满意度**：用户满意度 > 90%

### 功能完整性指标
- **IEC标准符合性**：100%符合IEC 61131-3标准
- **功能覆盖率**：覆盖所有主要SFC功能
- **集成度**：SFC-蓝图无缝集成
- **扩展性**：支持未来功能扩展

这个设计指导书整合了原有的运行时引擎设计思路和最新的SFC-蓝图深度融合架构，提供了完整的技术实施方案，包括详细的代码示例、性能优化策略、测试方法和最佳实践指南。
