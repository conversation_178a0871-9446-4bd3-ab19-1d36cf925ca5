using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.ViewModels;

namespace PC_Control2.Demo.Controls
{
    /// <summary>
    /// SFC顺控器终止控件 - 专用于显示SFC流程的终止元素
    /// </summary>
    public partial class SFCTerminatorView : UserControl
    {
        #region 私有字段

        private SFCStepViewModel? _viewModel;

        // 拖拽相关
        private bool _isDragging = false;
        private Point _dragStartPoint;

        #endregion

        #region 属性

        /// <summary>
        /// ViewModel属性
        /// </summary>
        public SFCStepViewModel? ViewModel => _viewModel;

        #endregion

        #region 构造函数

        public SFCTerminatorView()
        {
            InitializeComponent();

            // 设置数据上下文变化事件
            DataContextChanged += OnDataContextChanged;

            // 设置鼠标事件
            MouseLeftButtonDown += OnMouseLeftButtonDown;
            MouseLeftButtonUp += OnMouseLeftButtonUp;
            MouseMove += OnMouseMove;
            MouseEnter += OnMouseEnter;
            MouseLeave += OnMouseLeave;
            MouseRightButtonDown += OnMouseRightButtonDown;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 数据上下文变化处理
        /// </summary>
        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.NewValue is SFCStepViewModel stepViewModel)
            {
                _viewModel = stepViewModel;

                // 绑定选中状态变化事件
                stepViewModel.PropertyChanged += (s, args) =>
                {
                    if (args.PropertyName == nameof(SFCStepViewModel.IsSelected))
                    {
                        UpdateVisualState();
                    }
                };

                // 初始化连接点
                InitializeConnectPoints();
            }
        }

        /// <summary>
        /// 鼠标左键按下处理
        /// </summary>
        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_viewModel == null) return;

            // 选中顺控器终止元素
            _viewModel.SelectCommand?.Execute(null);

            // 开始拖拽
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            this.CaptureMouse();

            // 不设置e.Handled = true，让事件继续冒泡到SFCCanvas
        }

        /// <summary>
        /// 鼠标进入处理
        /// </summary>
        private void OnMouseEnter(object sender, MouseEventArgs e)
        {
            // 鼠标悬停效果由样式触发器处理
            Cursor = Cursors.Hand;
        }

        /// <summary>
        /// 鼠标离开处理
        /// </summary>
        private void OnMouseLeave(object sender, MouseEventArgs e)
        {
            Cursor = Cursors.Arrow;
        }

        /// <summary>
        /// 鼠标左键释放处理
        /// </summary>
        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                this.ReleaseMouseCapture();
                e.Handled = true;
            }
        }

        /// <summary>
        /// 鼠标移动处理
        /// </summary>
        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (_isDragging && _viewModel != null && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPosition = e.GetPosition(this);
                var deltaX = currentPosition.X - _dragStartPoint.X;
                var deltaY = currentPosition.Y - _dragStartPoint.Y;

                // 更新顺控器终止元素位置
                var newPosition = new Point(
                    _viewModel.Position.X + deltaX,
                    _viewModel.Position.Y + deltaY);

                System.Diagnostics.Debug.WriteLine($"[SFCTerminatorView] 拖动更新位置: {_viewModel.Id} -> {newPosition}");
                _viewModel.Position = newPosition;
                e.Handled = true;
            }
        }

        /// <summary>
        /// 鼠标右键按下处理 - 阻止事件冒泡到SFCCanvas，确保使用静态菜单
        /// </summary>
        private void OnMouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_viewModel == null) return;

            // 选中终止元素
            _viewModel.SelectCommand?.Execute(null);

            // 阻止事件冒泡到SFCCanvas，这样就会使用SFCTerminatorView.xaml中定义的静态菜单
            e.Handled = true;

            System.Diagnostics.Debug.WriteLine($"[SFCTerminatorView] 右键点击终止元素: {_viewModel.Id}，已阻止事件冒泡，将使用静态菜单");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新视觉状态
        /// </summary>
        private void UpdateVisualState()
        {
            if (DataContext is SFCStepViewModel stepViewModel)
            {
                // 选中状态的视觉效果由XAML中的DataTrigger处理
                // 这里可以添加额外的状态更新逻辑
            }
        }

        /// <summary>
        /// 初始化连接点 - 绑定连接点适配器，确保连接状态正确显示
        /// </summary>
        private void InitializeConnectPoints()
        {
            if (_viewModel != null && TopConnectPoint != null)
            {
                // 设置连接点的基本属性
                TopConnectPoint.ElementId = _viewModel.Id;
                TopConnectPoint.ElementType = Models.SFCElementType.Step;
                TopConnectPoint.PointType = ConnectPointType.Input;
                TopConnectPoint.Index = 0;

                // 🔧 关键修复：绑定连接点适配器，确保连接状态正确显示
                if (_viewModel.ConnectPointAdapters != null && _viewModel.ConnectPointAdapters.Count > 0)
                {
                    // 终止元素只有输入连接点，绑定第一个适配器（输入适配器）
                    var inputAdapter = _viewModel.ConnectPointAdapters.FirstOrDefault(a => a.Direction == ConnectPointDirection.Input);
                    if (inputAdapter != null)
                    {
                        TopConnectPoint.Adapter = inputAdapter;
                        System.Diagnostics.Debug.WriteLine($"[SFCTerminatorView] ✅ 已绑定输入连接点适配器: {_viewModel.Id}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[SFCTerminatorView] ❌ 警告：终止元素 {_viewModel.Id} 缺少输入连接点适配器");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[SFCTerminatorView] ❌ 警告：终止元素 {_viewModel.Id} 缺少连接点适配器");
                }
            }
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取顶部连接点的位置
        /// </summary>
        public Point GetTopConnectPointPosition()
        {
            // 返回顶部连接点的中心位置
            return new Point(25, 10); // Canvas.Left="20" + Width/2, Canvas.Top="5" + Height/2
        }

        /// <summary>
        /// 获取元素的边界矩形
        /// </summary>
        public Rect GetBounds()
        {
            return new Rect(0, 0, Width, Height);
        }

        #endregion
    }
}
